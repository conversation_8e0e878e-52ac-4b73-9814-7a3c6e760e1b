#!/usr/bin/env ruby

require 'sqlite3'
require 'json'
require 'digest'
require 'pathname'

class CodebaseBackend
  def initialize(db_path = 'codebase.db')
    @db = SQLite3::Database.new(db_path)
    @db.results_as_hash = true
    setup_database
  end

  def setup_database
    @db.execute <<-SQL
      CREATE TABLE IF NOT EXISTS repositories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        url TEXT NOT NULL,
        description TEXT,
        status INTEGER DEFAULT 0,
        active BOOLEAN DEFAULT 1,
        last_scan_at DATETIME,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    SQL

    @db.execute <<-SQL
      CREATE TABLE IF NOT EXISTS directories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        repository_id INTEGER NOT NULL,
        parent_id INTEGER,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        relative_path TEXT NOT NULL,
        depth INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (repository_id) REFERENCES repositories (id),
        FOREIGN KEY (parent_id) REFERENCES directories (id)
      )
    SQL

    @db.execute <<-SQL
      CREATE TABLE IF NOT EXISTS files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        repository_id INTEGER NOT NULL,
        directory_id INTEGER,
        name TEXT NOT NULL,
        path TEXT NOT NULL,
        relative_path TEXT NOT NULL,
        extension TEXT,
        language TEXT,
        content TEXT,
        size_bytes INTEGER DEFAULT 0,
        lines_count INTEGER DEFAULT 0,
        is_binary BOOLEAN DEFAULT 0,
        encoding TEXT,
        sha256_hash TEXT,
        last_modified_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (repository_id) REFERENCES repositories (id),
        FOREIGN KEY (directory_id) REFERENCES directories (id)
      )
    SQL

    # Create indexes
    @db.execute "CREATE INDEX IF NOT EXISTS idx_repositories_path ON repositories (path)"
    @db.execute "CREATE INDEX IF NOT EXISTS idx_directories_repo_path ON directories (repository_id, relative_path)"
    @db.execute "CREATE INDEX IF NOT EXISTS idx_files_repo_path ON files (repository_id, relative_path)"
    @db.execute "CREATE INDEX IF NOT EXISTS idx_files_extension ON files (extension)"
    @db.execute "CREATE INDEX IF NOT EXISTS idx_files_language ON files (language)"
    @db.execute "CREATE INDEX IF NOT EXISTS idx_files_content ON files (content)"
  end

  def scan_repository(repo_path, repo_name = nil, repo_url = nil)
    repo_name ||= File.basename(repo_path)
    repo_url ||= get_git_remote_url(repo_path) || repo_path

    # Insert or update repository
    repo_id = @db.execute(
      "INSERT OR REPLACE INTO repositories (name, path, url, status, last_scan_at) VALUES (?, ?, ?, 1, datetime('now'))",
      [repo_name, repo_path, repo_url]
    )
    repo_id = @db.last_insert_row_id

    # Clear existing data
    @db.execute("DELETE FROM files WHERE repository_id = ?", [repo_id])
    @db.execute("DELETE FROM directories WHERE repository_id = ?", [repo_id])

    puts "Scanning repository: #{repo_name}"
    puts "  Path: #{repo_path}"
    puts "  URL: #{repo_url}"
    scan_directory_structure(repo_id, repo_path)

    # Update status to completed
    @db.execute("UPDATE repositories SET status = 2, last_scan_at = datetime('now') WHERE id = ?", [repo_id])

    puts "Scan completed for repository: #{repo_name}"
    repo_id
  end

  def get_git_remote_url(repo_path)
    return nil unless File.directory?(File.join(repo_path, '.git'))

    begin
      Dir.chdir(repo_path) do
        output = `git remote -v 2>/dev/null`
        return nil if $?.exitstatus != 0

        # Parse git remote output
        lines = output.split("\n")
        origin_line = lines.find { |line| line.start_with?('origin') && line.include?('(fetch)') }

        if origin_line
          # Extract URL from "origin <url> (fetch)"
          url = origin_line.split[1]
          return url
        end
      end
    rescue => e
      puts "Warning: Failed to get git remote URL: #{e.message}"
    end

    nil
  end

  def get_current_repository_info
    current_path = Dir.pwd
    git_url = get_git_remote_url(current_path)

    if git_url
      # Extract repository name from URL
      repo_name = File.basename(git_url, '.git')
      repo_name = File.basename(current_path) if repo_name.empty?
    else
      repo_name = File.basename(current_path)
    end

    {
      name: repo_name,
      path: current_path,
      url: git_url || current_path
    }
  end

  def scan_directory_structure(repo_id, base_path)
    base_pathname = Pathname.new(base_path)
    directory_cache = {}

    Dir.glob(File.join(base_path, '**', '*'), File::FNM_DOTMATCH).each do |full_path|
      next if should_skip_path?(full_path)

      path_obj = Pathname.new(full_path)
      relative_path = path_obj.relative_path_from(base_pathname).to_s

      if File.directory?(full_path)
        create_directory(repo_id, full_path, relative_path, directory_cache)
      else
        create_file(repo_id, full_path, relative_path, directory_cache)
      end
    end
  end

  def create_directory(repo_id, full_path, relative_path, directory_cache)
    return directory_cache[relative_path] if directory_cache[relative_path]

    parent_dir_id = nil
    parent_path = File.dirname(relative_path)
    
    if parent_path != '.' && parent_path != relative_path
      parent_dir_id = create_directory_recursive(repo_id, parent_path, directory_cache)
    end

    @db.execute(
      "INSERT INTO directories (repository_id, parent_id, name, path, relative_path, depth) VALUES (?, ?, ?, ?, ?, ?)",
      [repo_id, parent_dir_id, File.basename(full_path), full_path, relative_path, parent_path.split('/').length]
    )
    
    dir_id = @db.last_insert_row_id
    directory_cache[relative_path] = dir_id
    dir_id
  end

  def create_directory_recursive(repo_id, relative_path, directory_cache)
    return directory_cache[relative_path] if directory_cache[relative_path]

    parent_dir_id = nil
    parent_path = File.dirname(relative_path)
    
    if parent_path != '.' && parent_path != relative_path
      parent_dir_id = create_directory_recursive(repo_id, parent_path, directory_cache)
    end

    @db.execute(
      "INSERT INTO directories (repository_id, parent_id, name, path, relative_path, depth) VALUES (?, ?, ?, ?, ?, ?)",
      [repo_id, parent_dir_id, File.basename(relative_path), File.join(@db.execute("SELECT path FROM repositories WHERE id = ?", [repo_id])[0]['path'], relative_path), relative_path, relative_path.split('/').length - 1]
    )
    
    dir_id = @db.last_insert_row_id
    directory_cache[relative_path] = dir_id
    dir_id
  end

  def create_file(repo_id, full_path, relative_path, directory_cache)
    # Get or create parent directory
    parent_dir_id = nil
    dir_path = File.dirname(relative_path)
    
    if dir_path != '.'
      parent_dir_id = directory_cache[dir_path] || create_directory_recursive(repo_id, dir_path, directory_cache)
    end

    # Read file content and metadata
    file_stats = File.stat(full_path)
    is_binary = binary_file?(full_path)
    content = is_binary ? nil : read_file_content(full_path)
    ext = File.extname(full_path)
    extension = ext[1..-1] if ext && !ext.empty?
    language = detect_language(extension)
    
    @db.execute(
      "INSERT INTO files (repository_id, directory_id, name, path, relative_path, extension, language, content, size_bytes, lines_count, is_binary, encoding, sha256_hash, last_modified_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
      [
        repo_id, parent_dir_id, File.basename(full_path), full_path, relative_path,
        extension, language, content, file_stats.size,
        content ? content.lines.count : 0, is_binary ? 1 : 0,
        content ? content.encoding.name : nil,
        Digest::SHA256.file(full_path).hexdigest,
        file_stats.mtime.strftime('%Y-%m-%d %H:%M:%S')
      ]
    )
  end

  def should_skip_path?(path)
    basename = File.basename(path)
    
    # Skip hidden files and directories (except .git for git repos)
    return true if basename.start_with?('.') && basename != '.git'
    
    # Skip common build/cache directories
    skip_dirs = %w[node_modules .git vendor tmp log coverage dist build .next .nuxt]
    return true if skip_dirs.any? { |dir| path.include?("/#{dir}/") || path.end_with?("/#{dir}") }
    
    false
  end

  def binary_file?(file_path)
    # Simple binary detection - check for null bytes in first 1KB
    File.open(file_path, 'rb') do |file|
      chunk = file.read(1024)
      return false if chunk.nil? || chunk.empty?
      chunk.include?("\x00")
    end
  rescue
    true # If we can't read it, assume it's binary
  end

  def read_file_content(file_path)
    # Limit file size to prevent memory issues
    max_size = 1024 * 1024 # 1MB
    return nil if File.size(file_path) > max_size

    File.read(file_path, encoding: 'UTF-8')
  rescue Encoding::InvalidByteSequenceError, Encoding::UndefinedConversionError
    # Try reading as binary and force UTF-8
    content = File.read(file_path, encoding: 'BINARY')
    content.force_encoding('UTF-8')
    content.valid_encoding? ? content : nil
  rescue => e
    puts "Failed to read file #{file_path}: #{e.message}"
    nil
  end

  def detect_language(extension)
    return nil if extension.nil? || extension.empty?
    
    language_map = {
      'rb' => 'Ruby', 'py' => 'Python', 'js' => 'JavaScript', 'ts' => 'TypeScript',
      'jsx' => 'JavaScript', 'tsx' => 'TypeScript', 'java' => 'Java', 'c' => 'C',
      'cpp' => 'C++', 'cc' => 'C++', 'cxx' => 'C++', 'h' => 'C', 'hpp' => 'C++',
      'cs' => 'C#', 'php' => 'PHP', 'go' => 'Go', 'rs' => 'Rust', 'swift' => 'Swift',
      'kt' => 'Kotlin', 'scala' => 'Scala', 'html' => 'HTML', 'css' => 'CSS',
      'json' => 'JSON', 'yaml' => 'YAML', 'yml' => 'YAML', 'md' => 'Markdown',
      'txt' => 'Text', 'sh' => 'Shell', 'sql' => 'SQL'
    }
    
    language_map[extension.downcase]
  end

  # API methods
  def search_files(query, repo_id = nil)
    sql = "SELECT * FROM files WHERE content LIKE ? OR name LIKE ? OR relative_path LIKE ?"
    params = ["%#{query}%", "%#{query}%", "%#{query}%"]
    
    if repo_id
      sql += " AND repository_id = ?"
      params << repo_id
    end
    
    sql += " LIMIT 100"
    
    @db.execute(sql, params)
  end

  def get_repositories
    @db.execute("SELECT * FROM repositories WHERE active = 1")
  end

  def get_repository_files(repo_id, limit = 100, offset = 0)
    @db.execute(
      "SELECT * FROM files WHERE repository_id = ? LIMIT ? OFFSET ?",
      [repo_id, limit, offset]
    )
  end

  def get_file_content(file_id)
    result = @db.execute("SELECT content FROM files WHERE id = ?", [file_id])
    result.first&.[]('content')
  end

  def close
    @db.close
  end
end

# CLI interface for testing
class CLI
  def initialize(backend)
    @backend = backend
  end

  def run
    puts "CodeBase Intelligence Backend CLI"
    puts "Available commands:"
    puts "  scan <path> [name] - Scan a repository"
    puts "  search <query> - Search files"
    puts "  repos - List repositories"
    puts "  files <repo_id> - List files in repository"
    puts "  exit - Exit"

    loop do
      print "> "
      input = gets.chomp.split
      command = input[0]

      case command
      when 'scan'
        path = input[1]
        name = input[2]
        if path && File.directory?(path)
          repo_id = @backend.scan_repository(path, name)
          puts "Repository scanned with ID: #{repo_id}"
        else
          puts "Invalid path: #{path}"
        end
      when 'search'
        query = input[1..-1].join(' ')
        results = @backend.search_files(query)
        puts "Found #{results.length} results:"
        results.each do |file|
          puts "  #{file['relative_path']} (#{file['language']})"
        end
      when 'repos'
        repos = @backend.get_repositories
        puts "Repositories:"
        repos.each do |repo|
          puts "  #{repo['id']}: #{repo['name']} (#{repo['path']})"
        end
      when 'files'
        repo_id = input[1]&.to_i
        if repo_id
          files = @backend.get_repository_files(repo_id, 20)
          puts "Files in repository #{repo_id}:"
          files.each do |file|
            puts "  #{file['relative_path']} (#{file['size_bytes']} bytes)"
          end
        else
          puts "Please provide repository ID"
        end
      when 'exit'
        break
      else
        puts "Unknown command: #{command}"
      end
    end
  end
end

# Main execution
if __FILE__ == $0
  backend = CodebaseBackend.new

  # Scan current directory if provided as argument
  if ARGV.length > 0
    repo_path = ARGV[0]
    if File.directory?(repo_path)
      backend.scan_repository(repo_path)
      puts "Repository scanned successfully!"
    else
      puts "Directory not found: #{repo_path}"
    end
  else
    # Start CLI
    cli = CLI.new(backend)
    cli.run
  end

  backend.close
end
