#!/usr/bin/env ruby

require 'socket'
require 'json'
require 'uri'
require 'cgi'
require_relative 'simple_backend'

class SimpleHTTPServer
  def initialize(backend, port = 3001)
    @backend = backend
    @port = port
  end

  def start
    server = TCPServer.new(@port)
    puts "Backend server starting on http://localhost:#{@port}"
    puts "Available endpoints:"
    puts "  GET  /api/health"
    puts "  GET  /api/repositories"
    puts "  POST /api/repositories"
    puts "  GET  /api/repositories/:id/files"
    puts "  GET  /api/files/:id/content"
    puts "  GET  /api/search?q=query"
    puts ""
    puts "Press Ctrl+C to stop"

    trap('INT') do
      puts "\nShutting down server..."
      server.close
      @backend.close
      exit
    end

    loop do
      client = server.accept
      handle_request(client)
      client.close
    end
  end

  private

  def handle_request(client)
    request_line = client.gets
    return unless request_line

    method, path, version = request_line.split
    headers = {}
    
    # Read headers
    while (line = client.gets.chomp) != ""
      key, value = line.split(': ', 2)
      headers[key.downcase] = value
    end

    # Read body if present
    body = ""
    if headers['content-length']
      body = client.read(headers['content-length'].to_i)
    end

    # Parse URL and query parameters
    uri = URI.parse(path)
    query_params = CGI.parse(uri.query || "")

    # Route the request
    response = route_request(method, uri.path, query_params, body)

    # Send response
    send_response(client, response)
  rescue => e
    puts "Error handling request: #{e.message}"
    send_error_response(client, 500, "Internal Server Error")
  end

  def route_request(method, path, query_params, body)
    # CORS headers
    cors_headers = {
      'Access-Control-Allow-Origin' => '*',
      'Access-Control-Allow-Methods' => 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers' => 'Content-Type, Authorization'
    }

    if method == 'OPTIONS'
      return { status: 200, headers: cors_headers, body: '' }
    end

    case [method, path]
    when ['GET', '/api/health']
      {
        status: 200,
        headers: cors_headers.merge('Content-Type' => 'application/json'),
        body: { success: true, message: 'Backend is running' }.to_json
      }

    when ['GET', '/api/repositories']
      repos = @backend.get_repositories
      {
        status: 200,
        headers: cors_headers.merge('Content-Type' => 'application/json'),
        body: { success: true, data: repos }.to_json
      }

    when ['POST', '/api/repositories']
      data = JSON.parse(body)
      repo_id = @backend.scan_repository(data['path'], data['name'])
      {
        status: 201,
        headers: cors_headers.merge('Content-Type' => 'application/json'),
        body: { success: true, data: { id: repo_id } }.to_json
      }

    when ['GET', '/api/search']
      query = query_params['q']&.first
      repo_id = query_params['repo_id']&.first&.to_i
      
      if query
        results = @backend.search_files(query, repo_id)
        {
          status: 200,
          headers: cors_headers.merge('Content-Type' => 'application/json'),
          body: { success: true, data: results, query: query }.to_json
        }
      else
        {
          status: 400,
          headers: cors_headers.merge('Content-Type' => 'application/json'),
          body: { success: false, error: 'Query parameter q is required' }.to_json
        }
      end

    else
      # Check for dynamic routes
      if path =~ /^\/api\/repositories\/(\d+)\/files$/
        repo_id = $1.to_i
        files = @backend.get_repository_files(repo_id)
        {
          status: 200,
          headers: cors_headers.merge('Content-Type' => 'application/json'),
          body: { success: true, data: files }.to_json
        }
      elsif path =~ /^\/api\/files\/(\d+)\/content$/
        file_id = $1.to_i
        content = @backend.get_file_content(file_id)
        {
          status: 200,
          headers: cors_headers.merge('Content-Type' => 'application/json'),
          body: { success: true, data: { content: content } }.to_json
        }
      else
        {
          status: 404,
          headers: cors_headers.merge('Content-Type' => 'application/json'),
          body: { success: false, error: 'Not found' }.to_json
        }
      end
    end
  rescue JSON::ParserError
    {
      status: 400,
      headers: { 'Content-Type' => 'application/json' },
      body: { success: false, error: 'Invalid JSON' }.to_json
    }
  rescue => e
    {
      status: 500,
      headers: { 'Content-Type' => 'application/json' },
      body: { success: false, error: e.message }.to_json
    }
  end

  def send_response(client, response)
    client.print "HTTP/1.1 #{response[:status]} #{status_text(response[:status])}\r\n"
    
    response[:headers].each do |key, value|
      client.print "#{key}: #{value}\r\n"
    end
    
    client.print "\r\n"
    client.print response[:body]
  end

  def send_error_response(client, status, message)
    response = {
      status: status,
      headers: { 'Content-Type' => 'application/json' },
      body: { success: false, error: message }.to_json
    }
    send_response(client, response)
  end

  def status_text(code)
    case code
    when 200 then 'OK'
    when 201 then 'Created'
    when 400 then 'Bad Request'
    when 404 then 'Not Found'
    when 500 then 'Internal Server Error'
    else 'Unknown'
    end
  end
end

# Main execution
if __FILE__ == $0
  backend = CodebaseBackend.new
  server = SimpleHTTPServer.new(backend)
  server.start
end
