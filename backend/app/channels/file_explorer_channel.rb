class FileExplorerChannel < ApplicationCable::Channel
  def subscribed
    repository_id = params[:repository_id]
    
    if repository_id.present?
      repository = Repository.find(repository_id)
      stream_from "file_explorer_#{repository_id}"
      
      Rails.logger.info "FileExplorerChannel: User subscribed to repository #{repository_id}"
    else
      reject
    end
  rescue ActiveRecord::RecordNotFound
    reject
  end

  def unsubscribed
    Rails.logger.info "FileExplorerChannel: User unsubscribed"
  end

  # Handle file content updates from client
  def update_file(data)
    repository_id = params[:repository_id]
    file_id = data['file_id']
    content = data['content']
    
    repository = Repository.find(repository_id)
    file = repository.files.find(file_id)
    
    if file.is_binary?
      transmit({ error: 'Cannot edit binary files' })
      return
    end

    begin
      # Write to actual file on disk
      File.write(file.path, content)
      
      # Update database record
      file.update!(
        content: content,
        lines_count: content.lines.count,
        size_bytes: content.bytesize,
        last_modified_at: Time.current,
        sha256_hash: Digest::SHA256.hexdigest(content)
      )

      # Broadcast update to all subscribers
      ActionCable.server.broadcast(
        "file_explorer_#{repository_id}",
        {
          type: 'file_updated',
          file: {
            id: file.id,
            name: file.name,
            path: file.relative_path,
            lines: file.lines_count,
            size: file.size_bytes,
            last_modified: file.last_modified_at
          }
        }
      )

      transmit({ success: true, message: 'File updated successfully' })
    rescue => e
      Rails.logger.error "Failed to update file via WebSocket: #{e.message}"
      transmit({ error: "Failed to update file: #{e.message}" })
    end
  rescue ActiveRecord::RecordNotFound => e
    transmit({ error: 'File or repository not found' })
  end

  # Handle file tree refresh requests
  def refresh_tree(data)
    repository_id = params[:repository_id]
    
    begin
      repository = Repository.find(repository_id)
      
      # Trigger repository rescan
      repository.scan!
      
      transmit({ 
        success: true, 
        message: 'Repository scan started',
        status: 'scanning'
      })
    rescue ActiveRecord::RecordNotFound
      transmit({ error: 'Repository not found' })
    rescue => e
      Rails.logger.error "Failed to refresh tree: #{e.message}"
      transmit({ error: "Failed to refresh tree: #{e.message}" })
    end
  end
end
