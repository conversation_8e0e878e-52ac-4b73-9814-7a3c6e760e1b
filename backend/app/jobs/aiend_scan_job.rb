class AiendScanJob < ApplicationJob
  queue_as :default
  
  def perform(repository_id, force_reindex: false)
    Rails.logger.info "Processing Aiend scan job for repository: #{repository_id}"
    
    begin
      repository = Repository.find(repository_id)
      
      # Aiend에 저장소 스캔 요청
      result = AiendWebhookService.trigger_repository_scan(
        repository,
        force_reindex: force_reindex
      )
      
      if result
        Rails.logger.info "Aiend repository scan triggered successfully for: #{repository.name}"
        
        # 성공한 경우 repository 업데이트
        repository.update!(
          last_aiend_scan_at: Time.current,
          aiend_scan_status: 'triggered'
        )
        
        # 작업 ID가 있는 경우 저장
        if result['task_id']
          repository.update!(aiend_task_id: result['task_id'])
          
          # 백그라운드에서 작업 상태 모니터링
          AiendTaskMonitorJob.perform_later(repository_id, result['task_id'])
        end
      else
        Rails.logger.error "Failed to trigger Aiend repository scan for: #{repository.name}"
        
        repository.update!(
          aiend_scan_status: 'failed',
          aiend_scan_error: 'Scan trigger failed'
        )
      end
      
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Repository not found for Aiend scan: #{repository_id}"
    rescue StandardError => e
      Rails.logger.error "Aiend scan job failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      # 에러 상태 업데이트
      begin
        repository = Repository.find(repository_id)
        repository.update!(
          aiend_scan_status: 'failed',
          aiend_scan_error: e.message
        )
      rescue ActiveRecord::RecordNotFound
        # Repository가 삭제된 경우 무시
      end
    end
  end
end
