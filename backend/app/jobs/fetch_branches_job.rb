require 'open3'

class FetchBranchesJob < ApplicationJob
  queue_as :default

  def perform(repository)
    Rails.logger.info "Fetching branches for repository: #{repository.id}"
    
    begin
      local_path = repository.local_path
      
      unless File.exist?(local_path)
        raise "Repository not found at: #{local_path}"
      end
      
      Dir.chdir(local_path) do
        # Fetch all remote branches
        stdout, stderr, status = Open3.capture3("git", "fetch", "origin")
        unless status.success?
          Rails.logger.warn "Git fetch failed: #{stderr}"
        end
        
        # Get all remote branches
        stdout, stderr, status = Open3.capture3("git", "branch", "-r")
        
        if status.success?
          remote_branches = stdout.lines
            .map(&:strip)
            .reject { |line| line.include?('->') } # Skip symbolic refs
            .map { |line| line.gsub(/^origin\//, '') } # Remove origin/ prefix
            .uniq
          
          # Get current branch
          stdout, stderr, status = Open3.capture3("git", "branch", "--show-current")
          current_branch = status.success? ? stdout.strip : repository.current_branch
          
          repository.update!(
            branches: remote_branches,
            current_branch: current_branch
          )
          
          Rails.logger.info "Updated branches: #{remote_branches.join(', ')}"
        else
          raise "Failed to fetch branches: #{stderr}"
        end
      end
      
    rescue => e
      Rails.logger.error "Failed to fetch branches for repository #{repository.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end
end
