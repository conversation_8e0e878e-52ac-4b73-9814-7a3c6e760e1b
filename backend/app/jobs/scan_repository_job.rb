class ScanRepositoryJob < ApplicationJob
  queue_as :default

  def perform(repository)
    Rails.logger.info "Starting repository scan for: #{repository.name}"
    
    repository.scan_repository!
    
    Rails.logger.info "Completed repository scan for: #{repository.name}"
  rescue => e
    Rails.logger.error "Repository scan failed for #{repository.name}: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    
    repository.update!(
      status: :error,
      error_message: e.message
    )
    
    raise e
  end
end
