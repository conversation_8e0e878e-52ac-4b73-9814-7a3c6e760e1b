require 'open3'
require 'fileutils'

class CloneRepositoryJob < ApplicationJob
  queue_as :default

  def perform(repository)
    Rails.logger.info "Starting clone for repository: #{repository.id} (#{repository.sanitized_repository_name})"

    begin
      # Ensure repositories directory exists
      repositories_root = Rails.root.join('repositories')
      FileUtils.mkdir_p(repositories_root) unless File.exist?(repositories_root)

      # Get local path using the new naming convention
      local_path = repository.local_path

      # Remove existing directory if it exists
      FileUtils.rm_rf(local_path) if File.exist?(local_path)

      # Clone the repository
      git_url = repository.git_url_with_auth

      Rails.logger.info "Cloning from: #{repository.url} to: #{local_path}"
      Rails.logger.info "Repository will be stored as: #{repository.sanitized_repository_name}"
      
      # Use Open3 to capture output and handle errors
      require 'open3'
      
      cmd = ["git", "clone", git_url, local_path.to_s]
      stdout, stderr, status = Open3.capture3(*cmd)
      
      unless status.success?
        raise "Git clone failed: #{stderr}"
      end
      
      Rails.logger.info "Clone successful: #{stdout}"
      
      # Fetch all branches
      fetch_branches(repository, local_path)
      
      # Update repository status
      repository.update!(
        clone_status: 'ready',
        path: local_path.to_s,
        name: repository.repository_name
      )

      # 자동 스캔 제거 - 사용자가 수동으로 선택한 브랜치만 분석
      # repository.scan!

      # Aiend에 클론 완료 알림 (분석 없이 클론 완료만 알림)
      # AiendWebhookService.notify_git_changes_async(repository.id, 'clone')

      Rails.logger.info "Repository clone completed: #{repository.id}"
      
    rescue => e
      Rails.logger.error "Clone failed for repository #{repository.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      repository.update!(
        clone_status: 'error',
        error_message: e.message
      )
      
      # Clean up failed clone
      FileUtils.rm_rf(local_path) if local_path && File.exist?(local_path)
    end
  end

  private

  def fetch_branches(repository, local_path)
    Dir.chdir(local_path) do
      # Fetch all remote branches
      stdout, stderr, status = Open3.capture3("git", "branch", "-r")
      
      if status.success?
        remote_branches = stdout.lines
          .map(&:strip)
          .reject { |line| line.include?('->') } # Skip symbolic refs
          .map { |line| line.gsub(/^origin\//, '') } # Remove origin/ prefix
          .uniq
        
        # Get current branch
        stdout, stderr, status = Open3.capture3("git", "branch", "--show-current")
        current_branch = status.success? ? stdout.strip : 'main'
        
        repository.update!(
          branches: remote_branches,
          current_branch: current_branch
        )
        
        Rails.logger.info "Fetched branches: #{remote_branches.join(', ')}"
      else
        Rails.logger.warn "Failed to fetch branches: #{stderr}"
        repository.update!(
          branches: [repository.current_branch || 'main'],
          current_branch: repository.current_branch || 'main'
        )
      end
    end
  rescue => e
    Rails.logger.error "Failed to fetch branches: #{e.message}"
    repository.update!(
      branches: [repository.current_branch || 'main'],
      current_branch: repository.current_branch || 'main'
    )
  end
end
