require 'open3'

class SwitchBranchJob < ApplicationJob
  queue_as :default

  def perform(repository, branch_name)
    Rails.logger.info "Switching to branch '#{branch_name}' for repository: #{repository.id}"
    
    begin
      local_path = repository.local_path
      
      unless File.exist?(local_path)
        raise "Repository not found at: #{local_path}"
      end
      
      Dir.chdir(local_path) do
        # Fetch latest changes
        stdout, stderr, status = Open3.capture3("git", "fetch", "origin")
        unless status.success?
          Rails.logger.warn "Git fetch failed: #{stderr}"
        end
        
        # Switch to the branch
        stdout, stderr, status = Open3.capture3("git", "checkout", branch_name)
        unless status.success?
          # Try to create and checkout the branch if it doesn't exist locally
          stdout, stderr, status = Open3.capture3("git", "checkout", "-b", branch_name, "origin/#{branch_name}")
          unless status.success?
            raise "Failed to switch to branch '#{branch_name}': #{stderr}"
          end
        end
        
        # Pull latest changes
        stdout, stderr, status = Open3.capture3("git", "pull", "origin", branch_name)
        unless status.success?
          Rails.logger.warn "Git pull failed: #{stderr}"
        end
        
        Rails.logger.info "Successfully switched to branch: #{branch_name}"
      end
      
      # Update repository
      repository.update!(
        current_branch: branch_name,
        last_indexed_at: Time.current
      )

      # 자동 파일 정리 및 스캔 제거 - 사용자가 수동으로 선택한 브랜치만 분석
      # repository.files.destroy_all
      # repository.directories.destroy_all
      # ScanRepositoryJob.perform_later(repository)

      # Aiend에 브랜치 변경 알림 제거 (수동 분석 시에만 알림)
      # AiendWebhookService.notify_git_changes_async(repository.id, 'branch_switch')

      Rails.logger.info "Branch switch completed for repository: #{repository.id}"
      
    rescue => e
      Rails.logger.error "Branch switch failed for repository #{repository.id}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      repository.update!(
        status: 'error',
        error_message: "Failed to switch to branch '#{branch_name}': #{e.message}"
      )
    end
  end
end
