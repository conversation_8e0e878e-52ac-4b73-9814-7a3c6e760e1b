class AiendTaskMonitorJob < ApplicationJob
  queue_as :default
  
  # 최대 모니터링 시간 (30분)
  MAX_MONITOR_TIME = 30.minutes
  # 상태 확인 간격 (30초)
  CHECK_INTERVAL = 30.seconds
  
  def perform(repository_id, task_id, start_time: Time.current)
    Rails.logger.info "Monitoring Aiend task: #{task_id} for repository: #{repository_id}"
    
    begin
      repository = Repository.find(repository_id)
      
      # 최대 모니터링 시간 초과 확인
      if Time.current - start_time > MAX_MONITOR_TIME
        Rails.logger.warn "Aiend task monitoring timeout for task: #{task_id}"
        repository.update!(
          aiend_scan_status: 'timeout',
          aiend_scan_error: 'Task monitoring timeout'
        )
        return
      end
      
      # Aiend에서 작업 상태 조회
      status_result = AiendWebhookService.get_task_status(task_id)
      
      if status_result.nil?
        Rails.logger.error "Failed to get Aiend task status for: #{task_id}"
        
        # 다시 시도 (5분 후)
        AiendTaskMonitorJob.set(wait: 5.minutes).perform_later(
          repository_id, 
          task_id, 
          start_time: start_time
        )
        return
      end
      
      task_status = status_result['status']
      progress = status_result['progress'] || 0
      message = status_result['message'] || ''
      
      Rails.logger.info "Aiend task #{task_id} status: #{task_status} (#{progress}%)"
      
      case task_status
      when 'completed'
        # 작업 완료
        repository.update!(
          aiend_scan_status: 'completed',
          aiend_scan_progress: 100,
          aiend_scan_message: message,
          last_aiend_scan_completed_at: Time.current
        )
        
        Rails.logger.info "Aiend task completed successfully: #{task_id}"
        
      when 'failed'
        # 작업 실패
        repository.update!(
          aiend_scan_status: 'failed',
          aiend_scan_error: message,
          aiend_scan_progress: progress
        )
        
        Rails.logger.error "Aiend task failed: #{task_id} - #{message}"
        
      when 'pending', 'running'
        # 작업 진행 중
        repository.update!(
          aiend_scan_status: task_status,
          aiend_scan_progress: progress,
          aiend_scan_message: message
        )
        
        # 다시 확인 (30초 후)
        AiendTaskMonitorJob.set(wait: CHECK_INTERVAL).perform_later(
          repository_id, 
          task_id, 
          start_time: start_time
        )
        
      else
        # 알 수 없는 상태
        Rails.logger.warn "Unknown Aiend task status: #{task_status} for task: #{task_id}"
        
        repository.update!(
          aiend_scan_status: 'unknown',
          aiend_scan_message: "Unknown status: #{task_status}"
        )
        
        # 다시 확인 (1분 후)
        AiendTaskMonitorJob.set(wait: 1.minute).perform_later(
          repository_id, 
          task_id, 
          start_time: start_time
        )
      end
      
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Repository not found for Aiend task monitoring: #{repository_id}"
    rescue StandardError => e
      Rails.logger.error "Aiend task monitoring failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      # 에러 상태 업데이트
      begin
        repository = Repository.find(repository_id)
        repository.update!(
          aiend_scan_status: 'monitor_failed',
          aiend_scan_error: e.message
        )
      rescue ActiveRecord::RecordNotFound
        # Repository가 삭제된 경우 무시
      end
    end
  end
end
