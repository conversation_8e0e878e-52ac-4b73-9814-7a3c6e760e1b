# app/jobs/codebase_indexing_job.rb
class CodebaseIndexingJob < ApplicationJob
  queue_as :default

  def perform(repository_id, branch)
    repository = Repository.find(repository_id)
    repo_path = repository.local_path

    # 1. 저장소 경로 확인
    unless Dir.exist?(repo_path)
      Rails.logger.error "[CodebaseIndexingJob] Repository path not found: #{repo_path}"
      return
    end

    # 2. 기존 데이터 삭제
    Rails.logger.info "[CodebaseIndexingJob] Cleaning up existing data for repository: #{repository.name}, branch: #{branch}"
    repository.codebase_files.where(branch: branch).destroy_all
    repository.codebase_directories.where(branch: branch).destroy_all
    Rails.logger.info "[CodebaseIndexingJob] Cleanup complete."

    # 3. 브랜치 체크아웃
    Rails.logger.info "[CodebaseIndexingJob] Checking out branch #{branch}..."
    checkout_success = system("git", "-C", repo_path.to_s, "checkout", branch, exception: true)
    unless checkout_success
      Rails.logger.error "[CodebaseIndexingJob] Failed to checkout branch #{branch} for repository #{repository.id}"
      return
    end

    # 4. 디렉토리 및 파일 인덱싱 시작
    Rails.logger.info "[CodebaseIndexingJob] Starting indexing for repository #{repository.id}, branch #{branch}..."
    total_files = Dir.glob(File.join(repo_path, '**', '*')).select { |f| File.file?(f) }.count
    indexed_files = 0
    index_directory(repository, branch, repo_path, '', nil, 0, total_files, indexed_files)
    Rails.logger.info "[CodebaseIndexingJob] Finished indexing for repository #{repository.id}, branch #{branch}."
  end

  private

  def index_directory(repository, branch, repo_path, relative_path, parent_directory, depth, total_files, indexed_files)
    current_full_path = relative_path.empty? ? repo_path : File.join(repo_path, relative_path)

    # 1. 현재 디렉토리 레코드 생성
    dir_name = File.basename(relative_path)
    parent_relative_path = File.dirname(relative_path)

    stored_parent_path = Pathname.new('/').join(parent_relative_path).to_s

    # 루트 디렉토리 특별 처리
    if relative_path.empty? || relative_path == '.'
      dir_name = '/'
      stored_parent_path = '/'
    end

    current_dir_record = repository.codebase_directories.create!(
      branch: branch,
      path: stored_parent_path,
      name: dir_name,
      depth: depth,
      parent_directory: parent_directory
    )

    # 2. 하위 항목 처리
    entries = Dir.entries(current_full_path).reject { |entry| ['.', '..', '.git'].include?(entry) }
    entries.each do |entry|
      entry_relative_path = File.join(relative_path, entry)
      entry_full_path = File.join(repo_path, entry_relative_path)

      if File.directory?(entry_full_path)
        indexed_files = index_directory(repository, branch, repo_path, entry_relative_path, current_dir_record, depth + 1, total_files, indexed_files)
      else
        index_file(repository, branch, entry_relative_path, depth + 1)
        indexed_files += 1
        # Progress logging removed for now
      end
    end
    indexed_files
  rescue => e
    Rails.logger.error "[CodebaseIndexingJob] Error indexing directory #{current_full_path}: #{e.message} - #{e.backtrace.first}"
    # Ensure indexed_files is returned even on error
    indexed_files
  end

  def index_file(repository, branch, relative_path, depth)
    full_path = File.join(repository.local_path, relative_path)
    return unless File.exist?(full_path)

    filename = File.basename(relative_path)
    dir_relative_path = File.dirname(relative_path)

    # 경로 저장 규칙에 따라 변환
    stored_path = Pathname.new('/').join(dir_relative_path).to_s

    is_binary = binary?(full_path)
    content = is_binary ? nil : File.read(full_path)
    size = File.size(full_path)
    hash_id = Digest::SHA256.hexdigest(content || File.binread(full_path))

    repository.codebase_files.create!(
      branch: branch,
      path: stored_path,
      filename: filename,
      depth: depth,
      content: content,
      hash_id: hash_id,
      size: size,
      is_binary: is_binary
    )
  rescue => e
    Rails.logger.error "[CodebaseIndexingJob] Error indexing file #{full_path}: #{e.message}"
  end

  def binary?(file_path)
    return true if File.size(file_path) > 1_000_000 # 1MB 이상은 바이너리로 간주
    s = File.read(file_path, 512) || ''
    s.each_byte.any? { |byte| byte == 0 }
  end

  def extract_symbols(file_record)
    # TODO: Implement symbol extraction logic based on file type
    # For example, using a parser gem like 'parser' for Ruby
  end
end
