class AiendNotificationJob < ApplicationJob
  queue_as :default
  
  def perform(repository_id, action, changed_files: nil, commit_hash: nil)
    Rails.logger.info "Processing Aiend notification job for repository: #{repository_id}"
    
    begin
      repository = Repository.find(repository_id)
      
      # Aiend에 변경사항 알림
      result = AiendWebhookService.notify_git_changes(
        repository,
        action,
        changed_files: changed_files,
        commit_hash: commit_hash
      )
      
      if result
        Rails.logger.info "Aiend notification sent successfully for repository: #{repository.name}"
        
        # 성공한 경우 repository 업데이트
        repository.update!(
          last_aiend_sync_at: Time.current,
          aiend_sync_status: 'success'
        )
      else
        Rails.logger.error "Failed to send Aiend notification for repository: #{repository.name}"
        
        repository.update!(
          aiend_sync_status: 'failed',
          aiend_sync_error: 'Webhook notification failed'
        )
      end
      
    rescue ActiveRecord::RecordNotFound => e
      Rails.logger.error "Repository not found for Aiend notification: #{repository_id}"
    rescue StandardError => e
      Rails.logger.error "Aiend notification job failed: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      
      # 에러 상태 업데이트
      begin
        repository = Repository.find(repository_id)
        repository.update!(
          aiend_sync_status: 'failed',
          aiend_sync_error: e.message
        )
      rescue ActiveRecord::RecordNotFound
        # Repository가 삭제된 경우 무시
      end
    end
  end
end
