# app/models/codebase_directory.rb
class CodebaseDirectory < ApplicationRecord
  belongs_to :repository
  belongs_to :parent_directory, class_name: 'CodebaseDirectory', optional: true
  has_many :subdirectories, class_name: 'CodebaseDirectory', foreign_key: 'parent_directory_id', dependent: :destroy

  # 검증
  validates :branch, presence: true
  validates :path, presence: true
  validates :name, presence: true
  validates :depth, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }

  # 스코프
  scope :by_repository, ->(repository_id) { where(repository_id: repository_id) }
  scope :by_branch, ->(branch) { where(branch: branch) }
  scope :roots, -> { where(parent_directory_id: nil) }

  # 디렉토리 트리 구축
  def self.build_tree(repository_id, branch)
    directories = by_repository(repository_id).by_branch(branch).order(:path)
    tree = {}
    directories.each do |dir|
      path_parts = dir.path.split('/')
      current_level = tree
      path_parts.each do |part|
        current_level = (current_level[part] ||= {})
      end
      current_level[dir.name] = {}
    end
    tree
  end
end
