class Commit < ApplicationRecord
  belongs_to :repository

  validates :sha, presence: true, uniqueness: { scope: :repository_id }
  validates :message, presence: true
  validates :author_name, presence: true
  validates :author_email, presence: true
  validates :committed_at, presence: true

  scope :by_author, ->(email) { where(author_email: email) }
  scope :recent, -> { order(committed_at: :desc) }
  scope :in_date_range, ->(start_date, end_date) { where(committed_at: start_date..end_date) }

  def short_sha
    sha[0..7]
  end

  def author_display
    "#{author_name} <#{author_email}>"
  end

  def committer_display
    return author_display if committer_name.blank? || committer_email.blank?
    "#{committer_name} <#{committer_email}>"
  end

  def same_author_and_committer?
    author_name == committer_name && author_email == committer_email
  end
end
