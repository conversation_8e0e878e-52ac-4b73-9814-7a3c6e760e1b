# app/models/codebase_symbol.rb
class CodebaseSymbol < ApplicationRecord
  belongs_to :codebase_file

  # 검증
  validates :name, presence: true
  validates :symbol_type, presence: true

  # 스코프
  scope :by_type, ->(type) { where(symbol_type: type) }

  # 전체 텍스트 검색 (FTS5 사용)
  def self.search(query)
    joins("JOIN codebase_symbols_fts ON codebase_symbols.id = codebase_symbols_fts.rowid")
      .where("codebase_symbols_fts MATCH ?", query)
  end

  # STI(Single Table Inheritance)를 피하기 위해 'symbol_type' 컬럼 사용
  self.inheritance_column = :_type_disabled

  # 메타데이터 접근자
  def metadata
    self[:metadata] || {}
  end
end
