class CodeFile < ApplicationRecord
  self.table_name = 'files'
  belongs_to :repository
  belongs_to :directory, optional: true

  validates :name, presence: true
  validates :relative_path, presence: true
  validates :size_bytes, presence: true, numericality: { greater_than_or_equal_to: 0 }
  validates :lines_count, numericality: { greater_than_or_equal_to: 0 }, allow_nil: true

  scope :by_extension, ->(ext) { where(extension: ext) }
  scope :by_language, ->(lang) { where(language: lang) }
  scope :binary_files, -> { where(is_binary: true) }
  scope :text_files, -> { where(is_binary: false) }
  scope :recent, -> { order(last_modified_at: :desc) }

  # Search scopes
  scope :search_content, ->(query) { where('content LIKE ? AND is_binary = ?', "%#{query}%", false) }
  scope :search_by_name, ->(query) { where('name LIKE ?', "%#{query}%") }
  scope :search_by_path, ->(query) { where('relative_path LIKE ?', "%#{query}%") }
  scope :search, ->(query) {
    where('(content LIKE ? AND is_binary = ?) OR name LIKE ? OR relative_path LIKE ?',
          "%#{query}%", false, "%#{query}%", "%#{query}%")
  }

  def full_path
    File.join(repository.path, relative_path)
  end

  def readable?
    !is_binary
  end

  def size_human
    return '0 B' if size_bytes.zero?

    units = %w[B KB MB GB TB]
    size = size_bytes.to_f
    unit_index = 0

    while size >= 1024 && unit_index < units.length - 1
      size /= 1024
      unit_index += 1
    end

    "#{size.round(1)} #{units[unit_index]}"
  end

  def content
    return nil if is_binary
    return @content if defined?(@content)

    begin
      @content = File.read(full_path)
    rescue => e
      Rails.logger.error "Failed to read file #{full_path}: #{e.message}"
      nil
    end
  end
end
