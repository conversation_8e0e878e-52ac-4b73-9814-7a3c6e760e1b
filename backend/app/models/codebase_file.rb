# app/models/codebase_file.rb
class CodebaseFile < ApplicationRecord
  belongs_to :repository
  has_many :codebase_symbols, dependent: :destroy

  # 검증
  validates :branch, presence: true
  validates :path, presence: true
  validates :filename, presence: true
  validates :depth, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }

  # 스코프
  scope :by_repository, ->(repository_id) { where(repository_id: repository_id) }
  scope :by_branch, ->(branch) { where(branch: branch) }
  scope :by_extension, ->(extension) { where(extension: extension) }
  scope :by_depth, ->(depth) { where(depth: depth) }

  # 전체 텍스트 검색 스코프 (FTS5 사용)
  def self.search(query)
    joins("JOIN codebase_files_fts ON codebase_files.id = codebase_files_fts.rowid")
      .where("codebase_files_fts MATCH ?", query)
  end

  # 파일 경로 생성
  def full_path
    File.join(path, filename)
  end

  # 파일 확장자 설정
  before_validation :set_extension

  private

  def set_extension
    self.extension = File.extname(filename).delete('.') if filename.present?
  end
end
