class Directory < ApplicationRecord
  belongs_to :repository
  belongs_to :parent, class_name: 'Directory', optional: true
  has_many :children, class_name: 'Directory', foreign_key: 'parent_id', dependent: :destroy
  has_many :files, class_name: 'CodeFile', dependent: :destroy

  validates :name, presence: true
  validates :relative_path, presence: true
  validates :depth, presence: true, numericality: { greater_than_or_equal_to: 0 }

  scope :by_depth, ->(depth) { where(depth: depth) }
  scope :root_directories, -> { where(depth: 0) }

  def full_path
    File.join(repository.path, relative_path)
  end

  def file_count
    files.count
  end

  def subdirectory_count
    repository.directories.where('relative_path LIKE ? AND depth = ?', "#{relative_path}/%", depth + 1).count
  end

  # Class method to build complete file tree
  def self.build_tree(repository, options = {})
    include_files = options.fetch(:include_files, true)
    max_depth = options.fetch(:max_depth, nil)
    lazy_load = options.fetch(:lazy_load, false)

    if lazy_load
      build_lazy_tree(repository, max_depth)
    else
      build_complete_tree(repository, include_files, max_depth)
    end
  end

  # Build complete tree structure
  def self.build_complete_tree(repository, include_files = true, max_depth = nil)
    # Get all directories
    directories_query = repository.directories.includes(:files)
    directories_query = directories_query.where('depth <= ?', max_depth) if max_depth
    directories = directories_query.order(:relative_path)

    # Build tree structure
    tree = []
    directory_map = {}

    # We'll add root files after directories for proper sorting

    # Create root structure
    directories.each do |dir|
      node = {
        id: "dir_#{dir.id}",
        name: dir.name,
        type: 'directory',
        path: dir.relative_path,
        depth: dir.depth,
        children: [],
        expanded: false,
        file_count: dir.files.count,
        subdirectory_count: 0
      }

      # Add files if requested (will be sorted later)
      if include_files
        dir.files.each do |file|
          file_node = {
            id: "file_#{file.id}",
            name: file.name,
            type: 'file',
            path: file.relative_path,
            size: file.size_bytes,
            lines: file.lines_count,
            language: file.language,
            extension: file.extension,
            is_binary: file.is_binary,
            last_modified: file.last_modified_at
          }
          node[:children] << file_node
        end
      end

      directory_map[dir.relative_path] = node

      # Find parent and add to tree
      if dir.depth == 0
        tree << node
      else
        parent_path = File.dirname(dir.relative_path)
        parent_node = directory_map[parent_path]
        if parent_node
          parent_node[:children] << node
          parent_node[:subdirectory_count] += 1
        end
      end
    end

    # Add root level files (files not in any directory)
    if include_files
      root_files = repository.files.where('relative_path NOT LIKE ?', '%/%')
      root_files.each do |file|
        file_node = {
          id: "file_#{file.id}",
          name: file.name,
          type: 'file',
          path: file.relative_path,
          size: file.size_bytes,
          lines: file.lines_count,
          language: file.language,
          extension: file.extension,
          is_binary: file.is_binary,
          last_modified: file.last_modified_at
        }
        tree << file_node
      end
    end

    # Sort tree: directories first, then files, both alphabetically
    tree.sort_by! { |node| [node[:type] == 'directory' ? 0 : 1, node[:name].downcase] }

    # Sort children in each directory recursively
    sort_tree_children(tree)

    tree
  end

  # Recursively sort children in tree nodes
  def self.sort_tree_children(nodes)
    nodes.each do |node|
      if node[:children] && node[:children].any?
        # Sort children: directories first, then files, both alphabetically
        node[:children].sort_by! { |child| [child[:type] == 'directory' ? 0 : 1, child[:name].downcase] }
        # Recursively sort grandchildren
        sort_tree_children(node[:children])
      end
    end
  end

  # Build lazy-loading tree (only directories, files loaded on demand)
  def self.build_lazy_tree(repository, max_depth = 2)
    directories = repository.directories
                           .where(depth: 0...(max_depth || 2))
                           .order(:relative_path)

    tree = []
    directory_map = {}

    directories.each do |dir|
      node = {
        id: "dir_#{dir.id}",
        name: dir.name,
        type: 'directory',
        path: dir.relative_path,
        depth: dir.depth,
        children: [],
        expanded: false,
        has_children: dir.files.exists? || repository.directories.where('relative_path LIKE ?', "#{dir.relative_path}/%").exists?,
        file_count: dir.files.count,
        subdirectory_count: repository.directories.where('relative_path LIKE ? AND depth = ?', "#{dir.relative_path}/%", dir.depth + 1).count
      }

      directory_map[dir.relative_path] = node

      if dir.depth == 0
        tree << node
      else
        parent_path = File.dirname(dir.relative_path)
        parent_node = directory_map[parent_path]
        parent_node[:children] << node if parent_node
      end
    end

    tree
  end
end
