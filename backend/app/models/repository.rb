class Repository < ApplicationRecord
  has_many :codebase_files, dependent: :destroy
  has_many :codebase_directories, dependent: :destroy
  has_many :directories, dependent: :destroy
  has_many :files, class_name: 'CodeFile', dependent: :destroy, foreign_key: 'repository_id'
  has_many :commits, dependent: :destroy

  validates :name, presence: true
  validates :url, presence: true

  # Path is optional during creation (will be set after cloning)
  validates :path, uniqueness: true, allow_blank: true

  # 삭제 전 정리 작업
  before_destroy :cleanup_repository_data

  # Enums for status fields
  enum :status, { pending: 0, scanning: 1, completed: 2, error: 3 }
  enum :clone_status, { pending: 0, cloning: 1, ready: 2, error: 3 }, prefix: :clone

  scope :active, -> { where(active: true) }
  scope :by_status, ->(status) { where(status: status) }
  scope :by_clone_status, ->(status) { where(clone_status: status) }

  # Serialize branches as JSON array
  serialize :branches, type: Array, coder: JSON

  def scan!
    update!(status: 'scanning', last_scan_at: Time.current)
    ScanRepositoryJob.perform_later(self)
  end

  def scan_repository!
    update!(status: 'scanning', last_scan_at: Time.current)

    begin
      scanner = RepositoryScannerService.new(self)
      scanner.scan

      update!(status: 'completed', last_scan_at: Time.current)
      Rails.logger.info "Repository scan completed successfully: #{name}"
    rescue => e
      update!(status: 'error', error_message: e.message)
      Rails.logger.error "Repository scan failed: #{e.message}"
      raise e
    end
  end

  def file_count
    files.count
  rescue
    0
  end

  def directory_count
    directories.count
  rescue
    0
  end

  def total_lines
    # Use direct query to avoid File class conflict
    ::File.where(repository_id: id).sum(:lines_count)
  rescue
    0
  end

  def total_size
    # Use direct query to avoid File class conflict
    ::File.where(repository_id: id).sum(:size_bytes)
  rescue
    0
  end

  def languages
    # Use direct query to avoid File class conflict
    ::File.where(repository_id: id).where.not(language: nil).distinct.pluck(:language)
  rescue
    []
  end

  def file_extensions
    # Use direct query to avoid File class conflict
    ::File.where(repository_id: id).where.not(extension: nil).distinct.pluck(:extension)
  rescue
    []
  end

  def recent_commits(limit = 10)
    commits.order(committed_at: :desc).limit(limit)
  end

  # Git-related methods
  def clone_repository!
    update!(clone_status: 'cloning')
    CloneRepositoryJob.perform_later(self)
  end

  def switch_branch!(branch_name)
    return false unless branches.include?(branch_name)

    update!(current_branch: branch_name, status: 'scanning', last_scan_at: Time.current)
    SwitchBranchJob.perform_later(self, branch_name)
    true
  end

  def fetch_branches!
    FetchBranchesJob.perform_later(self)
  end

  def cloning?
    clone_status == 'cloning'
  end

  def ready?
    clone_status == 'ready'
  end

  def clone_error?
    clone_status == 'error'
  end

  def scanning?
    status == 'scanning'
  end

  def repository_name
    return name if name.present?
    return File.basename(url, '.git') if url.present?
    'Unknown Repository'
  end

  def local_path
    # Use mounted volume for repositories
    Rails.root.join('repositories', sanitized_repository_name)
  end

  def sanitized_repository_name
    # Create a safe directory name from repository URL
    return id.to_s if url.blank?

    # Extract repository name from URL
    repo_name = extract_repo_name_from_url(url)

    # Sanitize the name for filesystem
    sanitized = repo_name.gsub(/[^a-zA-Z0-9\-_.]/, '_')

    # Add ID suffix to ensure uniqueness
    "#{sanitized}_#{id}"
  end

  def extract_repo_name_from_url(url)
    # Handle various Git URL formats
    if url.match?(/github\.com|gitlab\.com|bitbucket\.org/)
      # Extract from HTTPS URLs like https://github.com/user/repo.git
      match = url.match(/\/([^\/]+)\/([^\/]+?)(?:\.git)?(?:\/)?$/)
      return "#{match[1]}_#{match[2]}" if match
    end

    # Extract from SSH <NAME_EMAIL>:user/repo.git
    if url.start_with?('git@')
      match = url.match(/:([^\/]+)\/([^\/]+?)(?:\.git)?$/)
      return "#{match[1]}_#{match[2]}" if match
    end

    # Fallback: use the last part of the URL
    File.basename(url, '.git').presence || 'unknown_repo'
  end

  def git_url_with_auth
    return url unless username.present?

    begin
      uri = URI.parse(url)
      if password.present?
        # URL encode username and password to handle special characters
        encoded_username = URI.encode_www_form_component(username)
        encoded_password = URI.encode_www_form_component(password)
        uri.userinfo = "#{encoded_username}:#{encoded_password}"
      else
        uri.userinfo = URI.encode_www_form_component(username)
      end
      uri.to_s
    rescue URI::InvalidURIError => e
      Rails.logger.error "Invalid URI: #{url}, error: #{e.message}"
      url
    end
  end

  # Repository management methods
  def repository_exists_on_disk?
    File.exist?(local_path) && File.directory?(local_path)
  end

  def repository_size_on_disk
    return 0 unless repository_exists_on_disk?

    Dir.glob(File.join(local_path, '**', '*'))
       .select { |f| File.file?(f) }
       .sum { |f| File.size(f) }
  rescue
    0
  end

  def git_repository_valid?
    return false unless repository_exists_on_disk?

    Dir.chdir(local_path) do
      stdout, stderr, status = Open3.capture3("git", "status")
      status.success?
    end
  rescue
    false
  end

  def cleanup_repository!
    if repository_exists_on_disk?
      FileUtils.rm_rf(local_path)
      Rails.logger.info "Cleaned up repository directory: #{local_path}"
    end
  end

  # Class methods for repository management
  def self.find_by_directory_name(directory_name)
    # Extract ID from directory name (format: name_id)
    if directory_name.match(/_(\d+)$/)
      id = directory_name.match(/_(\d+)$/)[1]
      find_by(id: id)
    end
  end

  def self.scan_repositories_directory
    repositories_root = Rails.root.join('repositories')
    return [] unless File.exist?(repositories_root)

    Dir.entries(repositories_root)
       .select { |entry| File.directory?(File.join(repositories_root, entry)) }
       .reject { |entry| entry.start_with?('.') }
       .map { |dir_name| find_by_directory_name(dir_name) }
       .compact
  end

  # Total counts with better method names
  def total_files_count
    file_count
  end

  def total_lines_count
    total_lines
  end

  private

  def cleanup_repository_data
    """Repository 삭제 전 정리 작업"""

    Rails.logger.info "Cleaning up repository data for: #{name}"

    begin
      # 1. 관련된 모든 파일 데이터 정리 (이미 dependent: :destroy로 처리됨)
      Rails.logger.info "Database associations will be cleaned up automatically"

      # 2. 향후 Aiend에서 해당 repository 데이터 삭제 요청
      # (현재는 로그만 남김, 향후 Aiend API 추가 시 구현)
      Rails.logger.info "Repository cleanup completed for: #{name}"

    rescue => e
      Rails.logger.error "Error during repository cleanup: #{e.message}"
      # cleanup 실패해도 삭제는 진행 (raise하지 않음)
    end
  end
end
