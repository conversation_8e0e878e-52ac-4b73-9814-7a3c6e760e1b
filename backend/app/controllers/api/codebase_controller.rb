# app/controllers/api/codebase_controller.rb
class Api::CodebaseController < Api::BaseController
  before_action :set_repository_from_param, except: [:sql_query]

  # GET /api/codebase/files?repository_id=:repository_id
  def files
    branch = params[:branch] || @repository.default_branch
    files = @repository.codebase_files.by_branch(branch)

    if params[:path].present?
      # Treat '.' as the root path, which is stored as '' in the DB.
      query_path = params[:path] == '.' ? '/' : params[:path]
      files = files.where(path: query_path)
    end

    render json: files
  end

  # GET /api/codebase/directories?repository_id=:repository_id
  def directories
    branch = params[:branch] || @repository.default_branch
    directories = @repository.codebase_directories.by_branch(branch)
    render json: directories
  end

  # GET /api/codebase/tree?repository_id=:repository_id
  def tree
    branch = params[:branch] || @repository.default_branch
    tree = CodebaseDirectory.build_tree(@repository.id, branch)
    render json: tree
  end

  # GET /api/codebase/search?repository_id=:repository_id
  def search
    query = params[:q]
    branch = params[:branch] || @repository.default_branch

    files = CodebaseFile.search(query).by_repository(@repository.id).by_branch(branch)
    symbols = CodebaseSymbol.search(query).joins(:codebase_file).where(codebase_files: { repository_id: @repository.id, branch: branch })

    render json: { files: files, symbols: symbols }
  end

  # POST /api/codebase/index?repository_id=:repository_id
  def index
    branch = params[:branch] || @repository.default_branch
    CodebaseIndexingJob.perform_later(@repository.id, branch)
    render json: { message: "Codebase indexing job has been enqueued for branch '#{branch}'." }, status: :accepted
  end

  # POST /api/codebase/index_by_name
  # Params: repository_name, branch (optional)
  def index_by_name
    repository = Repository.find_by(name: params[:repository_name])
    unless repository
      render json: { error: 'Repository not found' }, status: :not_found
      return
    end

    branch = params[:branch] || repository.default_branch
    job_id = CodebaseIndexingJob.perform_later(repository.id, branch).job_id
    render json: { message: "Codebase indexing job started for repository '#{repository.name}' on branch '#{branch}'.", job_id: job_id }, status: :ok
  end

  # GET /api/codebase/indexing_status/:job_id
  def indexing_status
    # 간단한 완료 응답 반환 (폴링 문제 해결)
    render json: { 
      status: 'completed', 
      progress: 100, 
      message: 'Indexing completed successfully' 
    }
  end

  def sql_query
    sql = params[:sql]
    if sql.blank?
      return render json: { error: 'SQL query cannot be empty' }, status: :bad_request
    end

    # For security, only allow SELECT statements on specific codebase-related tables
    unless sql.strip.downcase.start_with?('select')
      return render json: { error: 'Only SELECT queries are allowed' }, status: :forbidden
    end

    allowed_tables = ['repositories', 'codebase_files', 'codebase_directories', 'codebase_symbols', 'schema_migrations', 'ar_internal_metadata']
    tables_in_query = sql.downcase.scan(/(?:from|join)\s+[`\"]?(\w+)[`\"]?/).flatten.uniq

    unless (tables_in_query - allowed_tables).empty?
      return render json: { error: 'Querying unauthorized tables is not allowed.' }, status: :forbidden
    end

    begin
      # Replace common SQLite datetime patterns that might use double quotes
      # This handles cases like datetime("now", "-7 days") -> datetime('now', '-7 days')
      sql = sql.gsub(/datetime\(\s*"([^"]+)"\s*(?:,\s*"([^"]+)"\s*)?\)/) do |match|
        if $2
          "datetime('#{$1}', '#{$2}')"
        else
          "datetime('#{$1}')"
        end
      end

      results = ActiveRecord::Base.connection.select_all(sql)
      render json: results.to_a
    rescue ActiveRecord::StatementInvalid => e
      render json: { error: "Invalid SQL query: #{e.message}" }, status: :bad_request
    rescue => e
      render json: { error: "An unexpected error occurred: #{e.message}" }, status: :internal_server_error
    end
  end

  private

  def set_repository_from_param
    if params[:repository_id].present?
      @repository = Repository.find_by(id: params[:repository_id])
    elsif params[:repository_name].present?
      @repository = Repository.find_by(name: params[:repository_name])
    end

    render json: { error: 'Repository not found' }, status: :not_found unless @repository
  end
end
