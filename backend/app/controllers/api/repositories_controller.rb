require 'open3'

class Api::RepositoriesController < Api::BaseController
  before_action :set_repository, only: [:show, :update, :destroy, :scan, :tree, :stats, :branches, :switch_branch, :status, :commits, :commit_detail, :commit_diff, :file_content, :commit_files_content, :pull, :aiend_status, :trigger_aiend_scan, :trigger_aiend_branch_scan]

  def index
    repositories = Repository.active

    result = paginate_collection(
      repositories,
      page: params[:page],
      per_page: params[:per_page]
    )

    render_success({
      repositories: result[:data].map { |repo| repository_summary(repo) },
      pagination: result[:pagination],
      last_updated: Time.current.iso8601
    })
  end

  def show
    render_success({
      repository: repository_detail(@repository),
      last_updated: Time.current.iso8601
    })
  end

  def create
    repository = Repository.new(repository_params)

    if repository.save
      # 자동 스캔 제거 - 사용자가 수동으로 선택한 브랜치만 분석
      # ScanRepositoryJob.perform_later(repository) if params[:auto_scan]

      render_success(
        { repository: repository_detail(repository) },
        'Repository created successfully',
        :created
      )
    else
      render_validation_errors(repository)
    end
  end

  def update
    if @repository.update(repository_params)
      render_success(
        { repository: repository_detail(@repository) },
        'Repository updated successfully'
      )
    else
      render_validation_errors(@repository)
    end
  end

  def destroy
    begin
      repository_name = @repository.name
      repository_path = @repository.local_path

      Rails.logger.info "Deleting repository: #{repository_name}"

      # 1. Aiend에 삭제 알림
      if AiendWebhookService.aiend_available?
        begin
          Rails.logger.info "Notifying Aiend about repository deletion: #{repository_name}"
          AiendWebhookService.delete_repository(@repository.id, repository_name)
        rescue => e
          Rails.logger.warn "Failed to notify Aiend about repository deletion: #{e.message}"
        end
      end

      # 2. 데이터베이스에서 repository 삭제 (cascade로 관련 데이터도 삭제됨)
      @repository.destroy!

      # 3. 실제 폴더 삭제
      if repository_path && File.exist?(repository_path)
        Rails.logger.info "Removing repository folder: #{repository_path}"
        FileUtils.rm_rf(repository_path)
        Rails.logger.info "Repository folder removed successfully"
      else
        Rails.logger.warn "Repository folder not found or already removed: #{repository_path}"
      end

      render_success(nil, "Repository '#{repository_name}' deleted successfully")

    rescue => e
      Rails.logger.error "Failed to delete repository: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      render_error("Failed to delete repository: #{e.message}")
    end
  end

  def scan
    if @repository.scanning?
      render_error('Repository is already being scanned')
      return
    end

    ScanRepositoryJob.perform_later(@repository)
    render_success(nil, 'Repository scan started')
  end

  def tree
    options = {
      include_files: params[:include_files] != 'false',
      max_depth: params[:max_depth]&.to_i,
      lazy_load: params[:lazy_load] == 'true'
    }

    tree_data = Directory.build_tree(@repository, options)
    render_success({
      tree: tree_data,
      repository_id: @repository.id,
      total_files: @repository.total_files_count,
      total_directories: @repository.directories.count
    })
  end

  def stats
    stats = {
      total_files: @repository.total_files_count,
      total_lines: @repository.total_lines_count,
      languages: @repository.languages,
      file_extensions: @repository.file_extensions,
      last_scan: @repository.last_scan_at,
      status: @repository.status
    }

    render_success({ stats: stats })
  end

  # New Git-related actions
  def clone
    repository_data = clone_params

    repository = Repository.new(
      url: repository_data[:url],
      username: repository_data[:username],
      password: repository_data[:password],
      name: extract_repo_name(repository_data[:url]),
      clone_status: 'pending',
      status: 'pending'
    )

    if repository.save
      repository.clone_repository!
      render_success(
        { repository: repository_detail(repository) },
        'Repository clone started',
        :created
      )
    else
      render_validation_errors(repository)
    end
  end

  def branches
    if @repository.ready?
      branches_data = @repository.branches.map do |branch_name|
        {
          name: branch_name,
          is_default: branch_name == @repository.current_branch,
          commit: get_branch_commit(@repository, branch_name),
          last_commit_date: get_branch_last_commit_date(@repository, branch_name),
          last_commit_message: get_branch_last_commit_message(@repository, branch_name)
        }
      end

      render_success({ branches: branches_data })
    else
      render_error('Repository is not ready. Please wait for cloning to complete.')
    end
  end

  def switch_branch
    branch_name = params[:branch]

    if branch_name.blank?
      render_error('Branch name is required')
      return
    end

    unless @repository.ready?
      render_error('Repository is not ready for branch switching')
      return
    end

    if @repository.switch_branch!(branch_name)
      render_success(
        { repository: repository_detail(@repository) },
        "Switched to branch '#{branch_name}' and started indexing"
      )
    else
      render_error("Branch '#{branch_name}' not found")
    end
  end

  def pull
    unless @repository.ready? && @repository.repository_exists_on_disk?
      render_error('Repository is not ready or does not exist on disk')
      return
    end

    begin
      Dir.chdir(@repository.local_path) do
        # Fetch latest changes
        stdout, stderr, status = Open3.capture3("git", "fetch", "origin")
        unless status.success?
          raise "Failed to fetch: #{stderr}"
        end

        # Pull changes for current branch
        current_branch = @repository.current_branch
        stdout, stderr, status = Open3.capture3("git", "pull", "origin", current_branch)
        unless status.success?
          raise "Failed to pull: #{stderr}"
        end

        # Update last indexed time
        @repository.update!(last_indexed_at: Time.current)

        # Aiend에 pull 완료 알림
        AiendWebhookService.notify_git_changes_async(@repository.id, 'pull')
      end

      render_success(
        { repository: repository_detail(@repository) },
        "Successfully pulled latest changes"
      )
    rescue => e
      Rails.logger.error "Failed to pull repository: #{e.message}"
      render_error("Failed to pull repository: #{e.message}")
    end
  end

  def aiend_status
    """Aiend 연동 상태 조회"""

    aiend_health = AiendWebhookService.health_check

    aiend_info = {
      service_status: aiend_health[:status],
      service_response: aiend_health[:response],
      last_sync_at: @repository.last_aiend_sync_at,
      sync_status: @repository.aiend_sync_status,
      sync_error: @repository.aiend_sync_error,
      last_scan_at: @repository.last_aiend_scan_at,
      scan_status: @repository.aiend_scan_status,
      scan_progress: @repository.aiend_scan_progress,
      scan_message: @repository.aiend_scan_message,
      scan_error: @repository.aiend_scan_error,
      task_id: @repository.aiend_task_id,
      last_scan_completed_at: @repository.last_aiend_scan_completed_at
    }

    render_success(
      { aiend: aiend_info },
      "Aiend status retrieved successfully"
    )
  end

  def trigger_aiend_scan
    """Aiend 스캔 수동 트리거"""

    force_reindex = params[:force_reindex] == 'true'

    begin
      # 즉시 스캔 트리거
      result = AiendWebhookService.trigger_repository_scan(@repository, force_reindex: force_reindex)

      if result
        @repository.update!(
          last_aiend_scan_at: Time.current,
          aiend_scan_status: 'triggered',
          aiend_task_id: result['task_id']
        )

        # 작업 모니터링 시작
        if result['task_id']
          AiendTaskMonitorJob.perform_later(@repository.id, result['task_id'])
        end

        render_success(
          {
            repository: repository_detail(@repository),
            aiend_task: result
          },
          "Aiend scan triggered successfully"
        )
      else
        render_error("Failed to trigger Aiend scan")
      end

    rescue => e
      Rails.logger.error "Failed to trigger Aiend scan: #{e.message}"
      render_error("Failed to trigger Aiend scan: #{e.message}")
    end
  end

  def trigger_aiend_branch_scan
    """Aiend 브랜치별 스캔 수동 트리거"""

    branch = params[:branch]
    force_reindex = params[:force_reindex] == 'true'

    if branch.blank?
      render_error("Branch parameter is required")
      return
    end

    unless @repository.branches.include?(branch)
      render_error("Branch '#{branch}' not found in repository")
      return
    end

    begin
      # 브랜치별 스캔 트리거
      result = AiendWebhookService.trigger_repository_branch_scan(@repository, branch, force_reindex: force_reindex)

      if result
        @repository.update!(
          last_aiend_scan_at: Time.current,
          aiend_scan_status: 'triggered',
          aiend_task_id: result['task_id']
        )

        # 작업 모니터링 시작
        if result['task_id']
          AiendTaskMonitorJob.perform_later(@repository.id, result['task_id'])
        end

        render_success(
          {
            repository: repository_detail(@repository),
            aiend_task: result,
            branch: branch
          },
          "Aiend branch scan triggered successfully for branch: #{branch}"
        )
      else
        render_error("Failed to trigger Aiend branch scan")
      end

    rescue => e
      Rails.logger.error "Failed to trigger Aiend branch scan: #{e.message}"
      render_error("Failed to trigger Aiend branch scan: #{e.message}")
    end
  end

  # repository_branch_id 형태로 Aiend 스캔 트리거
  def trigger_repository_branch_id_scan
    """repository_branch_id 형태로 Aiend 스캔 트리거"""

    repository_branch_id = params[:repository_branch_id]
    force_reindex = params[:force_reindex] == 'true'

    if repository_branch_id.blank?
      render_error("repository_branch_id parameter is required")
      return
    end

    begin
      # repository_branch_id 스캔 트리거
      result = AiendWebhookService.trigger_repository_branch_scan(repository_branch_id, force_reindex: force_reindex)

      if result
        # repository_branch_id에서 repository_id 추출 (오른쪽부터 첫 번째 '_' 이전까지)
        repository_id = repository_branch_id.split('_')[0..-2].join('_')

        # 해당 repository가 존재하는 경우 업데이트
        if repository = Repository.find_by(id: repository_id)
          repository.update!(
            last_aiend_scan_at: Time.current,
            aiend_scan_status: 'triggered',
            aiend_task_id: result['task_id']
          )

          # 작업 모니터링 시작
          if result['task_id']
            AiendTaskMonitorJob.perform_later(repository.id, result['task_id'])
          end
        end

        render_success(
          {
            repository_branch_id: repository_branch_id,
            aiend_task: result
          },
          "Aiend repository branch scan triggered successfully for: #{repository_branch_id}"
        )
      else
        render_error("Failed to trigger Aiend repository branch scan")
      end

    rescue => e
      Rails.logger.error "Failed to trigger Aiend repository branch scan: #{e.message}"
      render_error("Failed to trigger Aiend repository branch scan: #{e.message}")
    end
  end

  def status
    render_success({
      repository: {
        id: @repository.id,
        name: @repository.name,
        clone_status: @repository.clone_status,
        status: @repository.status,
        current_branch: @repository.current_branch,
        progress: calculate_progress(@repository),
        message: get_status_message(@repository),
        error: @repository.error_message,
        local_path: @repository.local_path.to_s,
        directory_name: @repository.sanitized_repository_name,
        exists_on_disk: @repository.repository_exists_on_disk?,
        size_on_disk: @repository.repository_size_on_disk,
        git_valid: @repository.git_repository_valid?,
        last_updated: Time.current.iso8601
      }
    })
  end

  # New action to scan and sync repositories directory
  def scan_directory
    begin
      scanned_repos = Repository.scan_repositories_directory
      repositories_root = Rails.root.join('repositories')

      # Get all directories in repositories folder
      all_dirs = if File.exist?(repositories_root)
        Dir.entries(repositories_root)
           .select { |entry| File.directory?(File.join(repositories_root, entry)) }
           .reject { |entry| entry.start_with?('.') }
      else
        []
      end

      # Find orphaned directories (directories without corresponding database records)
      orphaned_dirs = all_dirs.reject do |dir_name|
        Repository.find_by_directory_name(dir_name)
      end

      render_success({
        scanned_repositories: scanned_repos.map { |repo| repository_summary(repo) },
        total_directories: all_dirs.size,
        orphaned_directories: orphaned_dirs,
        repositories_root: repositories_root.to_s
      })
    rescue => e
      render_error("Failed to scan repositories directory: #{e.message}")
    end
  end

  # Git commit related actions
  def commits
    branch = params[:branch] || @repository.current_branch
    page = params[:page]&.to_i || 1
    per_page = [params[:per_page]&.to_i || 20, 100].min

    unless @repository.ready? && @repository.repository_exists_on_disk?
      render_error('Repository is not ready or does not exist on disk')
      return
    end

    begin
      commits_data = get_git_commits(@repository, branch, page, per_page)
      render_success({
        commits: commits_data[:commits],
        pagination: commits_data[:pagination],
        branch: branch
      })
    rescue => e
      Rails.logger.error "Failed to get commits: #{e.message}"
      render_error("Failed to get commits: #{e.message}")
    end
  end

  def commit_detail
    commit_hash = params[:commit_hash]

    unless @repository.ready? && @repository.repository_exists_on_disk?
      render_error('Repository is not ready or does not exist on disk')
      return
    end

    begin
      commit_detail = get_git_commit_detail(@repository, commit_hash)
      render_success(commit_detail)
    rescue => e
      Rails.logger.error "Failed to get commit detail: #{e.message}"
      render_error("Failed to get commit detail: #{e.message}")
    end
  end

  def commit_diff
    commit_hash = params[:commit_hash]
    format = params[:format] || 'unified'
    context = params[:context]&.to_i || 3

    unless @repository.ready? && @repository.repository_exists_on_disk?
      render_error('Repository is not ready or does not exist on disk')
      return
    end

    begin
      diff_data = get_git_commit_diff(@repository, commit_hash, format, context)
      render_success(diff_data)
    rescue => e
      Rails.logger.error "Failed to get commit diff: #{e.message}"
      render_error("Failed to get commit diff: #{e.message}")
    end
  end

  def file_content
    commit_hash = params[:commit_hash]
    file_path = params[:path]

    unless @repository.ready? && @repository.repository_exists_on_disk?
      render_error('Repository is not ready or does not exist on disk')
      return
    end

    begin
      content = get_git_file_content(@repository, commit_hash, file_path)
      render_success({ content: content, path: file_path, commit: commit_hash })
    rescue => e
      Rails.logger.error "Failed to get file content: #{e.message}"
      render_error("Failed to get file content: #{e.message}")
    end
  end

  def commit_files_content
    commit_hash = params[:commit_hash]

    unless @repository.ready? && @repository.repository_exists_on_disk?
      render_error('Repository is not ready or does not exist on disk')
      return
    end

    begin
      files_content = get_git_commit_files_content(@repository, commit_hash)
      render_success({
        commit: commit_hash,
        files: files_content[:files],
        total_size: files_content[:total_size],
        total_files: files_content[:total_files]
      })
    rescue => e
      Rails.logger.error "Failed to get commit files content: #{e.message}"
      render_error("Failed to get commit files content: #{e.message}")
    end
  end

  private

  def set_repository
    @repository = Repository.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render_not_found('Repository not found')
  end

  def repository_params
    params.require(:repository).permit(:name, :path, :url, :description, :active)
  end

  def clone_params
    params.permit(:url, :username, :password)
  end

  def extract_repo_name(url)
    return 'Unknown Repository' if url.blank?

    # Extract repository name from URL
    name = File.basename(url, '.git')
    name = url.split('/').last if name.blank?
    name.presence || 'Unknown Repository'
  end

  def get_branch_commit(repository, branch_name)
    return nil unless repository.ready? && File.exist?(repository.local_path)

    Dir.chdir(repository.local_path) do
      stdout, stderr, status = Open3.capture3("git", "rev-parse", "origin/#{branch_name}")
      status.success? ? stdout.strip[0..7] : nil
    end
  rescue
    nil
  end

  def get_branch_last_commit_date(repository, branch_name)
    return nil unless repository.ready? && File.exist?(repository.local_path)

    Dir.chdir(repository.local_path) do
      stdout, stderr, status = Open3.capture3("git", "log", "-1", "--format=%ci", "origin/#{branch_name}")
      status.success? ? Time.parse(stdout.strip) : nil
    end
  rescue
    nil
  end

  def get_branch_last_commit_message(repository, branch_name)
    return nil unless repository.ready? && File.exist?(repository.local_path)

    Dir.chdir(repository.local_path) do
      stdout, stderr, status = Open3.capture3("git", "log", "-1", "--format=%s", "origin/#{branch_name}")
      status.success? ? stdout.strip : nil
    end
  rescue
    nil
  end

  def calculate_progress(repository)
    case repository.clone_status
    when 'pending'
      0
    when 'cloning'
      25
    when 'ready'
      case repository.status
      when 'pending'
        50
      when 'scanning'
        75
      when 'completed'
        100
      when 'error'
        0
      else
        50
      end
    when 'error'
      0
    else
      0
    end
  end

  def get_status_message(repository)
    case repository.clone_status
    when 'pending'
      'Waiting to start cloning...'
    when 'cloning'
      'Cloning repository...'
    when 'ready'
      case repository.status
      when 'pending'
        'Repository cloned, waiting to start scanning...'
      when 'scanning'
        'Scanning and indexing files...'
      when 'completed'
        'Repository ready for use'
      when 'error'
        repository.error_message || 'An error occurred during scanning'
      else
        'Repository ready'
      end
    when 'error'
      repository.error_message || 'An error occurred during cloning'
    else
      'Unknown status'
    end
  end

  # Git helper methods
  def get_git_commits(repository, branch, page, per_page)
    Dir.chdir(repository.local_path) do
      # Get total count
      total_stdout, stderr, status = Open3.capture3("git", "rev-list", "--count", "origin/#{branch}")
      total_count = status.success? ? total_stdout.strip.to_i : 0

      # Calculate offset
      offset = (page - 1) * per_page

      # Get commits with pagination
      format = "--pretty=format:%H|%h|%an|%ae|%ai|%s|%b"
      stdout, stderr, status = Open3.capture3(
        "git", "log", "origin/#{branch}",
        "--skip=#{offset}", "--max-count=#{per_page}",
        format, "--numstat"
      )

      unless status.success?
        raise "Git log failed: #{stderr}"
      end

      commits = parse_git_log_output(stdout)

      {
        commits: commits,
        pagination: {
          current_page: page,
          per_page: per_page,
          total_count: total_count,
          total_pages: (total_count.to_f / per_page).ceil
        }
      }
    end
  end

  def get_git_commit_detail(repository, commit_hash)
    Dir.chdir(repository.local_path) do
      # Get commit info
      format = "--pretty=format:%H|%h|%an|%ae|%ai|%s|%b"
      stdout, stderr, status = Open3.capture3("git", "show", commit_hash, format, "--name-status")

      unless status.success?
        raise "Git show failed: #{stderr}"
      end

      lines = stdout.lines.map(&:strip)
      commit_line = lines.first

      if commit_line && commit_line.include?('|')
        hash, short_hash, author, email, date, subject, body = commit_line.split('|', 7)

        # Parse file changes
        file_lines = lines[1..-1].reject(&:empty?)
        files = parse_file_changes(file_lines)

        # Get detailed diff for each file
        files.each do |file|
          diff_stdout, diff_stderr, diff_status = Open3.capture3(
            "git", "show", commit_hash, "--", file[:path]
          )
          if diff_status.success?
            file[:diff] = diff_stdout
          end
        end

        # Calculate stats
        total_additions = files.sum { |f| f[:additions] }
        total_deletions = files.sum { |f| f[:deletions] }

        {
          commit: {
            hash: hash,
            short_hash: short_hash,
            author: author,
            author_email: email,
            date: date,
            subject: subject,
            body: body&.strip,
            message: [subject, body].compact.join("\n\n").strip
          },
          files: files,
          stats: {
            total_files: files.length,
            total_additions: total_additions,
            total_deletions: total_deletions
          }
        }
      else
        raise "Invalid commit format"
      end
    end
  end

  def get_git_commit_diff(repository, commit_hash, format, context)
    Dir.chdir(repository.local_path) do
      case format
      when 'name-only'
        stdout, stderr, status = Open3.capture3("git", "show", commit_hash, "--name-only", "--pretty=format:")
      when 'stat'
        stdout, stderr, status = Open3.capture3("git", "show", commit_hash, "--stat", "--pretty=format:")
      else # unified
        stdout, stderr, status = Open3.capture3("git", "show", commit_hash, "--unified=#{context}")
      end

      unless status.success?
        raise "Git diff failed: #{stderr}"
      end

      { diff: stdout, format: format }
    end
  end

  def get_git_file_content(repository, commit_hash, file_path)
    Dir.chdir(repository.local_path) do
      stdout, stderr, status = Open3.capture3("git", "show", "#{commit_hash}:#{file_path}")

      unless status.success?
        raise "Git show file failed: #{stderr}"
      end

      stdout
    end
  end

  def get_git_commit_files_content(repository, commit_hash)
    # Dir.chdir을 사용하는 대신 Open3.capture3의 :chdir 옵션을 사용하여
    # 현재 프로세스의 작업 디렉토리를 변경하지 않고 안전하게 git 명령을 실행합니다.
    # 이는 특히 development 환경에서 FileUpdateChecker와의 충돌을 방지합니다.

    # 먼저 커밋에서 변경된 파일 목록을 가져옴
    format = "--pretty=format:%H|%h|%an|%ae|%ai|%s|%b"
    git_show_cmd = ["git", "show", commit_hash, format, "--name-status"]
    stdout, stderr, status = Open3.capture3(*git_show_cmd, chdir: repository.local_path)

    unless status.success?
      raise "Git show failed: #{stderr}"
    end

    lines = stdout.lines.map(&:strip)

    # 파일 변경 정보 파싱
    file_lines = lines[1..-1].reject(&:empty?)
    files_with_content = []
    total_size = 0

    file_lines.each do |line|
      next if line.empty?

      parts = line.split("\t")
      next if parts.length < 2

      status_char = parts[0]
      file_path = parts[1]

      # 삭제된 파일은 내용을 가져올 수 없으므로 스킵
      next if status_char == 'D'

      # 바이너리 파일 확장자 체크 (기본적인 것들만)
      binary_extensions = %w[.jpg .jpeg .png .gif .bmp .ico .pdf .zip .tar .gz .exe .dll .so .dylib .a .o]
      next if binary_extensions.any? { |ext| file_path.downcase.end_with?(ext) }

      begin
        # 파일 내용 가져오기
        git_show_file_cmd = ["git", "show", "#{commit_hash}:#{file_path}"]
        content_stdout, content_stderr, content_status = Open3.capture3(*git_show_file_cmd, chdir: repository.local_path)

        if content_status.success?
          content = content_stdout

          # 바이너리 파일 체크 (null 바이트 포함 여부)
          next if content.include?("\0")

          # 너무 큰 파일은 제한 (1MB 이상)
          next if content.bytesize > 1_048_576

          files_with_content << {
            path: file_path,
            status: status_char,
            content: content,
            size: content.bytesize
          }

          total_size += content.bytesize
        end
      rescue => e
        Rails.logger.warn "Failed to get content for file #{file_path}: #{e.message}"
        next
      end
    end

    {
      files: files_with_content,
      total_size: total_size,
      total_files: files_with_content.length
    }
  end

  def parse_git_log_output(output)
    commits = []
    current_commit = nil

    output.lines.each do |line|
      line = line.strip
      next if line.empty?

      if line.include?('|') && line.split('|').length >= 6
        # Commit line
        if current_commit
          commits << current_commit
        end

        hash, short_hash, author, email, date, subject, body = line.split('|', 7)

        # Clean up body (remove empty lines and parent commit hashes)
        clean_body = body&.strip
        clean_body = nil if clean_body.blank? || clean_body.match(/^[a-f0-9]{40}$/)

        current_commit = {
          hash: hash,
          short_hash: short_hash,
          author: author,
          author_email: email,
          date: date,
          subject: subject,
          body: clean_body,
          message: [subject, clean_body].compact.join("\n\n").strip,
          files_changed: 0,
          insertions: 0,
          deletions: 0
        }
      elsif line.match(/^\d+\s+\d+\s+/)
        # Numstat line: additions deletions filename
        parts = line.split(/\s+/, 3)
        if parts.length >= 3 && current_commit
          additions = parts[0] == '-' ? 0 : parts[0].to_i
          deletions = parts[1] == '-' ? 0 : parts[1].to_i

          current_commit[:files_changed] += 1
          current_commit[:insertions] += additions
          current_commit[:deletions] += deletions
        end
      end
    end

    if current_commit
      commits << current_commit
    end

    commits
  end

  def parse_file_changes(lines)
    files = []

    lines.each do |line|
      next if line.empty?

      # Parse git status format: M filename or A filename, etc.
      if line.match(/^([AMDRC])\s+(.+)$/)
        status_char = $1
        filename = $2

        status = case status_char
                when 'A' then 'added'
                when 'M' then 'modified'
                when 'D' then 'deleted'
                when 'R' then 'renamed'
                when 'C' then 'copied'
                else 'modified'
                end

        files << {
          path: filename,
          status: status,
          additions: 0,
          deletions: 0,
          changes: 0
        }
      end
    end

    files
  end

  def repository_summary(repository)
    {
      id: repository.id,
      name: repository.name,
      path: repository.path,
      local_path: repository.local_path.to_s,
      url: repository.url,
      description: repository.description,
      status: repository.status,
      clone_status: repository.clone_status,
      current_branch: repository.current_branch,
      branches: repository.branches || [],
      active: repository.active,
      last_scan_at: repository.last_scan_at,
      last_indexed_at: repository.last_indexed_at,
      total_files: repository.total_files_count,
      total_lines: repository.total_lines_count,
      created_at: repository.created_at,
      updated_at: repository.updated_at
    }
  end

  def repository_detail(repository)
    repository_summary(repository).merge({
      languages: repository.languages,
      file_extensions: repository.file_extensions,
      error_message: repository.error_message,
      metadata: repository.metadata,
      aiend: {
        last_sync_at: repository.last_aiend_sync_at,
        sync_status: repository.aiend_sync_status,
        sync_error: repository.aiend_sync_error,
        last_scan_at: repository.last_aiend_scan_at,
        scan_status: repository.aiend_scan_status,
        scan_progress: repository.aiend_scan_progress,
        scan_message: repository.aiend_scan_message,
        scan_error: repository.aiend_scan_error,
        task_id: repository.aiend_task_id,
        last_scan_completed_at: repository.last_aiend_scan_completed_at
      }
    })
  end
end
