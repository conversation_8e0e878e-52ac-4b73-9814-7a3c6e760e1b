# app/controllers/api/search_controller.rb
module Api
    class SearchController < ApplicationController
      # POST /api/search
      def create
        query = params.require(:query)
        collection_name = params.fetch(:collection, 'codebase_files') # 기본 컬렉션 이름
        top_k = params.fetch(:top_k, 10).to_i
        
        # 1. 쿼리 임베딩
        embedding = EmbeddingService.embed(query)
  
        # 2. Qdrant 벡터 검색
        search_results = QdrantService.search(
          collection_name: collection_name,
          vector: embedding,
          limit: top_k
        )
  
        # 3. 결과 리랭킹
        reranked_results = RerankerService.rerank(query, search_results)
  
        # 4. 최종 결과 렌더링
        render json: {
          query: query,
          results: reranked_results.map do |item|
            {
              score: item[:score],
              payload: item[:document].payload,
              id: item[:document].id
            }
          end
        }
      rescue ActionController::ParameterMissing => e
        render json: { error: e.message }, status: :bad_request
      rescue StandardError => e
        render json: { error: "An unexpected error occurred: #{e.message}" }, status: :internal_server_error
      end
    end
  end