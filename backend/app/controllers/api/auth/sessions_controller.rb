module Api
  module Auth
    class Sessions<PERSON>ontroller < ApplicationController
      skip_before_action :verify_authenticity_token

      def create
        Rails.logger.info "Login attempt with params: #{params.inspect}"

        begin
          # Handle both nested and flat parameter structures
          login_params = params[:email].present? ? params : params[:user] || {}
          Rails.logger.info "Login params: #{login_params.inspect}"

          user = User.find_by(email: login_params[:email])

          if user.nil?
            Rails.logger.warn "User not found for email: #{login_params[:email]}"
            render json: {
              status: { code: 401, message: 'User not found.' },
              errors: ['User not found. Please check your email or sign up for a new account.'],
              success: false
            }, status: :unauthorized
            return
          end

          unless user.valid_password?(login_params[:password])
            Rails.logger.warn "Authentication failed for user: #{user.email}"
            render json: {
              status: { code: 401, message: 'Invalid password.' },
              errors: ['Incorrect password. Please check your password and try again.'],
              success: false
            }, status: :unauthorized
            return
          end

          Rails.logger.info "Login successful for user: #{user.email}"

          # Generate JWT token
          token = JWT.encode(
            {
              user_id: user.id,
              email: user.email,
              exp: 24.hours.from_now.to_i
            },
            Rails.application.secret_key_base,
            'HS256'
          )

          serialized_data = UserSerializer.new(user).serializable_hash

          render json: {
            status: { code: 200, message: 'Logged in successfully.' },
            data: serialized_data[:data][:attributes],
            access_token: token,
            token_type: 'bearer',
            csrf_token: form_authenticity_token,
            success: true
          }
        rescue => e
          Rails.logger.error "Login error: #{e.class} - #{e.message}"
          Rails.logger.error e.backtrace.join("\n")
          render json: {
            status: { code: 500, message: 'Internal server error' },
            errors: [e.message],
            success: false
          }, status: :internal_server_error
        end
      end

      def destroy
        render json: {
          status: { code: 200, message: "Logged out successfully." },
          success: true
        }, status: :ok
      end
    end
  end
end
