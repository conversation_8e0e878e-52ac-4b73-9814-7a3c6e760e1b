module Api
  module Auth
    class UsersController < ApplicationController

      def me
        # Check if user is logged in via session
        user_id = session[:user_id] || warden&.user&.id

        if user_id
          user = User.find_by(id: user_id)
          if user
            render json: {
              success: true,
              data: UserSerializer.new(user).serializable_hash[:data][:attributes]
            }, status: :ok
          else
            render json: {
              success: false,
              error: 'User not found'
            }, status: :unauthorized
          end
        else
          render json: {
            success: false,
            error: 'Not authenticated'
          }, status: :unauthorized
        end
      end
    end
  end
end
