class Api::FileExplorerController < Api::BaseController
  before_action :set_repository

  # GET /api/repositories/:repository_id/explorer/tree
  def tree
    options = {
      include_files: params[:include_files] != 'false',
      max_depth: params[:max_depth]&.to_i,
      lazy_load: params[:lazy_load] == 'true'
    }
    
    tree_data = Directory.build_tree(@repository, options)
    render_success({ 
      tree: tree_data,
      repository: {
        id: @repository.id,
        name: @repository.name,
        current_branch: @repository.current_branch
      },
      stats: {
        total_files: @repository.total_files_count,
        total_directories: @repository.directories.count,
        total_size: @repository.total_size,
        languages: @repository.languages
      }
    })
  end

  # GET /api/repositories/:repository_id/explorer/directory/:path
  def directory
    path = params[:path] || ''
    directory = @repository.directories.find_by(relative_path: path)
    
    unless directory
      render_not_found('Directory not found')
      return
    end

    # Get immediate children (files and subdirectories)
    subdirectories = @repository.directories
                                .where('relative_path LIKE ? AND depth = ?', 
                                       "#{path}/%", directory.depth + 1)
                                .order(:name)

    files = directory.files.order(:name)

    children = []
    
    # Add subdirectories
    subdirectories.each do |subdir|
      children << {
        id: "dir_#{subdir.id}",
        name: subdir.name,
        type: 'directory',
        path: subdir.relative_path,
        has_children: subdir.files.exists? || 
                     @repository.directories.where('relative_path LIKE ?', "#{subdir.relative_path}/%").exists?,
        file_count: subdir.files.count
      }
    end

    # Add files
    files.each do |file|
      children << {
        id: "file_#{file.id}",
        name: file.name,
        type: 'file',
        path: file.relative_path,
        size: file.size_bytes,
        lines: file.lines_count,
        language: file.language,
        extension: file.extension,
        is_binary: file.is_binary,
        last_modified: file.last_modified_at
      }
    end

    render_success({
      directory: {
        id: "dir_#{directory.id}",
        name: directory.name,
        path: directory.relative_path,
        depth: directory.depth
      },
      children: children,
      total_children: children.length
    })
  end

  # GET /api/repositories/:repository_id/explorer/file/:id
  def file
    file = @repository.files.find(params[:id])
    
    render_success({
      file: {
        id: file.id,
        name: file.name,
        path: file.relative_path,
        full_path: file.path,
        size: file.size_bytes,
        lines: file.lines_count,
        language: file.language,
        extension: file.extension,
        encoding: file.encoding,
        is_binary: file.is_binary,
        last_modified: file.last_modified_at,
        sha256_hash: file.sha256_hash
      }
    })
  rescue ActiveRecord::RecordNotFound
    render_not_found('File not found')
  end

  # GET /api/repositories/:repository_id/explorer/file/:id/content
  def file_content
    file = @repository.files.find(params[:id])
    
    if file.is_binary?
      render_error('Cannot display binary file content', :unprocessable_entity)
      return
    end

    render_success({
      content: file.content,
      file: {
        id: file.id,
        name: file.name,
        path: file.relative_path,
        language: file.language,
        encoding: file.encoding,
        lines: file.lines_count,
        size: file.size_bytes
      }
    })
  rescue ActiveRecord::RecordNotFound
    render_not_found('File not found')
  end

  # PUT /api/repositories/:repository_id/explorer/file/:id/content
  def update_file_content
    file = @repository.files.find(params[:id])
    
    if file.is_binary?
      render_error('Cannot edit binary files', :unprocessable_entity)
      return
    end

    new_content = params[:content]
    if new_content.nil?
      render_error('Content parameter is required', :bad_request)
      return
    end

    begin
      # Write to actual file on disk
      File.write(file.path, new_content)
      
      # Update database record
      file.update!(
        content: new_content,
        lines_count: new_content.lines.count,
        size_bytes: new_content.bytesize,
        last_modified_at: Time.current,
        sha256_hash: Digest::SHA256.hexdigest(new_content)
      )

      render_success({
        message: 'File updated successfully',
        file: {
          id: file.id,
          name: file.name,
          path: file.relative_path,
          lines: file.lines_count,
          size: file.size_bytes,
          last_modified: file.last_modified_at
        }
      })
    rescue => e
      Rails.logger.error "Failed to update file: #{e.message}"
      render_error("Failed to update file: #{e.message}", :internal_server_error)
    end
  rescue ActiveRecord::RecordNotFound
    render_not_found('File not found')
  end

  # GET /api/repositories/:repository_id/explorer/search
  def search
    query = params[:q]
    search_type = params[:type] || 'all' # 'content', 'filename', 'path', 'all'
    
    if query.blank?
      render_error('Search query is required', :bad_request)
      return
    end

    results = case search_type
              when 'content'
                search_file_content(query)
              when 'filename'
                search_filenames(query)
              when 'path'
                search_file_paths(query)
              else
                search_all(query)
              end

    render_success({
      results: results,
      query: query,
      search_type: search_type,
      total_results: results.length
    })
  end

  private

  def set_repository
    @repository = Repository.find(params[:repository_id])
  rescue ActiveRecord::RecordNotFound
    render_not_found('Repository not found')
  end

  def search_file_content(query)
    @repository.files
               .where('content LIKE ? AND is_binary = ?', "%#{query}%", false)
               .limit(50)
               .map { |file| format_search_result(file, 'content') }
  end

  def search_filenames(query)
    @repository.files
               .where('name LIKE ?', "%#{query}%")
               .limit(50)
               .map { |file| format_search_result(file, 'filename') }
  end

  def search_file_paths(query)
    @repository.files
               .where('relative_path LIKE ?', "%#{query}%")
               .limit(50)
               .map { |file| format_search_result(file, 'path') }
  end

  def search_all(query)
    content_results = search_file_content(query)
    filename_results = search_filenames(query)
    path_results = search_file_paths(query)
    
    # Remove duplicates and limit total results
    all_results = (content_results + filename_results + path_results)
                    .uniq { |result| result[:file][:id] }
                    .first(50)
    
    all_results
  end

  def format_search_result(file, match_type)
    {
      file: {
        id: file.id,
        name: file.name,
        path: file.relative_path,
        language: file.language,
        size: file.size_bytes,
        lines: file.lines_count
      },
      match_type: match_type,
      directory: File.dirname(file.relative_path)
    }
  end
end
