class Api::FilesController < Api::BaseController
  before_action :set_repository, only: [:index, :search]
  before_action :set_file, only: [:show, :content]

  def index
    files = @repository.files.includes(:directory)
    
    # Apply filters
    files = files.by_extension(params[:extension]) if params[:extension].present?
    files = files.by_language(params[:language]) if params[:language].present?
    files = files.text_files if params[:text_only] == 'true'
    files = files.binary_files if params[:binary_only] == 'true'
    
    # Apply sorting
    case params[:sort_by]
    when 'name'
      files = files.order(:name)
    when 'size'
      files = files.order(:size_bytes)
    when 'modified'
      files = files.order(:last_modified_at)
    when 'lines'
      files = files.order(:lines_count)
    else
      files = files.order(:relative_path)
    end
    
    files = files.reverse_order if params[:sort_direction] == 'desc'

    result = paginate_collection(
      files,
      page: params[:page],
      per_page: params[:per_page]
    )

    render_success({
      files: result[:data].map { |file| file_summary(file) },
      pagination: result[:pagination]
    })
  end

  def show
    render_success({
      file: file_detail(@file)
    })
  end

  def content
    if @file.is_binary?
      render_error('Cannot display binary file content')
      return
    end

    render_success({
      content: @file.content,
      encoding: @file.encoding,
      lines_count: @file.lines_count
    })
  end

  def search
    query = params[:q]
    
    if query.blank?
      render_error('Search query is required')
      return
    end

    # Determine search type
    search_type = params[:type] || 'all'
    
    files = case search_type
            when 'content'
              @repository.files.search_content(query)
            when 'path'
              @repository.files.search_by_path(query)
            else
              @repository.files.search(query)
            end

    # Apply additional filters
    files = files.by_extension(params[:extension]) if params[:extension].present?
    files = files.by_language(params[:language]) if params[:language].present?
    files = files.text_files if params[:text_only] == 'true'

    result = paginate_collection(
      files,
      page: params[:page],
      per_page: params[:per_page]
    )

    # Add search highlights for content search
    files_with_highlights = result[:data].map do |file|
      file_data = file_summary(file)
      
      if search_type == 'content' && file.content.present?
        file_data[:highlights] = extract_highlights(file.content, query)
      end
      
      file_data
    end

    render_success({
      files: files_with_highlights,
      pagination: result[:pagination],
      query: query,
      search_type: search_type
    })
  end

  def global_search
    query = params[:q]
    
    if query.blank?
      render_error('Search query is required')
      return
    end

    # Search across all repositories
    files = CodeFile.joins(:repository)
                    .where(repositories: { active: true })
                    .search(query)

    # Apply filters
    files = files.by_extension(params[:extension]) if params[:extension].present?
    files = files.by_language(params[:language]) if params[:language].present?

    result = paginate_collection(
      files,
      page: params[:page],
      per_page: params[:per_page]
    )

    files_with_repo = result[:data].map do |file|
      file_summary(file).merge({
        repository: {
          id: file.repository.id,
          name: file.repository.name
        }
      })
    end

    render_success({
      files: files_with_repo,
      pagination: result[:pagination],
      query: query
    })
  end

  private

  def set_repository
    @repository = Repository.find(params[:repository_id])
  rescue ActiveRecord::RecordNotFound
    render_not_found('Repository not found')
  end

  def set_file
    @file = CodeFile.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render_not_found('File not found')
  end

  def file_summary(file)
    {
      id: file.id,
      name: file.name,
      relative_path: file.relative_path,
      extension: file.extension,
      language: file.language,
      size_bytes: file.size_bytes,
      size_human: file.size_human,
      lines_count: file.lines_count,
      is_binary: file.is_binary,
      file_type: file.file_type,
      last_modified_at: file.last_modified_at,
      directory: file.directory ? {
        id: file.directory.id,
        name: file.directory.name,
        relative_path: file.directory.relative_path
      } : nil
    }
  end

  def file_detail(file)
    file_summary(file).merge({
      path: file.path,
      encoding: file.encoding,
      mime_type: file.mime_type,
      sha256_hash: file.sha256_hash,
      metadata: file.metadata,
      readable: file.readable?,
      created_at: file.created_at,
      updated_at: file.updated_at
    })
  end

  def extract_highlights(content, query)
    return [] if content.blank? || query.blank?

    lines = content.lines
    highlights = []
    
    lines.each_with_index do |line, index|
      if line.downcase.include?(query.downcase)
        highlights << {
          line_number: index + 1,
          content: line.strip,
          start_pos: line.downcase.index(query.downcase),
          end_pos: line.downcase.index(query.downcase) + query.length
        }
      end
    end
    
    highlights.first(10) # Limit to first 10 matches
  end
end
