class Api::BaseController < ApplicationController
  # API controllers don't need CSRF protection

  # Set JSON as default response format
  before_action :set_default_response_format
  before_action :set_no_cache_headers

  def health
    render_success({ status: 'ok', timestamp: Time.current })
  end

  protected

  def set_no_cache_headers
    # API 응답은 기본적으로 캐시하지 않음
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    response.headers['ETag'] = nil
  end

  def render_success(data = nil, message = nil, status = :ok)
    response = { success: true }
    response[:data] = data if data
    response[:message] = message if message
    
    render json: response, status: status
  end

  def render_error(message, status = :bad_request, errors = nil)
    response = { 
      success: false, 
      error: message 
    }
    response[:errors] = errors if errors
    
    render json: response, status: status
  end

  def render_not_found(message = 'Resource not found')
    render_error(message, :not_found)
  end

  def render_validation_errors(record)
    render_error(
      'Validation failed',
      :unprocessable_entity,
      record.errors.full_messages
    )
  end

  def paginate_collection(collection, page: 1, per_page: 20)
    page = [page.to_i, 1].max
    per_page = [[per_page.to_i, 1].max, 100].min
    
    offset = (page - 1) * per_page
    total = collection.count
    
    {
      data: collection.limit(per_page).offset(offset),
      pagination: {
        current_page: page,
        per_page: per_page,
        total_count: total,
        total_pages: (total.to_f / per_page).ceil
      }
    }
  end

  private

  def set_default_response_format
    request.format = :json unless params[:format]
  end
end
