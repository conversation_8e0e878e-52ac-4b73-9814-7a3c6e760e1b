class ApplicationController < ActionController::API
  include ActionController::RequestForgeryProtection
  include ActionController::Cookies

  # Include Devise helpers for authentication
  before_action :configure_permitted_parameters, if: :devise_controller?

  protect_from_forgery with: :null_session
  skip_before_action :verify_authenticity_token, if: :api_request?

  private

  def api_request?
    request.format.json? || request.path.start_with?('/api/')
  end

  def configure_permitted_parameters
    devise_parameter_sanitizer.permit(:sign_in, keys: [:email, :password])
  end
end
