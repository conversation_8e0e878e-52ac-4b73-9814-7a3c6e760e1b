# app/services/embedding_service.rb
class EmbeddingService
  include HTTParty

  # Docker 컨테이너에서 호스트의 Ollama에 접근하기 위한 설정
  # host.docker.internal은 Docker 컨테이너에서 호스트 머신을 참조하는 특수 DNS 이름입니다.
  # docker-compose.yml에 OLLAMA_HOST 환경 변수를 추가해야 합니다.
  base_uri ENV.fetch('OLLAMA_HOST', 'http://host.docker.internal:11434')

  # 임베딩 모델 설정 - 환경 변수 또는 기본값 사용
  EMBEDDING_MODEL = ENV.fetch('CODEBASE_VECTOR_MODEL', 'dengcao/Qwen3-Embedding-8B:Q5_K_M')

  # 텍스트를 임베딩 벡터로 변환합니다.
  def self.embed(text)
    Rails.logger.info("[EmbeddingService] Generating embedding for text: #{text.truncate(50)}")

    # Ollama의 /api/embeddings 엔드포인트를 사용합니다.
    response = post('/api/embeddings', 
      body: {
        model: EMBEDDING_MODEL,
        prompt: text
      }.to_json, 
      headers: { 'Content-Type' => 'application/json' },
      timeout: 60
    )

    if response.success?
      JSON.parse(response.body)['embedding']
    else
      error_message = "[EmbeddingService] Failed to get embedding. Status: #{response.code}, Body: #{response.body}"
      Rails.logger.error(error_message)
      raise StandardError, error_message
    end
  rescue Net::ReadTimeout => e
    error_message = "[EmbeddingService] Ollama connection timed out: #{e.message}"
    Rails.logger.error(error_message)
    raise StandardError, error_message
  rescue StandardError => e
    error_message = "[EmbeddingService] An error occurred: #{e.message}"
    Rails.logger.error(error_message)
    raise StandardError, error_message
  end
end
