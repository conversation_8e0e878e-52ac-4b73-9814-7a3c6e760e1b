# app/services/reranker_service.rb
class RerankerService
    include HTTParty
  
    # Docker 컨테이너에서 호스트의 Ollama에 접근
    base_uri ENV.fetch('OLLAMA_HOST', 'http://host.docker.internal:11434')
  
    # 리랭커 모델 설정
    RERANKER_MODEL = ENV.fetch('RERANKER_MODEL', 'dengcao/Qwen3-Reranker-4B:Q5_K_M')
  
    # 쿼리와 문서를 받아 리랭킹된 결과를 반환
    def self.rerank(query, documents)
      Rails.logger.info("[RerankerService] Reranking #{documents.count} documents for query: #{query.truncate(50)}")
  
      scored_documents = documents.map do |doc|
        # Qdrant 검색 결과 객체에서 원문 텍스트를 추출
        document_text = doc.payload['text']
        
        # Dengcao 리랭커 모델의 프롬프트 형식
        prompt = "[Q] #{query}\n[D] #{document_text}"
  
        begin
          response = post('/api/generate',
            body: {
              model: RERANKER_MODEL,
              prompt: prompt,
              stream: false
            }.to_json,
            headers: { 'Content-Type' => 'application/json' },
            timeout: 30
          )
  
          if response.success?
            # 응답에서 점수(숫자)만 추출
            score = response.parsed_response['response'].to_f
            { document: doc, score: score }
          else
            Rails.logger.error("[RerankerService] Failed to get score for doc. Status: #{response.code}")
            { document: doc, score: 0.0 } # 실패 시 점수 0점 부여
          end
        rescue StandardError => e
          Rails.logger.error("[RerankerService] Error during reranking: #{e.message}")
          { document: doc, score: 0.0 }
        end
      end
  
      # 점수가 높은 순으로 정렬
      scored_documents.sort_by { |item| -item[:score] }
    end
  end