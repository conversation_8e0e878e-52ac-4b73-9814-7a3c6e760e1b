# app/services/qdrant_service.rb
require 'qdrant'

class QdrantService
  # Qdrant 클라이언트 인스턴스를 반환
  def self.client
    # docker-compose.yml에 설정된 QDRANT_URL을 사용
    qdrant_url = ENV.fetch('QDRANT_URL', 'http://qdrant:6333')
    @client ||= Qdrant::Client.new(url: qdrant_url)
  end

  # 벡터 검색 수행
  def self.search(collection_name:, vector:, limit: 10)
    Rails.logger.info("[QdrantService] Searching in collection '#{collection_name}' with limit #{limit}")
    
    begin
      client.points.search(
        collection_name: collection_name,
        limit: limit,
        vector: vector,
        with_payload: true # 원문 텍스트 등 payload 정보 포함
      )
    rescue StandardError => e
      Rails.logger.error("[QdrantService] Search failed: #{e.message}")
      [] # 오류 발생 시 빈 배열 반환
    end
  end
end