class RepositoryScannerService
  attr_reader :repository

  def initialize(repository)
    @repository = repository
  end

  def scan
    return unless File.directory?(repository.path)

    # Clear existing data
    clear_existing_data

    # Scan directory structure and files
    scan_directory_structure
    
    # Scan git commits if it's a git repository
    scan_git_commits if git_repository?

    Rails.logger.info "Repository scan completed: #{repository.name}"
  end

  private

  def clear_existing_data
    repository.files.delete_all
    repository.directories.delete_all
    repository.commits.delete_all
  end

  def scan_directory_structure
    base_path = Pathname.new(repository.path)
    directory_cache = {}

    Dir.glob(File.join(repository.path, '**', '*'), File::FNM_DOTMATCH).each do |full_path|
      next if should_skip_path?(full_path)

      path_obj = Pathname.new(full_path)
      relative_path = path_obj.relative_path_from(base_path).to_s

      if File.directory?(full_path)
        create_directory(full_path, relative_path, directory_cache)
      else
        create_file(full_path, relative_path, directory_cache)
      end
    end
  end

  def create_directory(full_path, relative_path, directory_cache)
    return if directory_cache[relative_path]

    parent_dir = nil
    parent_path = File.dirname(relative_path)
    depth = relative_path.split('/').length - 1

    if parent_path != '.' && parent_path != relative_path
      parent_dir = directory_cache[parent_path] || create_directory_recursive(parent_path, directory_cache)
    end

    directory = repository.directories.create!(
      name: safe_encode_string(File.basename(full_path)),
      path: safe_encode_string(full_path),
      relative_path: safe_encode_string(relative_path),
      parent: parent_dir,
      depth: depth
    )

    directory_cache[relative_path] = directory
    directory
  end

  def create_directory_recursive(relative_path, directory_cache)
    return directory_cache[relative_path] if directory_cache[relative_path]

    parent_dir = nil
    parent_path = File.dirname(relative_path)
    depth = relative_path.split('/').length - 1

    if parent_path != '.' && parent_path != relative_path
      parent_dir = create_directory_recursive(parent_path, directory_cache)
    end

    full_path = File.join(repository.path, relative_path)
    directory = repository.directories.create!(
      name: safe_encode_string(File.basename(relative_path)),
      path: safe_encode_string(full_path),
      relative_path: safe_encode_string(relative_path),
      parent: parent_dir,
      depth: depth
    )

    directory_cache[relative_path] = directory
    directory
  end

  def create_file(full_path, relative_path, directory_cache)
    # 바이너리 파일은 DB에 저장하지 않고 건너뛰어 불필요한 오류를 방지합니다.
    return if binary_file?(full_path)

    # Get or create parent directory
    parent_dir = nil
    dir_path = File.dirname(relative_path)
    
    if dir_path != '.'
      parent_dir = directory_cache[dir_path] || create_directory_recursive(dir_path, directory_cache)
    end

    # Read file content and metadata
    file_stats = File.stat(full_path)
    is_binary = binary_file?(full_path)
    content = is_binary ? nil : read_file_content(full_path)
    
    # Ensure all string fields are safe for database storage
    safe_content = content
    if content && !content.valid_encoding?
      safe_content = content.encode('UTF-8', invalid: :replace, undef: :replace, replace: '?')
    end

    # Safely encode all path-related strings
    safe_name = safe_encode_string(File.basename(full_path))
    safe_full_path = safe_encode_string(full_path)
    safe_relative_path = safe_encode_string(relative_path)

    repository.files.create!(
      name: safe_name,
      path: safe_full_path,
      relative_path: safe_relative_path,
      directory: parent_dir,
      content: safe_content,
      size_bytes: file_stats.size,
      lines_count: safe_content ? safe_content.lines.count : 0,
      is_binary: is_binary,
      last_modified_at: file_stats.mtime,
      encoding: safe_content ? safe_content.encoding.name : nil,
      sha256_hash: Digest::SHA256.file(full_path).hexdigest
    )
  end

  def scan_git_commits
    return unless git_repository?

    # This would require the 'rugged' gem or git command line
    # For now, we'll implement a basic version using git command line
    scan_git_commits_with_cli
  end

  def scan_git_commits_with_cli
    Dir.chdir(repository.path) do
      # Get commit log
      log_output = `git log --pretty=format:"%H|%an|%ae|%cn|%ce|%ci|%s" --no-merges`

      # Ensure git output is properly encoded
      log_output = safe_encode_string(log_output)

      log_output.each_line do |line|
        parts = line.strip.split('|', 7)
        next if parts.length < 7

        sha, author_name, author_email, committer_name, committer_email, date_str, message = parts
        
        # Ensure all text fields are safe for database storage
        safe_message = safe_encode_string(message)
        safe_author_name = safe_encode_string(author_name)
        safe_author_email = safe_encode_string(author_email)
        safe_committer_name = safe_encode_string(committer_name)
        safe_committer_email = safe_encode_string(committer_email)

        repository.commits.create!(
          sha: sha,
          message: safe_message,
          author_name: safe_author_name,
          author_email: safe_author_email,
          committer_name: safe_committer_name,
          committer_email: safe_committer_email,
          committed_at: Time.parse(date_str)
        )
      end
    end
  rescue => e
    Rails.logger.warn "Failed to scan git commits: #{e.message}"
  end

  def git_repository?
    File.directory?(File.join(repository.path, '.git'))
  end

  def should_skip_path?(path)
    basename = File.basename(path)

    # .git 디렉토리 내부의 모든 경로는 스캔에서 제외합니다.
    return true if path.include?('/.git/') || path.end_with?('/.git')

    # Skip other specific hidden files/directories
    skip_hidden = %w[.DS_Store .Trash .cache .tmp .]
    return true if skip_hidden.include?(basename)

    # Skip common build/cache directories
    skip_dirs = %w[node_modules vendor tmp log coverage dist build .next .nuxt __pycache__ .pytest_cache]
    return true if skip_dirs.any? { |dir| path.include?("/#{dir}/") || path.end_with?("/#{dir}") }

    # Skip temporary and backup files
    return true if basename.end_with?('~', '.tmp', '.bak', '.swp', '.swo')

    false
  end

  def binary_file?(file_path)
    # Simple binary detection - check for null bytes in first 1KB
    File.open(file_path, 'rb') do |file|
      chunk = file.read(1024)
      return false if chunk.nil? || chunk.empty?
      chunk.include?("\x00")
    end
  rescue
    true # If we can't read it, assume it's binary
  end

  def read_file_content(file_path)
    # Limit file size to prevent memory issues
    max_size = 1.megabyte
    return nil if File.size(file_path) > max_size

    # First try UTF-8
    begin
      content = File.read(file_path, encoding: 'UTF-8')
      return content if content.valid_encoding?
    rescue Encoding::InvalidByteSequenceError, Encoding::UndefinedConversionError
      # Fall through to binary reading
    end

    # Try reading as binary and convert to UTF-8
    begin
      content = File.read(file_path, encoding: 'BINARY')

      # Try to detect encoding and convert
      if content.encoding == Encoding::BINARY
        # Try common encodings
        ['UTF-8', 'EUC-KR', 'CP949', 'ISO-8859-1'].each do |encoding_name|
          begin
            converted = content.dup.force_encoding(encoding_name)
            if converted.valid_encoding?
              return converted.encode('UTF-8', invalid: :replace, undef: :replace, replace: '?')
            end
          rescue Encoding::ConverterNotFoundError, Encoding::InvalidByteSequenceError
            next
          end
        end
      end

      # If all else fails, force UTF-8 and replace invalid characters
      content.force_encoding('UTF-8')
      return content.encode('UTF-8', invalid: :replace, undef: :replace, replace: '?')

    rescue => e
      Rails.logger.warn "Failed to read file #{file_path}: #{e.message}"
      return nil
    end
  rescue => e
    Rails.logger.warn "Failed to read file #{file_path}: #{e.message}"
    nil
  end

  def safe_encode_string(str)
    return nil if str.nil?
    return str if str.valid_encoding?

    # Try to fix encoding issues
    str.encode('UTF-8', invalid: :replace, undef: :replace, replace: '?')
  rescue => e
    Rails.logger.warn "Failed to encode string: #{e.message}"
    str.to_s.force_encoding('UTF-8').encode('UTF-8', invalid: :replace, undef: :replace, replace: '?')
  end
end
