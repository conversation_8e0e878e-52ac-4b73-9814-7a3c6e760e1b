require 'open3'

class AiendWebhookService
  include HTTParty

  # Aiend 서버 설정
  AIEND_BASE_URL = ENV.fetch('AIEND_BASE_URL', 'http://localhost:7102')
  WEBHOOK_TIMEOUT = 30 # seconds
  
  def self.notify_git_changes(repository, action, changed_files: nil, commit_hash: nil)
    # Git 변경사항을 Aiend에 알림
    #
    # Args:
    #   repository: Repository 모델 인스턴스
    #   action: 액션 타입 ('clone', 'pull', 'branch_switch')
    #   changed_files: 변경된 파일 목록 (선택사항)
    #   commit_hash: 커밋 해시 (선택사항)
    
    return false unless aiend_available?
    
    begin
      payload = {
        repository_id: repository.id.to_s,
        repository_name: repository.name,
        repository_path: repository.local_path.to_s,
        action: action,
        branch: repository.current_branch || 'main',
        commit_hash: commit_hash,
        changed_files: changed_files,
        timestamp: Time.current.iso8601,
        metadata: {
          repository_url: repository.url,
          clone_status: repository.clone_status,
          last_indexed_at: repository.last_indexed_at&.iso8601
        }
      }
      
      Rails.logger.info "Sending git change notification to Aiend: #{action} for #{repository.name}"
      Rails.logger.debug "Payload: #{payload.to_json}"
      
      response = HTTParty.post(
        "#{AIEND_BASE_URL}/api/v1/webhook/git-changes",
        body: payload.to_json,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        },
        timeout: WEBHOOK_TIMEOUT
      )
      
      if response.success?
        Rails.logger.info "Aiend webhook notification sent successfully"
        response.parsed_response
      else
        Rails.logger.error "Aiend webhook failed: #{response.code} - #{response.body}"
        false
      end
      
    rescue Net::TimeoutError => e
      Rails.logger.error "Aiend webhook timeout: #{e.message}"
      false
    rescue StandardError => e
      Rails.logger.error "Aiend webhook error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  def self.trigger_repository_scan(repository, force_reindex: false)
    # 저장소 전체 스캔을 Aiend에 요청
    #
    # Args:
    #   repository: Repository 모델 인스턴스
    #   force_reindex: 강제 재인덱싱 여부
    
    return false unless aiend_available?
    
    begin
      query_params = {
        repository_path: repository.local_path.to_s,
        repository_name: repository.name,
        force_reindex: force_reindex
      }

      Rails.logger.info "Triggering repository scan in Aiend for: #{repository.name}"

      response = HTTParty.post(
        "#{AIEND_BASE_URL}/api/v1/webhook/repository-scan",
        query: query_params,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        },
        timeout: WEBHOOK_TIMEOUT
      )
      
      if response.success?
        Rails.logger.info "Aiend repository scan triggered successfully"
        response.parsed_response
      else
        Rails.logger.error "Aiend repository scan trigger failed: #{response.code} - #{response.body}"
        false
      end
      
    rescue Net::TimeoutError => e
      Rails.logger.error "Aiend repository scan timeout: #{e.message}"
      false
    rescue StandardError => e
      Rails.logger.error "Aiend repository scan error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  def self.get_task_status(task_id)
    # Aiend 백그라운드 작업 상태 조회
    #
    # Args:
    #   task_id: 작업 ID
    
    return nil unless aiend_available?
    
    begin
      response = HTTParty.get(
        "#{AIEND_BASE_URL}/api/v1/webhook/status/#{task_id}",
        headers: {
          'Accept' => 'application/json'
        },
        timeout: WEBHOOK_TIMEOUT
      )
      
      if response.success?
        response.parsed_response
      else
        Rails.logger.error "Failed to get Aiend task status: #{response.code} - #{response.body}"
        nil
      end
      
    rescue StandardError => e
      Rails.logger.error "Aiend task status error: #{e.message}"
      nil
    end
  end
  
  def self.aiend_available?
    # Aiend 서비스 사용 가능 여부 확인
    
    # 환경 변수로 Aiend 사용 여부 제어
    return false unless ENV.fetch('AIEND_ENABLED', 'true') == 'true'
    
    begin
      response = HTTParty.get(
        "#{AIEND_BASE_URL}/health",
        timeout: 5
      )
      
      response.success?
    rescue StandardError => e
      Rails.logger.debug "Aiend not available: #{e.message}"
      false
    end
  end
  
  def self.health_check
    # Aiend 서비스 상태 확인
    
    begin
      response = HTTParty.get(
        "#{AIEND_BASE_URL}/health/detailed",
        timeout: 10
      )
      
      if response.success?
        {
          status: 'available',
          response: response.parsed_response
        }
      else
        {
          status: 'error',
          code: response.code,
          message: response.body
        }
      end
      
    rescue StandardError => e
      {
        status: 'unavailable',
        error: e.message
      }
    end
  end
  
  # 비동기 알림을 위한 백그라운드 작업 메서드들
  def self.notify_git_changes_async(repository_id, action, changed_files: nil, commit_hash: nil)
    # 비동기로 Git 변경사항 알림
    
    AiendNotificationJob.perform_later(
      repository_id,
      action,
      changed_files: changed_files,
      commit_hash: commit_hash
    )
  end
  
  def self.trigger_repository_scan_async(repository_id, force_reindex: false)
    # 비동기로 저장소 스캔 트리거

    AiendScanJob.perform_later(repository_id, force_reindex: force_reindex)
  end

  def self.trigger_repository_branch_scan(repository, branch, force_reindex: false)
    # 특정 브랜치 스캔을 Aiend에 요청
    #
    # Args:
    #   repository: Repository 모델 인스턴스
    #   branch: 브랜치명
    #   force_reindex: 강제 재인덱싱 여부

    return false unless aiend_available?

    begin
      # 먼저 해당 브랜치로 체크아웃 (자동 스캔 없이)
      Rails.logger.info "Switching to branch: #{branch} for repository: #{repository.name}"

      # 직접 브랜치 체크아웃 수행 (SwitchBranchJob 사용하지 않음)
      local_path = repository.local_path

      unless File.exist?(local_path)
        Rails.logger.error "Repository not found at: #{local_path}"
        return false
      end

      Dir.chdir(local_path) do
        # Fetch latest changes
        stdout, stderr, status = Open3.capture3("git", "fetch", "origin")
        unless status.success?
          Rails.logger.warn "Git fetch failed: #{stderr}"
        end

        # Switch to the branch
        stdout, stderr, status = Open3.capture3("git", "checkout", branch)
        unless status.success?
          # Try to create and checkout the branch if it doesn't exist locally
          stdout, stderr, status = Open3.capture3("git", "checkout", "-b", branch, "origin/#{branch}")
          unless status.success?
            Rails.logger.error "Failed to switch to branch '#{branch}': #{stderr}"
            return false
          end
        end

        # Pull latest changes
        stdout, stderr, status = Open3.capture3("git", "pull", "origin", branch)
        unless status.success?
          Rails.logger.warn "Git pull failed: #{stderr}"
        end

        Rails.logger.info "Successfully switched to branch: #{branch}"
      end

      # Repository 현재 브랜치 업데이트 (스캔 없이)
      repository.update!(current_branch: branch)

      # 브랜치 체크아웃이 완료될 때까지 잠시 대기
      sleep(1)

      query_params = {
        repository_path: repository.local_path.to_s,
        repository_name: repository.name,
        repository_id: repository.id.to_s,
        branch: branch,
        force_reindex: force_reindex
      }

      Rails.logger.info "Triggering repository branch scan in Aiend for: #{repository.name} (branch: #{branch})"

      response = HTTParty.post(
        "#{AIEND_BASE_URL}/api/v1/webhook/repository-scan",
        query: query_params,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        },
        timeout: WEBHOOK_TIMEOUT
      )

      if response.success?
        Rails.logger.info "Aiend repository branch scan triggered successfully"
        response.parsed_response
      else
        Rails.logger.error "Aiend repository branch scan trigger failed: #{response.code} - #{response.body}"
        false
      end

    rescue Net::TimeoutError => e
      Rails.logger.error "Aiend repository branch scan timeout: #{e.message}"
      false
    rescue StandardError => e
      Rails.logger.error "Aiend repository branch scan error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  def self.trigger_repository_branch_scan(repository_branch_id, force_reindex: false)
    
    return false unless aiend_available?

    begin
      query_params = {
        repository_branch_id: repository_branch_id,
        force_reindex: force_reindex
      }

      Rails.logger.info "Triggering repository branch scan in Aiend for: #{repository_branch_id}"

      response = HTTParty.post(
        "#{AIEND_BASE_URL}/api/v1/webhook/repository-branch-scan",
        query: query_params,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        },
        timeout: WEBHOOK_TIMEOUT
      )

      if response.success?
        Rails.logger.info "Aiend repository branch scan triggered successfully"
        response.parsed_response
      else
        Rails.logger.error "Aiend repository branch scan trigger failed: #{response.code} - #{response.body}"
        false
      end

    rescue Net::TimeoutError => e
      Rails.logger.error "Aiend repository branch scan timeout: #{e.message}"
      false
    rescue StandardError => e
      Rails.logger.error "Aiend repository branch scan error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  def self.delete_repository(repository_id, repository_name, branch: nil)
    # Aiend에 저장소 삭제 알림
    #
    # Args:
    #   repository_id: Repository ID
    #   repository_name: Repository 이름
    #   branch: 브랜치명 (선택사항, 없으면 전체 리포지토리 삭제)

    return false unless aiend_available?

    begin
      query_params = {
        repository_id: repository_id.to_s,
        repository_name: repository_name
      }
      query_params[:branch] = branch if branch

      Rails.logger.info "Notifying Aiend about repository deletion: #{repository_name} (ID: #{repository_id})"

      response = HTTParty.delete(
        "#{AIEND_BASE_URL}/api/v1/webhook/repository",
        query: query_params,
        headers: {
          'Content-Type' => 'application/json',
          'Accept' => 'application/json'
        },
        timeout: WEBHOOK_TIMEOUT
      )

      if response.success?
        Rails.logger.info "Aiend repository deletion notification sent successfully"
        response.parsed_response
      else
        Rails.logger.error "Aiend repository deletion notification failed: #{response.code} - #{response.body}"
        false
      end

    rescue Net::TimeoutError => e
      Rails.logger.error "Aiend repository deletion timeout: #{e.message}"
      false
    rescue StandardError => e
      Rails.logger.error "Aiend repository deletion error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end
  
  private
  
  def self.log_webhook_call(endpoint, payload, response)
    # Webhook 호출 로그 기록
    
    Rails.logger.info "Aiend Webhook Call:"
    Rails.logger.info "  Endpoint: #{endpoint}"
    Rails.logger.info "  Payload: #{payload.to_json}"
    Rails.logger.info "  Response: #{response&.code} - #{response&.body}"
  end
end
