#!/usr/bin/env ruby

require 'socket'
require 'json'
require 'sqlite3'
require 'cgi'
require_relative 'simple_backend'

class VerySimpleServer
  def initialize(port = 3001)
    @port = port
    @db = SQLite3::Database.new('codebase.db')
    @db.results_as_hash = true
    @backend = CodebaseBackend.new('codebase.db')

    # Auto-scan current repository on startup
    auto_scan_current_repository
  end

  def start
    server = TCPServer.new(@port)
    puts "Simple Backend Server running on http://localhost:#{@port}"
    puts "Endpoints:"
    puts "  GET /api/health"
    puts "  GET /api/repositories"
    puts "  GET /api/search?q=query"
    puts ""

    loop do
      client = server.accept
      
      Thread.new(client) do |c|
        handle_client(c)
        c.close
      end
    end
  rescue Interrupt
    puts "\nShutting down..."
    server.close if server
    @db.close if @db
  end

  private

  def auto_scan_current_repository
    puts "Checking current repository..."

    repo_info = @backend.get_current_repository_info
    puts "Repository info: #{repo_info}"

    # Check if repository already exists and is up to date
    existing_repo = @db.execute(
      "SELECT * FROM repositories WHERE path = ? ORDER BY last_scan_at DESC LIMIT 1",
      [repo_info[:path]]
    ).first

    should_scan = false

    if existing_repo.nil?
      puts "Repository not found in database, scanning..."
      should_scan = true
    else
      # Check if repository has been modified since last scan
      last_scan = Time.parse(existing_repo['last_scan_at']) rescue Time.at(0)

      # Check if .git directory has been modified (indicates new commits)
      git_dir = File.join(repo_info[:path], '.git')
      if File.directory?(git_dir)
        git_mtime = File.mtime(git_dir)
        if git_mtime > last_scan
          puts "Repository has been modified since last scan, re-scanning..."
          should_scan = true
        else
          puts "Repository is up to date (last scan: #{last_scan})"
        end
      else
        # Non-git repository, check if it's been more than 1 hour
        if Time.now - last_scan > 3600
          puts "Non-git repository, re-scanning after 1 hour..."
          should_scan = true
        else
          puts "Repository scanned recently, skipping..."
        end
      end
    end

    if should_scan
      begin
        @backend.scan_repository(repo_info[:path], repo_info[:name], repo_info[:url])
        puts "✅ Repository scan completed successfully!"
      rescue => e
        puts "❌ Repository scan failed: #{e.message}"
      end
    end
  end

  def handle_client(client)
    request = client.gets
    return unless request

    method, path, _ = request.split
    
    # Skip headers
    while (line = client.gets) && line.chomp != ""
    end

    # Parse path and query
    if path.include?('?')
      path, query_string = path.split('?', 2)
      query_params = parse_query(query_string)
    else
      query_params = {}
    end

    response = handle_request(method, path, query_params)
    send_response(client, response)
  rescue => e
    puts "Error: #{e.message}"
    send_error(client, 500, "Internal Server Error")
  end

  def handle_request(method, path, params)
    case [method, path]
    when ['GET', '/api/health']
      {
        status: 200,
        body: { success: true, message: 'Backend server is running' }
      }

    when ['GET', '/api/repositories']
      repos = @db.execute("SELECT * FROM repositories WHERE active = 1 ORDER BY last_scan_at DESC")
      {
        status: 200,
        body: { success: true, data: repos }
      }

    when ['GET', '/api/current-repository']
      repo_info = @backend.get_current_repository_info
      current_repo = @db.execute(
        "SELECT * FROM repositories WHERE path = ? ORDER BY last_scan_at DESC LIMIT 1",
        [repo_info[:path]]
      ).first

      if current_repo
        # Add file counts
        file_count = @db.execute("SELECT COUNT(*) as count FROM files WHERE repository_id = ?", [current_repo['id']]).first['count']
        dir_count = @db.execute("SELECT COUNT(*) as count FROM directories WHERE repository_id = ?", [current_repo['id']]).first['count']

        current_repo['file_count'] = file_count
        current_repo['directory_count'] = dir_count

        {
          status: 200,
          body: { success: true, data: current_repo }
        }
      else
        {
          status: 404,
          body: { success: false, error: 'Current repository not found in database' }
        }
      end

    when ['POST', '/api/scan-current']
      repo_info = @backend.get_current_repository_info
      begin
        repo_id = @backend.scan_repository(repo_info[:path], repo_info[:name], repo_info[:url])
        {
          status: 200,
          body: { success: true, message: 'Repository scanned successfully', repository_id: repo_id }
        }
      rescue => e
        {
          status: 500,
          body: { success: false, error: "Scan failed: #{e.message}" }
        }
      end

    when ['GET', '/api/search']
      query = params['q']
      if query
        results = search_files(query)
        {
          status: 200,
          body: { success: true, data: results, query: query }
        }
      else
        {
          status: 400,
          body: { success: false, error: 'Query parameter q is required' }
        }
      end

    else
      if path =~ /^\/api\/repositories\/(\d+)\/files$/
        repo_id = $1.to_i
        files = @db.execute("SELECT * FROM files WHERE repository_id = ? LIMIT 50", [repo_id])
        {
          status: 200,
          body: { success: true, data: files }
        }
      else
        {
          status: 404,
          body: { success: false, error: 'Not found' }
        }
      end
    end
  end

  def search_files(query)
    sql = "SELECT name, relative_path, language, size_bytes FROM files WHERE content LIKE ? OR name LIKE ? OR relative_path LIKE ? LIMIT 20"
    @db.execute(sql, ["%#{query}%", "%#{query}%", "%#{query}%"])
  end

  def parse_query(query_string)
    params = {}
    query_string.split('&').each do |pair|
      key, value = pair.split('=', 2)
      params[key] = value ? CGI.unescape(value) : ''
    end
    params
  end

  def send_response(client, response)
    body = response[:body].to_json
    
    client.print "HTTP/1.1 #{response[:status]} OK\r\n"
    client.print "Content-Type: application/json\r\n"
    client.print "Access-Control-Allow-Origin: *\r\n"
    client.print "Access-Control-Allow-Methods: GET, POST, OPTIONS\r\n"
    client.print "Access-Control-Allow-Headers: Content-Type\r\n"
    client.print "Content-Length: #{body.bytesize}\r\n"
    client.print "\r\n"
    client.print body
  end

  def send_error(client, status, message)
    send_response(client, {
      status: status,
      body: { success: false, error: message }
    })
  end
end

# Start server
if __FILE__ == $0
  server = VerySimpleServer.new
  server.start
end
