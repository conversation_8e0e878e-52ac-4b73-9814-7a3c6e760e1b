Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # API routes
  namespace :api do
    # CSRF token endpoint
    get 'csrf_token', to: 'csrf#show'

    # Authentication routes
    devise_for :users,
               path: '',
               path_names: {
                 sign_in: 'login',
                 sign_out: 'logout'
               },
               controllers: {
                 sessions: 'api/auth/sessions'
               }

    # Current user endpoint
    get 'users/me', to: 'auth/users#me'
    # File Explorer API (outside repositories scope for testing)
    namespace :explorer do
      resources :repositories, only: [] do
        get :tree, to: '/api/file_explorer#tree'
        get 'directory/*path', to: '/api/file_explorer#directory', constraints: { path: /.*/ }
        get 'file/:id', to: '/api/file_explorer#file'
        get 'file/:id/content', to: '/api/file_explorer#file_content'
        put 'file/:id/content', to: '/api/file_explorer#update_file_content'
        get :search, to: '/api/file_explorer#search'
      end
    end

    # Codebase API routes
    get 'codebase/files', to: 'codebase#files'
    get 'codebase/directories', to: 'codebase#directories'
    get 'codebase/tree', to: 'codebase#tree'
    get 'codebase/search', to: 'codebase#search'
    post 'codebase/index', to: 'codebase#index'
    post 'codebase/index_by_name', to: 'codebase#index_by_name'
    post 'codebase/sql_query', to: 'codebase#sql_query'
    get 'codebase/indexing_status/:job_id', to: 'codebase#indexing_status'

    resources :repositories do
      member do
        # Other repository routes
        post :scan
        get :tree
        get :stats
        get :branches
        post :switch_branch, path: 'switch-branch'
        post :pull
        get :status
        get :commits
        get 'commits/:commit_hash', to: 'repositories#commit_detail', as: :commit_detail
        get 'commits/:commit_hash/diff', to: 'repositories#commit_diff', as: :commit_diff
        get 'commits/:commit_hash/files', to: 'repositories#file_content', as: :file_content
        get 'commits/:commit_hash/files-content', to: 'repositories#commit_files_content', as: :commit_files_content
        get :aiend_status, path: 'aiend-status'
        post :trigger_aiend_scan, path: 'aiend-scan'
        post :trigger_aiend_branch_scan, path: 'aiend-branch-scan'
      end

      collection do
        post :trigger_repository_branch_id_scan, path: 'repository-branch-id-scan'
      end

      collection do
        post :clone
        get :scan_directory, path: 'scan-directory'
      end

      resources :files, only: [:index, :show] do
        member do
          get :content
        end
        collection do
          get :search
        end
      end
    end

    # Global file search across all repositories
    get 'files/search', to: 'files#global_search'

    # Health check for API
    get 'health', to: 'base#health'
    # 검색 API 엔드포인트 추가
    post 'search', to: 'search#create'
  end

  # Defines the root path route ("/")
  root "api/repositories#index"
end
