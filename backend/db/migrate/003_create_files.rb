class CreateFiles < ActiveRecord::Migration[8.0]
  def change
    create_table :files do |t|
      t.references :repository, null: false, foreign_key: true
      t.references :directory, null: true, foreign_key: true
      t.string :name, null: false
      t.text :path, null: false
      t.text :relative_path, null: false
      t.string :extension
      t.string :language
      t.text :content
      t.integer :size_bytes, default: 0
      t.integer :lines_count, default: 0
      t.boolean :is_binary, default: false
      t.string :encoding
      t.string :mime_type
      t.string :sha256_hash
      t.datetime :last_modified_at
      t.json :metadata

      t.timestamps
    end

    add_index :files, [:repository_id, :relative_path], unique: true
    add_index :files, :extension
    add_index :files, :language
    add_index :files, :is_binary
    add_index :files, :size_bytes
    add_index :files, :lines_count
    add_index :files, :last_modified_at
    add_index :files, :name
  end
end
