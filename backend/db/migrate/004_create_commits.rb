class CreateCommits < ActiveRecord::Migration[8.0]
  def change
    create_table :commits do |t|
      t.references :repository, null: false, foreign_key: true
      t.string :sha, null: false
      t.text :message, null: false
      t.string :author_name, null: false
      t.string :author_email, null: false
      t.string :committer_name
      t.string :committer_email
      t.datetime :committed_at, null: false
      t.json :files_changed
      t.integer :additions, default: 0
      t.integer :deletions, default: 0
      t.json :metadata

      t.timestamps
    end

    add_index :commits, [:repository_id, :sha], unique: true
    add_index :commits, :committed_at
    add_index :commits, :author_email
    add_index :commits, :committer_email
  end
end
