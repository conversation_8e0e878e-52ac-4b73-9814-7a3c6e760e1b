class AddAiendFieldsToRepositories < ActiveRecord::Migration[7.0]
  def change
    add_column :repositories, :last_aiend_sync_at, :datetime
    add_column :repositories, :aiend_sync_status, :string
    add_column :repositories, :aiend_sync_error, :text
    add_column :repositories, :last_aiend_scan_at, :datetime
    add_column :repositories, :aiend_scan_status, :string
    add_column :repositories, :aiend_scan_error, :text
    add_column :repositories, :aiend_scan_progress, :integer, default: 0
    add_column :repositories, :aiend_scan_message, :text
    add_column :repositories, :aiend_task_id, :string
    add_column :repositories, :last_aiend_scan_completed_at, :datetime
    
    add_index :repositories, :aiend_sync_status
    add_index :repositories, :aiend_scan_status
    add_index :repositories, :aiend_task_id
  end
end
