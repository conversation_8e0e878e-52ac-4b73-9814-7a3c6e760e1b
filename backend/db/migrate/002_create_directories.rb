class CreateDirectories < ActiveRecord::Migration[8.0]
  def change
    create_table :directories do |t|
      t.references :repository, null: false, foreign_key: true
      t.references :parent, null: true, foreign_key: { to_table: :directories }
      t.string :name, null: false
      t.text :path, null: false
      t.text :relative_path, null: false
      t.integer :depth, default: 0
      t.json :metadata

      t.timestamps
    end

    add_index :directories, [:repository_id, :relative_path], unique: true
    add_index :directories, :depth
    add_index :directories, :name
  end
end
