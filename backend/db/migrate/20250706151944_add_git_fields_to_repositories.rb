class AddGitFieldsToRepositories < ActiveRecord::Migration[8.0]
  def change
    add_column :repositories, :current_branch, :string, default: 'main'
    add_column :repositories, :branches, :text # JSON array of branch names
    add_column :repositories, :username, :string
    add_column :repositories, :password, :string
    add_column :repositories, :last_indexed_at, :datetime
    add_column :repositories, :clone_status, :string, default: 'pending' # pending, cloning, ready, error
  end
end
