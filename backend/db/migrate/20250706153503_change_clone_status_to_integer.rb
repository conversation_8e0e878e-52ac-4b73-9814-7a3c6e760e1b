class ChangeCloneStatusToInteger < ActiveRecord::Migration[8.0]
  def up
    # Convert string values to integer values
    execute <<-SQL
      UPDATE repositories
      SET clone_status = CASE
        WHEN clone_status = 'pending' THEN 0
        WHEN clone_status = 'cloning' THEN 1
        WHEN clone_status = 'ready' THEN 2
        WHEN clone_status = 'error' THEN 3
        ELSE 0
      END
    SQL

    # Change column type to integer
    change_column :repositories, :clone_status, :integer, default: 0
  end

  def down
    # Change back to string
    change_column :repositories, :clone_status, :string, default: 'pending'

    # Convert integer values back to string values
    execute <<-SQL
      UPDATE repositories
      SET clone_status = CASE
        WHEN clone_status = 0 THEN 'pending'
        WHEN clone_status = 1 THEN 'cloning'
        WHEN clone_status = 2 THEN 'ready'
        WHEN clone_status = 3 THEN 'error'
        ELSE 'pending'
      END
    SQL
  end
end
