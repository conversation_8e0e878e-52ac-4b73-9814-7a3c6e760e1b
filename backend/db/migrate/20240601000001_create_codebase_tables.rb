class CreateCodebaseTables < ActiveRecord::Migration[7.0]
  def change
    # 파일 메타데이터를 저장하는 테이블
    create_table :codebase_files do |t|
      t.references :repository, null: false, foreign_key: true
      t.string :branch, null: false
      t.string :path, null: false
      t.string :filename, null: false
      t.string :extension
      t.integer :depth, null: false, default: 0
      t.text :content
      t.string :hash_id
      t.integer :size
      t.boolean :is_binary, default: false
      t.timestamps
      
      # 복합 인덱스 추가
      t.index [:repository_id, :branch, :path, :filename], name: 'idx_codebase_files_repo_branch_path_filename'
      t.index [:repository_id, :branch, :extension], name: 'idx_codebase_files_repo_branch_ext'
      t.index [:repository_id, :branch, :depth], name: 'idx_codebase_files_repo_branch_depth'
    end

    # 디렉토리 메타데이터를 저장하는 테이블
    create_table :codebase_directories do |t|
      t.references :repository, null: false, foreign_key: true
      t.string :branch, null: false
      t.string :path, null: false
      t.string :name, null: false
      t.integer :depth, null: false, default: 0
      t.references :parent_directory, null: true, index: true
      t.timestamps
      
      # 복합 인덱스 추가
      t.index [:repository_id, :branch, :path], name: 'idx_codebase_dirs_repo_branch_path'
      t.index [:repository_id, :branch, :depth], name: 'idx_codebase_dirs_repo_branch_depth'
    end

    # 코드 심볼(함수, 클래스, 변수 등)을 저장하는 테이블
    create_table :codebase_symbols do |t|
      t.references :codebase_file, null: false, foreign_key: true
      t.string :name, null: false
      t.string :type, null: false  # 'function', 'class', 'variable', 'interface' 등
      t.integer :start_line
      t.integer :end_line
      t.text :signature
      t.text :documentation
      t.json :metadata
      t.timestamps
      
      # 인덱스 추가
      t.index [:codebase_file_id, :name], name: 'idx_codebase_symbols_file_name'
      t.index [:codebase_file_id, :type], name: 'idx_codebase_symbols_file_type'
      t.index :name, name: 'idx_codebase_symbols_name'
    end

    # 전체 텍스트 검색을 위한 FTS5 가상 테이블 생성
    # SQLite FTS5 문법 사용
    execute <<-SQL
      CREATE VIRTUAL TABLE IF NOT EXISTS codebase_files_fts USING fts5(
        content, 
        filename,
        path,
        extension,
        content='codebase_files',
        content_rowid='id'
      );
    SQL

    execute <<-SQL
      CREATE VIRTUAL TABLE IF NOT EXISTS codebase_symbols_fts USING fts5(
        name,
        signature,
        documentation,
        content='codebase_symbols',
        content_rowid='id'
      );
    SQL

    # FTS 테이블을 위한 트리거 생성
    # 파일 FTS 트리거
    execute <<-SQL
      CREATE TRIGGER codebase_files_ai AFTER INSERT ON codebase_files BEGIN
        INSERT INTO codebase_files_fts(rowid, content, filename, path, extension)
        VALUES (new.id, new.content, new.filename, new.path, new.extension);
      END;
    SQL

    execute <<-SQL
      CREATE TRIGGER codebase_files_ad AFTER DELETE ON codebase_files BEGIN
        INSERT INTO codebase_files_fts(codebase_files_fts, rowid, content, filename, path, extension)
        VALUES('delete', old.id, old.content, old.filename, old.path, old.extension);
      END;
    SQL

    execute <<-SQL
      CREATE TRIGGER codebase_files_au AFTER UPDATE ON codebase_files BEGIN
        INSERT INTO codebase_files_fts(codebase_files_fts, rowid, content, filename, path, extension)
        VALUES('delete', old.id, old.content, old.filename, old.path, old.extension);
        INSERT INTO codebase_files_fts(rowid, content, filename, path, extension)
        VALUES (new.id, new.content, new.filename, new.path, new.extension);
      END;
    SQL

    # 심볼 FTS 트리거
    execute <<-SQL
      CREATE TRIGGER codebase_symbols_ai AFTER INSERT ON codebase_symbols BEGIN
        INSERT INTO codebase_symbols_fts(rowid, name, signature, documentation)
        VALUES (new.id, new.name, new.signature, new.documentation);
      END;
    SQL

    execute <<-SQL
      CREATE TRIGGER codebase_symbols_ad AFTER DELETE ON codebase_symbols BEGIN
        INSERT INTO codebase_symbols_fts(codebase_symbols_fts, rowid, name, signature, documentation)
        VALUES('delete', old.id, old.name, old.signature, old.documentation);
      END;
    SQL

    execute <<-SQL
      CREATE TRIGGER codebase_symbols_au AFTER UPDATE ON codebase_symbols BEGIN
        INSERT INTO codebase_symbols_fts(codebase_symbols_fts, rowid, name, signature, documentation)
        VALUES('delete', old.id, old.name, old.signature, old.documentation);
        INSERT INTO codebase_symbols_fts(rowid, name, signature, documentation)
        VALUES (new.id, new.name, new.signature, new.documentation);
      END;
    SQL
  end
end
