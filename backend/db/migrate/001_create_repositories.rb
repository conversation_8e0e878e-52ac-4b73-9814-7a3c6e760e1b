class CreateRepositories < ActiveRecord::Migration[8.0]
  def change
    create_table :repositories do |t|
      t.string :name, null: false
      t.string :path, null: false
      t.string :url, null: false
      t.text :description
      t.integer :status, default: 0
      t.boolean :active, default: true
      t.datetime :last_scan_at
      t.text :error_message
      t.json :metadata

      t.timestamps
    end

    add_index :repositories, :path, unique: true
    add_index :repositories, :status
    add_index :repositories, :active
    add_index :repositories, :last_scan_at
  end
end
