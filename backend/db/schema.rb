# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_06_153709) do
  create_table "codebase_directories", force: :cascade do |t|
    t.integer "repository_id", null: false
    t.string "branch", null: false
    t.string "path", null: false
    t.string "name", null: false
    t.integer "depth", default: 0, null: false
    t.integer "parent_directory_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["parent_directory_id"], name: "index_codebase_directories_on_parent_directory_id"
    t.index ["repository_id", "branch", "depth"], name: "idx_codebase_dirs_repo_branch_depth"
    t.index ["repository_id", "branch", "path"], name: "idx_codebase_dirs_repo_branch_path"
    t.index ["repository_id"], name: "index_codebase_directories_on_repository_id"
  end

  create_table "codebase_files", force: :cascade do |t|
    t.integer "repository_id", null: false
    t.string "branch", null: false
    t.string "path", null: false
    t.string "filename", null: false
    t.string "extension"
    t.integer "depth", default: 0, null: false
    t.text "content"
    t.string "hash_id"
    t.integer "size"
    t.boolean "is_binary", default: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["repository_id", "branch", "depth"], name: "idx_codebase_files_repo_branch_depth"
    t.index ["repository_id", "branch", "extension"], name: "idx_codebase_files_repo_branch_ext"
    t.index ["repository_id", "branch", "path", "filename"], name: "idx_codebase_files_repo_branch_path_filename"
    t.index ["repository_id"], name: "index_codebase_files_on_repository_id"
  end

  create_table "codebase_symbols", force: :cascade do |t|
    t.integer "codebase_file_id", null: false
    t.string "name", null: false
    t.string "type", null: false
    t.integer "start_line"
    t.integer "end_line"
    t.text "signature"
    t.text "documentation"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["codebase_file_id", "name"], name: "idx_codebase_symbols_file_name"
    t.index ["codebase_file_id", "type"], name: "idx_codebase_symbols_file_type"
    t.index ["codebase_file_id"], name: "index_codebase_symbols_on_codebase_file_id"
    t.index ["name"], name: "idx_codebase_symbols_name"
  end

  create_table "commits", force: :cascade do |t|
    t.integer "repository_id", null: false
    t.string "sha", null: false
    t.text "message", null: false
    t.string "author_name", null: false
    t.string "author_email", null: false
    t.string "committer_name"
    t.string "committer_email"
    t.datetime "committed_at", null: false
    t.json "files_changed"
    t.integer "additions", default: 0
    t.integer "deletions", default: 0
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["author_email"], name: "index_commits_on_author_email"
    t.index ["committed_at"], name: "index_commits_on_committed_at"
    t.index ["committer_email"], name: "index_commits_on_committer_email"
    t.index ["repository_id", "sha"], name: "index_commits_on_repository_id_and_sha", unique: true
    t.index ["repository_id"], name: "index_commits_on_repository_id"
  end

  create_table "directories", force: :cascade do |t|
    t.integer "repository_id", null: false
    t.integer "parent_id"
    t.string "name", null: false
    t.text "path", null: false
    t.text "relative_path", null: false
    t.integer "depth", default: 0
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["depth"], name: "index_directories_on_depth"
    t.index ["name"], name: "index_directories_on_name"
    t.index ["parent_id"], name: "index_directories_on_parent_id"
    t.index ["repository_id", "relative_path"], name: "index_directories_on_repository_id_and_relative_path", unique: true
    t.index ["repository_id"], name: "index_directories_on_repository_id"
  end

  create_table "files", force: :cascade do |t|
    t.integer "repository_id", null: false
    t.integer "directory_id"
    t.string "name", null: false
    t.text "path", null: false
    t.text "relative_path", null: false
    t.string "extension"
    t.string "language"
    t.text "content"
    t.integer "size_bytes", default: 0
    t.integer "lines_count", default: 0
    t.boolean "is_binary", default: false
    t.string "encoding"
    t.string "mime_type"
    t.string "sha256_hash"
    t.datetime "last_modified_at"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["directory_id"], name: "index_files_on_directory_id"
    t.index ["extension"], name: "index_files_on_extension"
    t.index ["is_binary"], name: "index_files_on_is_binary"
    t.index ["language"], name: "index_files_on_language"
    t.index ["last_modified_at"], name: "index_files_on_last_modified_at"
    t.index ["lines_count"], name: "index_files_on_lines_count"
    t.index ["name"], name: "index_files_on_name"
    t.index ["repository_id", "relative_path"], name: "index_files_on_repository_id_and_relative_path", unique: true
    t.index ["repository_id"], name: "index_files_on_repository_id"
    t.index ["size_bytes"], name: "index_files_on_size_bytes"
  end

  create_table "repositories", force: :cascade do |t|
    t.string "name", null: false
    t.string "path"
    t.string "url", null: false
    t.text "description"
    t.integer "status", default: 0
    t.boolean "active", default: true
    t.datetime "last_scan_at"
    t.text "error_message"
    t.json "metadata"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "current_branch", default: "main"
    t.text "branches"
    t.string "username"
    t.string "password"
    t.datetime "last_indexed_at"
    t.integer "clone_status", default: 0
    t.datetime "last_aiend_sync_at"
    t.string "aiend_sync_status"
    t.text "aiend_sync_error"
    t.datetime "last_aiend_scan_at"
    t.string "aiend_scan_status"
    t.text "aiend_scan_error"
    t.integer "aiend_scan_progress", default: 0
    t.text "aiend_scan_message"
    t.string "aiend_task_id"
    t.datetime "last_aiend_scan_completed_at"
    t.index ["active"], name: "index_repositories_on_active"
    t.index ["aiend_scan_status"], name: "index_repositories_on_aiend_scan_status"
    t.index ["aiend_sync_status"], name: "index_repositories_on_aiend_sync_status"
    t.index ["aiend_task_id"], name: "index_repositories_on_aiend_task_id"
    t.index ["last_scan_at"], name: "index_repositories_on_last_scan_at"
    t.index ["path"], name: "index_repositories_on_path", unique: true
    t.index ["status"], name: "index_repositories_on_status"
  end

  add_foreign_key "codebase_directories", "repositories"
  add_foreign_key "codebase_files", "repositories"
  add_foreign_key "codebase_symbols", "codebase_files"
  add_foreign_key "commits", "repositories"
  add_foreign_key "directories", "directories", column: "parent_id"
  add_foreign_key "directories", "repositories"
  add_foreign_key "files", "directories"
  add_foreign_key "files", "repositories"

  # Virtual tables defined in this database.
  # Note that virtual tables may not work with other database engines. Be careful if changing database.
