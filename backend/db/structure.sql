CREATE TABLE IF NOT EXISTS "repositories" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "name" varchar NOT NULL, "path" varchar, "url" varchar NOT NULL, "description" text, "status" integer DEFAULT 0, "active" boolean DEFAULT 1, "last_scan_at" datetime(6), "error_message" text, "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, "current_branch" varchar DEFAULT 'main', "branches" text, "username" varchar, "password" varchar, "last_indexed_at" datetime(6), "clone_status" integer DEFAULT 0, "last_aiend_sync_at" datetime(6) /*application='Backend'*/, "aiend_sync_status" varchar /*application='Backend'*/, "aiend_sync_error" text /*application='Backend'*/, "last_aiend_scan_at" datetime(6) /*application='Backend'*/, "aiend_scan_status" varchar /*application='Backend'*/, "aiend_scan_error" text /*application='Backend'*/, "aiend_scan_progress" integer DEFAULT 0 /*application='Backend'*/, "aiend_scan_message" text /*application='Backend'*/, "aiend_task_id" varchar /*application='Backend'*/, "last_aiend_scan_completed_at" datetime(6) /*application='Backend'*/);
CREATE INDEX "index_repositories_on_active" ON "repositories" ("active") /*application='Backend'*/;
CREATE INDEX "index_repositories_on_last_scan_at" ON "repositories" ("last_scan_at") /*application='Backend'*/;
CREATE UNIQUE INDEX "index_repositories_on_path" ON "repositories" ("path") /*application='Backend'*/;
CREATE INDEX "index_repositories_on_status" ON "repositories" ("status") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "commits" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "repository_id" integer NOT NULL, "sha" varchar NOT NULL, "message" text NOT NULL, "author_name" varchar NOT NULL, "author_email" varchar NOT NULL, "committer_name" varchar, "committer_email" varchar, "committed_at" datetime(6) NOT NULL, "files_changed" json, "additions" integer DEFAULT 0, "deletions" integer DEFAULT 0, "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_a8299bc69b"
FOREIGN KEY ("repository_id")
  REFERENCES "repositories" ("id")
);
CREATE INDEX "index_commits_on_author_email" ON "commits" ("author_email") /*application='Backend'*/;
CREATE INDEX "index_commits_on_committed_at" ON "commits" ("committed_at") /*application='Backend'*/;
CREATE INDEX "index_commits_on_committer_email" ON "commits" ("committer_email") /*application='Backend'*/;
CREATE UNIQUE INDEX "index_commits_on_repository_id_and_sha" ON "commits" ("repository_id", "sha") /*application='Backend'*/;
CREATE INDEX "index_commits_on_repository_id" ON "commits" ("repository_id") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "directories" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "repository_id" integer NOT NULL, "parent_id" integer, "name" varchar NOT NULL, "path" text NOT NULL, "relative_path" text NOT NULL, "depth" integer DEFAULT 0, "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_3db8c99af1"
FOREIGN KEY ("parent_id")
  REFERENCES "directories" ("id")
, CONSTRAINT "fk_rails_a7c88909b6"
FOREIGN KEY ("repository_id")
  REFERENCES "repositories" ("id")
);
CREATE INDEX "index_directories_on_depth" ON "directories" ("depth") /*application='Backend'*/;
CREATE INDEX "index_directories_on_name" ON "directories" ("name") /*application='Backend'*/;
CREATE INDEX "index_directories_on_parent_id" ON "directories" ("parent_id") /*application='Backend'*/;
CREATE UNIQUE INDEX "index_directories_on_repository_id_and_relative_path" ON "directories" ("repository_id", "relative_path") /*application='Backend'*/;
CREATE INDEX "index_directories_on_repository_id" ON "directories" ("repository_id") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "files" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "repository_id" integer NOT NULL, "directory_id" integer, "name" varchar NOT NULL, "path" text NOT NULL, "relative_path" text NOT NULL, "extension" varchar, "language" varchar, "content" text, "size_bytes" integer DEFAULT 0, "lines_count" integer DEFAULT 0, "is_binary" boolean DEFAULT 0, "encoding" varchar, "mime_type" varchar, "sha256_hash" varchar, "last_modified_at" datetime(6), "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_d280c6285c"
FOREIGN KEY ("directory_id")
  REFERENCES "directories" ("id")
, CONSTRAINT "fk_rails_b33c6e0e8e"
FOREIGN KEY ("repository_id")
  REFERENCES "repositories" ("id")
);
CREATE INDEX "index_files_on_directory_id" ON "files" ("directory_id") /*application='Backend'*/;
CREATE INDEX "index_files_on_extension" ON "files" ("extension") /*application='Backend'*/;
CREATE INDEX "index_files_on_is_binary" ON "files" ("is_binary") /*application='Backend'*/;
CREATE INDEX "index_files_on_language" ON "files" ("language") /*application='Backend'*/;
CREATE INDEX "index_files_on_last_modified_at" ON "files" ("last_modified_at") /*application='Backend'*/;
CREATE INDEX "index_files_on_lines_count" ON "files" ("lines_count") /*application='Backend'*/;
CREATE INDEX "index_files_on_name" ON "files" ("name") /*application='Backend'*/;
CREATE UNIQUE INDEX "index_files_on_repository_id_and_relative_path" ON "files" ("repository_id", "relative_path") /*application='Backend'*/;
CREATE INDEX "index_files_on_repository_id" ON "files" ("repository_id") /*application='Backend'*/;
CREATE INDEX "index_files_on_size_bytes" ON "files" ("size_bytes") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "schema_migrations" ("version" varchar NOT NULL PRIMARY KEY);
CREATE TABLE IF NOT EXISTS "ar_internal_metadata" ("key" varchar NOT NULL PRIMARY KEY, "value" varchar, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL);
CREATE INDEX "index_repositories_on_aiend_sync_status" ON "repositories" ("aiend_sync_status") /*application='Backend'*/;
CREATE INDEX "index_repositories_on_aiend_scan_status" ON "repositories" ("aiend_scan_status") /*application='Backend'*/;
CREATE INDEX "index_repositories_on_aiend_task_id" ON "repositories" ("aiend_task_id") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "codebase_files" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "repository_id" integer NOT NULL, "branch" varchar NOT NULL, "path" varchar NOT NULL, "filename" varchar NOT NULL, "extension" varchar, "depth" integer DEFAULT 0 NOT NULL, "content" text, "hash_id" varchar, "size" integer, "is_binary" boolean DEFAULT 0, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_1161b031e0"
FOREIGN KEY ("repository_id")
  REFERENCES "repositories" ("id")
);
CREATE INDEX "index_codebase_files_on_repository_id" ON "codebase_files" ("repository_id") /*application='Backend'*/;
CREATE INDEX "idx_codebase_files_repo_branch_path_filename" ON "codebase_files" ("repository_id", "branch", "path", "filename") /*application='Backend'*/;
CREATE INDEX "idx_codebase_files_repo_branch_ext" ON "codebase_files" ("repository_id", "branch", "extension") /*application='Backend'*/;
CREATE INDEX "idx_codebase_files_repo_branch_depth" ON "codebase_files" ("repository_id", "branch", "depth") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "codebase_directories" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "repository_id" integer NOT NULL, "branch" varchar NOT NULL, "path" varchar NOT NULL, "name" varchar NOT NULL, "depth" integer DEFAULT 0 NOT NULL, "parent_directory_id" integer, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_c78c5adb21"
FOREIGN KEY ("repository_id")
  REFERENCES "repositories" ("id")
);
CREATE INDEX "index_codebase_directories_on_repository_id" ON "codebase_directories" ("repository_id") /*application='Backend'*/;
CREATE INDEX "index_codebase_directories_on_parent_directory_id" ON "codebase_directories" ("parent_directory_id") /*application='Backend'*/;
CREATE INDEX "idx_codebase_dirs_repo_branch_path" ON "codebase_directories" ("repository_id", "branch", "path") /*application='Backend'*/;
CREATE INDEX "idx_codebase_dirs_repo_branch_depth" ON "codebase_directories" ("repository_id", "branch", "depth") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "codebase_symbols" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "codebase_file_id" integer NOT NULL, "name" varchar NOT NULL, "type" varchar NOT NULL, "start_line" integer, "end_line" integer, "signature" text, "documentation" text, "metadata" json, "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, CONSTRAINT "fk_rails_77926f2583"
FOREIGN KEY ("codebase_file_id")
  REFERENCES "codebase_files" ("id")
);
CREATE INDEX "index_codebase_symbols_on_codebase_file_id" ON "codebase_symbols" ("codebase_file_id") /*application='Backend'*/;
CREATE INDEX "idx_codebase_symbols_file_name" ON "codebase_symbols" ("codebase_file_id", "name") /*application='Backend'*/;
CREATE INDEX "idx_codebase_symbols_file_type" ON "codebase_symbols" ("codebase_file_id", "type") /*application='Backend'*/;
CREATE INDEX "idx_codebase_symbols_name" ON "codebase_symbols" ("name") /*application='Backend'*/;
CREATE VIRTUAL TABLE codebase_files_fts USING fts5(
        content, 
        filename,
        path,
        extension,
        content='codebase_files',
        content_rowid='id'
      )
/* codebase_files_fts(content,filename,path,extension) */;
CREATE TABLE IF NOT EXISTS 'codebase_files_fts_data'(id INTEGER PRIMARY KEY, block BLOB);
CREATE TABLE IF NOT EXISTS 'codebase_files_fts_idx'(segid, term, pgno, PRIMARY KEY(segid, term)) WITHOUT ROWID;
CREATE TABLE IF NOT EXISTS 'codebase_files_fts_docsize'(id INTEGER PRIMARY KEY, sz BLOB);
CREATE TABLE IF NOT EXISTS 'codebase_files_fts_config'(k PRIMARY KEY, v) WITHOUT ROWID;
CREATE VIRTUAL TABLE codebase_symbols_fts USING fts5(
        name,
        signature,
        documentation,
        content='codebase_symbols',
        content_rowid='id'
      )
/* codebase_symbols_fts(name,signature,documentation) */;
CREATE TABLE IF NOT EXISTS 'codebase_symbols_fts_data'(id INTEGER PRIMARY KEY, block BLOB);
CREATE TABLE IF NOT EXISTS 'codebase_symbols_fts_idx'(segid, term, pgno, PRIMARY KEY(segid, term)) WITHOUT ROWID;
CREATE TABLE IF NOT EXISTS 'codebase_symbols_fts_docsize'(id INTEGER PRIMARY KEY, sz BLOB);
CREATE TABLE IF NOT EXISTS 'codebase_symbols_fts_config'(k PRIMARY KEY, v) WITHOUT ROWID;
CREATE TRIGGER codebase_files_ai AFTER INSERT ON codebase_files BEGIN
        INSERT INTO codebase_files_fts(rowid, content, filename, path, extension)
        VALUES (new.id, new.content, new.filename, new.path, new.extension);
      END;
CREATE TRIGGER codebase_files_ad AFTER DELETE ON codebase_files BEGIN
        INSERT INTO codebase_files_fts(codebase_files_fts, rowid, content, filename, path, extension)
        VALUES('delete', old.id, old.content, old.filename, old.path, old.extension);
      END;
CREATE TRIGGER codebase_files_au AFTER UPDATE ON codebase_files BEGIN
        INSERT INTO codebase_files_fts(codebase_files_fts, rowid, content, filename, path, extension)
        VALUES('delete', old.id, old.content, old.filename, old.path, old.extension);
        INSERT INTO codebase_files_fts(rowid, content, filename, path, extension)
        VALUES (new.id, new.content, new.filename, new.path, new.extension);
      END;
CREATE TRIGGER codebase_symbols_ai AFTER INSERT ON codebase_symbols BEGIN
        INSERT INTO codebase_symbols_fts(rowid, name, signature, documentation)
        VALUES (new.id, new.name, new.signature, new.documentation);
      END;
CREATE TRIGGER codebase_symbols_ad AFTER DELETE ON codebase_symbols BEGIN
        INSERT INTO codebase_symbols_fts(codebase_symbols_fts, rowid, name, signature, documentation)
        VALUES('delete', old.id, old.name, old.signature, old.documentation);
      END;
CREATE TRIGGER codebase_symbols_au AFTER UPDATE ON codebase_symbols BEGIN
        INSERT INTO codebase_symbols_fts(codebase_symbols_fts, rowid, name, signature, documentation)
        VALUES('delete', old.id, old.name, old.signature, old.documentation);
        INSERT INTO codebase_symbols_fts(rowid, name, signature, documentation)
        VALUES (new.id, new.name, new.signature, new.documentation);
      END;
CREATE TABLE IF NOT EXISTS "jwt_blacklists" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "jti" varchar, "exp" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL);
CREATE INDEX "index_jwt_blacklists_on_jti" ON "jwt_blacklists" ("jti") /*application='Backend'*/;
CREATE TABLE IF NOT EXISTS "users" ("id" integer PRIMARY KEY AUTOINCREMENT NOT NULL, "email" varchar DEFAULT '' NOT NULL, "encrypted_password" varchar DEFAULT '' NOT NULL, "reset_password_token" varchar, "reset_password_sent_at" datetime(6), "remember_created_at" datetime(6), "created_at" datetime(6) NOT NULL, "updated_at" datetime(6) NOT NULL, "jti" varchar NOT NULL);
CREATE UNIQUE INDEX "index_users_on_email" ON "users" ("email") /*application='Backend'*/;
CREATE UNIQUE INDEX "index_users_on_reset_password_token" ON "users" ("reset_password_token") /*application='Backend'*/;
CREATE UNIQUE INDEX "index_users_on_jti" ON "users" ("jti") /*application='Backend'*/;
INSERT INTO "schema_migrations" (version) VALUES
('4'),
('3'),
('20250709045704'),
('20250709045519'),
('20250709045446'),
('20250706153709'),
('20250706153503'),
('20250706151944'),
('20241207000001'),
('20240601000001'),
('2'),
('1');

