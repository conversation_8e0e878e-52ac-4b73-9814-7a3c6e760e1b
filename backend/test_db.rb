#!/usr/bin/env ruby

require 'sqlite3'

# Test database connection and data
db = SQLite3::Database.new('codebase.db')
db.results_as_hash = true

puts "Testing database connection..."

# Check repositories
repos = db.execute("SELECT * FROM repositories")
puts "Repositories found: #{repos.length}"
repos.each do |repo|
  puts "  ID: #{repo['id']}, Name: #{repo['name']}, Path: #{repo['path']}"
end

# Check files count
files_count = db.execute("SELECT COUNT(*) as count FROM files")[0]['count']
puts "Total files: #{files_count}"

# Check directories count
dirs_count = db.execute("SELECT COUNT(*) as count FROM directories")[0]['count']
puts "Total directories: #{dirs_count}"

# Show some sample files
puts "\nSample files:"
files = db.execute("SELECT name, relative_path, language, size_bytes FROM files LIMIT 10")
files.each do |file|
  puts "  #{file['relative_path']} (#{file['language']}, #{file['size_bytes']} bytes)"
end

# Test search
puts "\nTesting search for 'package':"
search_results = db.execute("SELECT name, relative_path FROM files WHERE content LIKE '%package%' OR name LIKE '%package%' LIMIT 5")
search_results.each do |file|
  puts "  #{file['relative_path']}"
end

db.close
puts "\nDatabase test completed!"
