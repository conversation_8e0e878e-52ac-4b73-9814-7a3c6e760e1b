services:
  # Backend Service (Ruby on Rails)
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: codebase-intelligence-backend
    command: bash -c "mkdir -p storage && rm -f tmp/pids/server.pid && bundle exec rails s -p 3000 -b '0.0.0.0'"
    ports:
      - "7101:3000"
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=sqlite3:/app/storage/development.sqlite3
      - AI_ENGINE_URL=http://host.docker.internal:7102
      - AIEND_BASE_URL=http://host.docker.internal:7102
      - AIEND_ENABLED=true
      - RAILS_LOG_LEVEL=info
      - OLLAMA_HOST=http://host.docker.internal:11434
      - QDRANT_URL=http://qdrant:6333
      - CODEBASE_VECTOR_MODEL=dengcao/Qwen3-Embedding-8B:Q5_K_M
    volumes:
      - .:/app
      - /mnt/hdd500/data/codebase-intelligence/backend/storage:/app/storage
      - /mnt/hdd500/data/codebase-intelligence/repos:/app/repositories
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/up"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence
