import subprocess
import json
import time
import statistics
import random

# 설정
OLLAMA_HOST = "http://localhost:11434"
MODEL = "dengcao/Qwen3-Embedding-8B:Q5_K_M"
NUM_REQUESTS = 100  # 테스트 횟수
BASE_TEXT = "This is a test sentence for measuring embedding latency."

def make_curl_command(prompt: str):
    payload = json.dumps({
        "model": MODEL,
        "prompt": prompt
    })
    return [
        "curl", "-s", "-X", "POST",
        f"{OLLAMA_HOST}/api/embeddings",
        "-H", "Content-Type: application/json",
        "-d", payload
    ]

def benchmark():
    times = []
    success = 0

    for i in range(NUM_REQUESTS):
        # 각 요청에 약간의 랜덤 문장 추가
        test_prompt = f"{BASE_TEXT} #{random.randint(1, 100000)}"
        print(f"Request {i+1}/{NUM_REQUESTS}...", end=" ")

        start = time.time()

        try:
            result = subprocess.run(
                make_curl_command(test_prompt),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                timeout=30
            )
        except subprocess.TimeoutExpired:
            print("❌ Timeout")
            continue

        end = time.time()
        elapsed = end - start

        if result.returncode == 0:
            try:
                response_json = json.loads(result.stdout.decode())
                if 'embedding' in response_json:
                    times.append(elapsed)
                    success += 1
                    print(f"✅ {elapsed:.3f} sec")
                else:
                    print("❌ Invalid response format")
            except json.JSONDecodeError:
                print("❌ JSON Decode Error")
        else:
            print(f"❌ Curl Error: {result.stderr.decode().strip()}")

    print("\n📊 Benchmark Summary:")
    print(f"- Total Requests     : {NUM_REQUESTS}")
    print(f"- Successful Requests: {success}")
    if times:
        print(f"- Average Time       : {statistics.mean(times):.3f} sec")
        print(f"- Minimum Time       : {min(times):.3f} sec")
        print(f"- Maximum Time       : {max(times):.3f} sec")
        if len(times) > 1:
            print(f"- Std Dev            : {statistics.stdev(times):.3f} sec")
    else:
        print("❌ No successful responses")

if __name__ == "__main__":
    benchmark()
