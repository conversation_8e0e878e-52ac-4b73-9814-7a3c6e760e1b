# Quick Start Guide

CodeBase Intelligence System을 빠르게 시작하는 방법입니다.

## 5분 만에 시작하기

### 1. 설치

```bash
git clone https://github.com/codebase-intelligence/codebase-sqlite.git
cd codebase-sqlite
pip install -e .
```

### 2. 환경 설정

```bash
cp .env.example .env
# .env 파일에서 API 키 설정 (선택사항)
```

### 3. 코드베이스 인덱싱

```bash
# 현재 디렉토리의 코드베이스 인덱싱
codebase-intel index

# 다른 프로젝트 인덱싱
codebase-intel --repo /path/to/your/project index
```

### 4. 검색 시작

```bash
# 의미적 검색
codebase-intel search "authentication function"

# 특정 타입 검색
codebase-intel search "user class" --type class

# 구조적 검색
codebase-intel search "database connection" --mode structural
```

## 주요 명령어

### 인덱싱

```bash
# 기본 인덱싱
codebase-intel index

# 강제 재인덱싱
codebase-intel index --force

# 특정 언어만 인덱싱
codebase-intel index --languages python,javascript
```

### 검색

```bash
# 기본 검색 (자동 모드)
codebase-intel search "your query"

# 검색 모드 지정
codebase-intel search "query" --mode semantic
codebase-intel search "query" --mode structural
codebase-intel search "query" --mode temporal
codebase-intel search "query" --mode hybrid

# 결과 수 제한
codebase-intel search "query" --max-results 5

# 출력 형식 변경
codebase-intel search "query" --format json
codebase-intel search "query" --format detailed
```

### LLM 질문 (API 키 필요)

```bash
# 기본 질문
codebase-intel ask "이 코드의 주요 기능은 무엇인가요?"

# 특정 제공자 사용
codebase-intel ask "코드 리뷰해주세요" --provider anthropic

# 컨텍스트 저장
codebase-intel ask "버그를 찾아주세요" --save-context
```

### 통계 및 정보

```bash
# 시스템 통계
codebase-intel stats

# 현재 설정 확인
codebase-intel config-show
```

## 검색 모드 설명

### 1. Semantic (의미적 검색)
- 코드의 의미와 기능을 기반으로 검색
- 자연어 쿼리에 적합
- 예: "파일을 읽는 함수", "사용자 인증 로직"

### 2. Structural (구조적 검색)
- 코드의 구조와 관계를 기반으로 검색
- 클래스 상속, 함수 호출 관계 등
- 예: "UserService를 상속받는 클래스", "database 모듈을 사용하는 함수"

### 3. Temporal (시간적 검색)
- Git 히스토리를 기반으로 검색
- 최근 변경, 버그 수정 이력 등
- 예: "최근에 수정된 인증 코드", "버그 수정 이력"

### 4. Hybrid (하이브리드 검색)
- 위 세 가지 방법을 조합
- 가장 정확하지만 느림
- 복잡한 쿼리에 적합

### 5. Auto (자동 모드)
- 쿼리를 분석하여 최적 모드 자동 선택
- 기본 모드

## 실제 사용 예시

### 코드 이해하기

```bash
# 특정 함수 찾기
codebase-intel search "login function"

# 클래스 구조 파악
codebase-intel search "User class" --type class

# 설정 파일 찾기
codebase-intel search "configuration" --type file
```

### 버그 찾기

```bash
# 에러 처리 코드 검색
codebase-intel search "error handling exception"

# 최근 변경된 코드 검색
codebase-intel search "recent changes" --mode temporal

# 특정 패턴 검색
codebase-intel search "null pointer" --mode structural
```

### 리팩토링 준비

```bash
# 의존성 분석
codebase-intel search "imports database" --mode structural

# 중복 코드 찾기
codebase-intel search "similar functions" --mode semantic

# 테스트 코드 찾기
codebase-intel search "test" --type function
```

## 성능 팁

### 1. 인덱싱 최적화

```bash
# 불필요한 파일 제외
# codebase-intel.yaml에서 exclude_patterns 설정

# 큰 파일 제외
# max_file_size 설정 조정
```

### 2. 검색 최적화

```bash
# 구체적인 쿼리 사용
codebase-intel search "authentication login user" # 좋음
codebase-intel search "auth" # 너무 일반적

# 적절한 모드 선택
codebase-intel search "recent bug fix" --mode temporal # 시간 관련
codebase-intel search "class inheritance" --mode structural # 구조 관련
```

### 3. 메모리 사용량 줄이기

```bash
# 결과 수 제한
codebase-intel search "query" --max-results 5

# 경량 임베딩 모델 사용
# vector_store.model을 "all-MiniLM-L6-v2"로 설정
```

## 다음 단계

- [설치 가이드](installation.md)에서 상세한 설치 방법 확인
- [설정 가이드](configuration.md)에서 고급 설정 방법 학습
- [API 문서](api.md)에서 프로그래밍 인터페이스 확인
