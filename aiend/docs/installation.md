# Installation Guide

CodeBase Intelligence System 설치 가이드입니다.

## 시스템 요구사항

- Python 3.9 이상
- Git (저장소 분석용)
- 최소 4GB RAM
- 최소 2GB 디스크 공간

## 설치 방법

### 1. 저장소 클론

```bash
git clone https://github.com/codebase-intelligence/codebase-sqlite.git
cd codebase-sqlite
```

### 2. 가상환경 생성 및 활성화

```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 또는
venv\Scripts\activate  # Windows
```

### 3. 의존성 설치

```bash
pip install -e .
```

개발용 의존성도 함께 설치하려면:

```bash
pip install -e ".[dev,test]"
```

### 4. 환경 변수 설정

`.env.example` 파일을 `.env`로 복사하고 API 키를 설정합니다:

```bash
cp .env.example .env
```

`.env` 파일을 편집하여 다음 값들을 설정하세요:

```env
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

### 5. 설치 확인

```bash
codebase-intel --help
```

## 선택적 의존성

### Tree-sitter 언어 파서

더 정확한 코드 분석을 위해 추가 언어 파서를 설치할 수 있습니다:

```bash
pip install tree-sitter-javascript
pip install tree-sitter-typescript
pip install tree-sitter-java
```

### GPU 가속 (선택사항)

벡터 임베딩 생성 속도를 높이려면:

```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 설정

### 기본 설정 파일 생성

```bash
codebase-intel config-show > codebase-intel.yaml
```

### 설정 파일 편집

생성된 `codebase-intel.yaml` 파일을 편집하여 프로젝트에 맞게 설정을 조정하세요.

## 첫 실행

### 1. 코드베이스 인덱싱

```bash
codebase-intel index
```

### 2. 검색 테스트

```bash
codebase-intel search "function that calculates"
```

### 3. LLM 질문 (API 키 필요)

```bash
codebase-intel ask "이 코드베이스의 주요 기능은 무엇인가요?"
```

## 문제 해결

### 일반적인 문제들

1. **ImportError: No module named 'tree_sitter'**
   ```bash
   pip install tree-sitter
   ```

2. **ChromaDB 초기화 오류**
   ```bash
   rm -rf ./chroma_db
   codebase-intel index --force
   ```

3. **SQLite 권한 오류**
   ```bash
   chmod 755 .
   rm -f codebase.db
   codebase-intel index
   ```

### 로그 확인

상세한 로그를 보려면:

```bash
codebase-intel --verbose index
```

디버그 모드:

```bash
codebase-intel --debug search "your query"
```

## 업그레이드

```bash
git pull
pip install -e . --upgrade
```

데이터베이스 스키마가 변경된 경우 재인덱싱이 필요할 수 있습니다:

```bash
codebase-intel index --force
```
