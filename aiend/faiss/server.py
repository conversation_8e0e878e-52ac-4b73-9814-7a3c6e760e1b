#!/usr/bin/env python3
"""
FAISS GPU Server
Simple Flask server for FAISS GPU operations
"""

import os
import json
import numpy as np
from flask import Flask, request, jsonify
import faiss

app = Flask(__name__)

# Global FAISS index
index = None
dimension = 768  # Default dimension for CodeBERT embeddings

def initialize_index():
    """Initialize FAISS GPU index"""
    global index, dimension
    
    # Check if GPU is available
    if faiss.get_num_gpus() > 0:
        print(f"FAISS: Found {faiss.get_num_gpus()} GPU(s)")
        # Create GPU index
        res = faiss.StandardGpuResources()
        index = faiss.GpuIndexFlatIP(res, dimension)
        print("FAISS: GPU index initialized")
    else:
        print("FAISS: No GPU found, using CPU index")
        index = faiss.IndexFlatIP(dimension)
    
    return index

@app.route('/health', methods=['GET'])
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'gpu_count': faiss.get_num_gpus(),
        'index_size': index.ntotal if index else 0
    })

@app.route('/add', methods=['POST'])
def add_vectors():
    """Add vectors to the index"""
    global index
    
    try:
        data = request.json
        vectors = np.array(data['vectors'], dtype=np.float32)
        
        if vectors.shape[1] != dimension:
            return jsonify({'error': f'Vector dimension must be {dimension}'}), 400
        
        # Normalize vectors for cosine similarity
        faiss.normalize_L2(vectors)
        
        # Add to index
        index.add(vectors)
        
        return jsonify({
            'status': 'success',
            'added_count': len(vectors),
            'total_count': index.ntotal
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/search', methods=['POST'])
def search_vectors():
    """Search for similar vectors"""
    global index
    
    try:
        data = request.json
        query_vector = np.array(data['vector'], dtype=np.float32).reshape(1, -1)
        k = data.get('k', 10)
        
        if query_vector.shape[1] != dimension:
            return jsonify({'error': f'Vector dimension must be {dimension}'}), 400
        
        # Normalize query vector
        faiss.normalize_L2(query_vector)
        
        # Search
        scores, indices = index.search(query_vector, k)
        
        return jsonify({
            'status': 'success',
            'scores': scores[0].tolist(),
            'indices': indices[0].tolist()
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/reset', methods=['POST'])
def reset_index():
    """Reset the index"""
    global index
    
    try:
        index.reset()
        return jsonify({
            'status': 'success',
            'message': 'Index reset successfully'
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/info', methods=['GET'])
def info():
    """Get index information"""
    global index
    
    return jsonify({
        'dimension': dimension,
        'total_vectors': index.ntotal if index else 0,
        'gpu_count': faiss.get_num_gpus(),
        'index_type': type(index).__name__ if index else None
    })

if __name__ == '__main__':
    print("Starting FAISS GPU Server...")
    initialize_index()
    app.run(host='0.0.0.0', port=8001, debug=False)
