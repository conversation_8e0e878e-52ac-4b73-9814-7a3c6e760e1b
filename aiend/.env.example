# CodeBase Intelligence System - Environment Variables Example
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Configuration  
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Database Configuration
CODEBASE_DB_PATH=codebase.db

# Vector Store Configuration
CODEBASE_VECTOR_PATH=./chroma_db
CODEBASE_VECTOR_MODEL=microsoft/codebert-base

# Repository Configuration
CODEBASE_REPO_PATH=.

# Logging Configuration
CODEBASE_LOG_LEVEL=INFO

# Optional: Custom API endpoints
# OPENAI_BASE_URL=https://api.openai.com/v1
# ANTHROPIC_BASE_URL=https://api.anthropic.com
