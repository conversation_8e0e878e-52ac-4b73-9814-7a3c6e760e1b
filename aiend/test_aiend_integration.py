#!/usr/bin/env python3
"""
Aiend Integration Test Script

Aiend의 모든 핵심 기능들을 테스트하는 통합 테스트 스크립트
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Dict, Any, List

import requests

# 로깅 설정
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 서비스 URL 설정
AIEND_URL = "http://localhost:7102"  # aiend 서비스 포트
BACKEND_URL = "http://localhost:7101"  # backend 서비스 포트
QDRANT_URL = "http://localhost:7105"   # qdrant 외부 포트

class AiendIntegrationTester:
    """Aiend 통합 테스트 클래스"""
    
    def __init__(self):
        self.test_results = {}
        self.failed_tests = []
    
    async def run_all_tests(self):
        """모든 테스트 실행"""
        logger.info("🚀 Starting Aiend Integration Tests")
        
        tests = [
            ("Service Health Checks", self.test_service_health),
            ("Aiend API Endpoints", self.test_aiend_api),
            ("Vector Store Integration", self.test_vector_store),
            ("Backend-Aiend Webhook", self.test_backend_webhook),
            ("Git Change Workflow", self.test_git_workflow),
            ("Data Layer Components", self.test_data_layer),
            ("Search Functionality", self.test_search_functionality)
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n📋 Running: {test_name}")
            try:
                result = await test_func()
                self.test_results[test_name] = result
                if result.get("success", False):
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    logger.error(f"❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
                    self.failed_tests.append(test_name)
            except Exception as e:
                logger.error(f"❌ {test_name}: EXCEPTION - {str(e)}")
                self.test_results[test_name] = {"success": False, "error": str(e)}
                self.failed_tests.append(test_name)
        
        # 결과 요약
        self.print_test_summary()
    
    async def test_service_health(self) -> Dict[str, Any]:
        """서비스 상태 확인"""
        results = {}
        
        # Aiend 헬스체크
        try:
            response = requests.get(f"{AIEND_URL}/health", timeout=5)
            results["aiend"] = {
                "status": response.status_code,
                "available": response.status_code == 200
            }
        except Exception as e:
            results["aiend"] = {"status": "error", "error": str(e), "available": False}
        
        # Backend 헬스체크
        try:
            response = requests.get(f"{BACKEND_URL}/api/health", timeout=5)
            results["backend"] = {
                "status": response.status_code,
                "available": response.status_code == 200
            }
        except Exception as e:
            results["backend"] = {"status": "error", "error": str(e), "available": False}
        
        # Qdrant 헬스체크 (livez 엔드포인트 사용)
        try:
            response = requests.get(f"{QDRANT_URL}/livez", timeout=5)
            results["qdrant"] = {
                "status": response.status_code,
                "available": response.status_code == 200
            }
        except Exception as e:
            results["qdrant"] = {"status": "error", "error": str(e), "available": False}
        
        all_available = all(service.get("available", False) for service in results.values())
        
        return {
            "success": all_available,
            "services": results,
            "message": "All services available" if all_available else "Some services unavailable"
        }
    
    async def test_aiend_api(self) -> Dict[str, Any]:
        """Aiend API 엔드포인트 테스트"""
        endpoints = [
            ("/", "GET", "Root endpoint"),
            ("/info", "GET", "System info"),
            ("/health/detailed", "GET", "Detailed health"),
            ("/api/v1/search/modes", "GET", "Search modes"),
            ("/api/v1/search/context-types", "GET", "Context types"),
            ("/api/v1/search/vector/status", "GET", "Vector store status")
        ]
        
        results = {}
        
        for endpoint, method, description in endpoints:
            try:
                response = requests.get(f"{AIEND_URL}{endpoint}", timeout=10)
                results[endpoint] = {
                    "status": response.status_code,
                    "success": response.status_code == 200,
                    "description": description
                }
                if response.status_code == 200:
                    try:
                        results[endpoint]["data"] = response.json()
                    except:
                        results[endpoint]["data"] = response.text[:100]
            except Exception as e:
                results[endpoint] = {
                    "status": "error",
                    "success": False,
                    "error": str(e),
                    "description": description
                }
        
        success_count = sum(1 for r in results.values() if r.get("success", False))
        total_count = len(endpoints)
        
        return {
            "success": success_count == total_count,
            "endpoints": results,
            "summary": f"{success_count}/{total_count} endpoints working"
        }
    
    async def test_vector_store(self) -> Dict[str, Any]:
        """벡터 스토어 테스트"""
        try:
            # 벡터 스토어 상태 확인
            response = requests.get(f"{AIEND_URL}/api/v1/search/vector/status", timeout=10)
            
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Vector store status check failed: {response.status_code}"
                }
            
            status_data = response.json()
            vector_available = status_data.get("available", False)
            
            if not vector_available:
                return {
                    "success": False,
                    "error": "Vector store not available",
                    "status": status_data
                }
            
            # 간단한 벡터 검색 테스트
            search_payload = {
                "query": "function authentication",
                "max_results": 5
            }
            
            search_response = requests.post(
                f"{AIEND_URL}/api/v1/search/vector",
                json=search_payload,
                timeout=15
            )
            
            return {
                "success": True,
                "vector_status": status_data,
                "search_test": {
                    "status": search_response.status_code,
                    "results_count": len(search_response.json().get("results", [])) if search_response.status_code == 200 else 0
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_backend_webhook(self) -> Dict[str, Any]:
        """Backend-Aiend Webhook 테스트"""
        try:
            # 테스트용 webhook 페이로드 (실제 존재하는 경로 사용)
            webhook_payload = {
                "repository_id": "test-repo-123",
                "repository_name": "test-repository",
                "repository_path": "/home/<USER>/dev/codebase-sqlite",  # 실제 존재하는 경로
                "action": "pull",
                "branch": "main",
                "commit_hash": "abc123def456",
                "changed_files": ["test.py", "README.md"],
                "metadata": {
                    "test": True
                }
            }
            
            # Webhook 엔드포인트 테스트
            response = requests.post(
                f"{AIEND_URL}/api/v1/webhook/git-changes",
                json=webhook_payload,
                timeout=10
            )
            
            if response.status_code == 200:
                webhook_result = response.json()
                return {
                    "success": True,
                    "webhook_response": webhook_result,
                    "task_id": webhook_result.get("task_id")
                }
            else:
                return {
                    "success": False,
                    "error": f"Webhook failed: {response.status_code} - {response.text}"
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_git_workflow(self) -> Dict[str, Any]:
        """Git 워크플로우 테스트"""
        try:
            # Backend에서 저장소 목록 조회
            response = requests.get(f"{BACKEND_URL}/api/repositories", timeout=10)
            
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"Failed to get repositories: {response.status_code}"
                }
            
            repos_data = response.json()
            repositories = repos_data.get("data", {}).get("repositories", [])
            
            if not repositories:
                return {
                    "success": True,
                    "message": "No repositories found - workflow test skipped",
                    "repositories_count": 0
                }
            
            # 첫 번째 저장소로 테스트
            test_repo = repositories[0]
            repo_id = test_repo["id"]
            
            # Aiend 상태 확인
            aiend_status_response = requests.get(
                f"{BACKEND_URL}/api/repositories/{repo_id}/aiend-status",
                timeout=10
            )
            
            return {
                "success": True,
                "repositories_count": len(repositories),
                "test_repository": {
                    "id": repo_id,
                    "name": test_repo.get("name", ""),
                    "aiend_status": aiend_status_response.json() if aiend_status_response.status_code == 200 else None
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_data_layer(self) -> Dict[str, Any]:
        """데이터 레이어 컴포넌트 테스트"""
        try:
            # 간단한 임포트 테스트
            from src.codebase_intelligence.core.persistent import (
                SQLiteManager, VectorStore, GitAnalyzer, CodebaseAnalyzer
            )
            
            # 설정 로드 테스트
            from src.codebase_intelligence.utils.config import Config
            config = Config.load()
            
            # 컴포넌트 초기화 테스트
            components = {}
            
            try:
                db_manager = SQLiteManager(":memory:")  # 메모리 DB 사용
                components["sqlite"] = "initialized"
            except Exception as e:
                components["sqlite"] = f"error: {str(e)}"
            
            try:
                vector_store = VectorStore(host="qdrant", port=6333)
                components["vector_store"] = "initialized"
            except Exception as e:
                components["vector_store"] = f"error: {str(e)}"
            
            try:
                git_analyzer = GitAnalyzer("/tmp")  # 임시 경로
                components["git_analyzer"] = "initialized"
            except Exception as e:
                components["git_analyzer"] = f"error: {str(e)}"
            
            return {
                "success": True,
                "config_loaded": True,
                "components": components
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    async def test_search_functionality(self) -> Dict[str, Any]:
        """검색 기능 테스트"""
        try:
            # 검색 모드 조회
            modes_response = requests.get(f"{AIEND_URL}/api/v1/search/modes", timeout=10)
            
            # 컨텍스트 타입 조회
            context_response = requests.get(f"{AIEND_URL}/api/v1/search/context-types", timeout=10)
            
            results = {
                "modes_available": modes_response.status_code == 200,
                "context_types_available": context_response.status_code == 200
            }
            
            if modes_response.status_code == 200:
                results["search_modes"] = modes_response.json().get("modes", [])
            
            if context_response.status_code == 200:
                results["context_types"] = context_response.json().get("context_types", [])
            
            return {
                "success": results["modes_available"] and results["context_types_available"],
                "details": results
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    def print_test_summary(self):
        """테스트 결과 요약 출력"""
        logger.info("\n" + "="*60)
        logger.info("🏁 AIEND INTEGRATION TEST SUMMARY")
        logger.info("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = total_tests - len(self.failed_tests)
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {len(self.failed_tests)}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if self.failed_tests:
            logger.info("\n❌ Failed Tests:")
            for test_name in self.failed_tests:
                error = self.test_results[test_name].get("error", "Unknown error")
                logger.info(f"  - {test_name}: {error}")
        
        logger.info("\n📊 Detailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            logger.info(f"  {status} {test_name}")
        
        logger.info("="*60)
        
        if len(self.failed_tests) == 0:
            logger.info("🎉 All tests passed! Aiend integration is working correctly.")
        else:
            logger.info("⚠️  Some tests failed. Please check the logs above.")


async def main():
    """메인 함수"""
    tester = AiendIntegrationTester()
    await tester.run_all_tests()
    
    # 실패한 테스트가 있으면 exit code 1
    if tester.failed_tests:
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
