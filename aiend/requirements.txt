# Core dependencies
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0
python-dotenv>=1.0.0
python-multipart>=0.0.6

# CLI and utilities
click>=8.0.0
rich>=13.0.0
typer>=0.9.0

# Database
sqlalchemy>=2.0.0
sqlite-utils>=3.34.0
alembic>=1.13.0

# Vector search and embeddings
chromadb>=0.4.0
qdrant-client>=1.7.0
sentence-transformers>=2.2.0
numpy>=1.24.0
faiss-cpu>=1.7.4

# Git analysis
gitpython>=3.1.0
pygit2>=1.13.0

# LLM integrations
openai>=1.0.0
anthropic>=0.7.0
httpx>=0.25.0
ollama>=0.1.0

# Code analysis
tree-sitter>=0.20.0
tree-sitter-python>=0.20.0
tree-sitter-javascript>=0.20.0
tree-sitter-typescript>=0.20.0
ast-grep-py>=0.12.0

# Utilities
pathspec>=0.11.0
watchdog>=3.0.0
aiofiles>=23.0.0
asyncio-throttle>=1.0.0
pyyaml>=6.0.0

# Development dependencies (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
pytest-cov>=4.0.0
black>=23.0.0
isort>=5.12.0
flake8>=6.0.0
mypy>=1.5.0
pre-commit>=3.0.0

# FastAPI specific
jinja2>=3.1.0
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Machine Learning (future)
scikit-learn>=1.3.0
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Monitoring and logging
structlog>=23.0.0
prometheus-client>=0.19.0
