# CodeBase Intelligence System - Default Configuration

# Database configuration
database:
  path: "codebase.db"
  backup_enabled: true
  backup_interval: 3600  # seconds
  vacuum_interval: 86400  # seconds

# Vector store configuration
vector_store:
  type: "qdrant"  # qdrant or chromadb
  host: "qdrant"  # Docker service name
  port: 6333  # Internal Qdrant port
  path: "./chroma_db"  # for chromadb fallback
  model: "dengcao/Qwen3-Embedding-8B:Q5_K_M"
  collection_name: "codebase"
  chunk_size: 512
  chunk_overlap: 50
  vector_size: 4096

# Repository configuration
repository:
  path: "."
  exclude_patterns:
    - "__pycache__"
    - ".git"
    - ".svn"
    - ".hg"
    - "node_modules"
    - ".venv"
    - "venv"
    - "env"
    - ".pytest_cache"
    - ".mypy_cache"
    - ".tox"
    - "build"
    - "dist"
    - "target"
    - "out"
    - ".idea"
    - ".vscode"
    - ".vs"
  include_extensions:
    - ".py"
    - ".js"
    - ".ts"
    - ".java"
    - ".cpp"
    - ".cc"
    - ".cxx"
    - ".cs"
    - ".go"
    - ".rs"
    - ".rb"
    - ".php"
    - ".swift"
    - ".kt"
  max_file_size: 1048576  # 1MB

# LLM configuration
llm:
  default_provider: ollama
  routing_strategy: auto
  max_context_tokens: 8000
  response_timeout: 60
  providers:
    ollama:
      name: ollama
      api_key: "ollama" # Not required, but field is mandatory
      base_url: http://172.17.0.1:11434
      models:
        - gemma3:12b
        - phi4:latest
      default_model: gemma3:12b
      max_tokens: 4096
      temperature: 0.1
      enabled: true
    # openai:
    #   name: openai
    #   api_key: ${OPENAI_API_KEY}
    #   models:
    #     - gpt-4
    #     - gpt-3.5-turbo
    #   default_model: gpt-4
    #   max_tokens: 4096
    #   temperature: 0.1
    #   enabled: true
    # anthropic:
    #   name: anthropic
    #   api_key: ${ANTHROPIC_API_KEY}
    #   models:
    #     - claude-3-sonnet-********
    #     - claude-3-haiku-********
    #   default_model: claude-3-sonnet-********
    #   max_tokens: 4096
    #   temperature: 0.1
    #   enabled: true
  response_timeout: 600  # 10분으로 증가 (백그라운드 작업용)

# Reranker configuration
reranker:
  enabled: true
  model: "dengcao/Qwen3-Reranker-4B:Q5_K_M"
  api_base: "http://172.17.0.1:11434/api/generate"
  top_n: 5

# Search configuration
search:
  default_mode: "auto"  # auto, semantic, structural, temporal, hybrid
  max_results: 10
  semantic_weight: 0.6
  structural_weight: 0.2
  temporal_weight: 0.2
  enable_caching: true
  cache_ttl: 3600  # seconds

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_path: null  # Set to enable file logging
  max_file_size: 10485760  # 10MB
  backup_count: 5
