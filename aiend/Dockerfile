# Use Python 3.11 slim image as base
FROM python:3.11-slim

ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

WORKDIR /app

RUN apt-get update && apt-get install -y \
    git \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

COPY . .
RUN if [ ! -f .env ]; then cp .env.example .env; fi

RUN pip install -e .

RUN mkdir -p /app/data /app/logs /app/models

RUN useradd --create-home --shell /bin/bash aiend && \
    chown -R aiend:aiend /app
USER aiend

# 여기 포트는 컨테이너 기준, FastAPI 앱이 8000에서 떠있으니 8000이 직관적임
EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD ["uvicorn", "codebase_intelligence.api.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
