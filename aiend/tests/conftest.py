"""
Pytest configuration and fixtures
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from typing import Generator

from codebase_intelligence.core.persistence.sqlite_manager import SQLiteManager
from codebase_intelligence.core.persistence.vector_store import VectorStore
from codebase_intelligence.utils.config import Config


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """임시 디렉토리 픽스처"""
    temp_path = Path(tempfile.mkdtemp())
    try:
        yield temp_path
    finally:
        shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def sample_repo(temp_dir: Path) -> Path:
    """샘플 Git 저장소 픽스처"""
    repo_path = temp_dir / "sample_repo"
    repo_path.mkdir()
    
    # 샘플 Python 파일 생성
    (repo_path / "main.py").write_text('''
"""Main module for sample application"""

def hello_world(name: str = "World") -> str:
    """
    Greet someone with a hello message.
    
    Args:
        name: The name to greet
        
    Returns:
        A greeting message
    """
    return f"Hello, {name}!"


class Calculator:
    """Simple calculator class"""
    
    def add(self, a: int, b: int) -> int:
        """Add two numbers"""
        return a + b
    
    def multiply(self, a: int, b: int) -> int:
        """Multiply two numbers"""
        return a * b


if __name__ == "__main__":
    print(hello_world())
    calc = Calculator()
    print(f"2 + 3 = {calc.add(2, 3)}")
''')
    
    # 샘플 유틸리티 파일
    (repo_path / "utils.py").write_text('''
"""Utility functions"""

from typing import List, Optional


def find_max(numbers: List[int]) -> Optional[int]:
    """Find the maximum number in a list"""
    if not numbers:
        return None
    return max(numbers)


def is_even(number: int) -> bool:
    """Check if a number is even"""
    return number % 2 == 0
''')
    
    # Git 저장소 초기화
    import git
    try:
        repo = git.Repo.init(repo_path)
        repo.index.add(["main.py", "utils.py"])
        repo.index.commit("Initial commit")
    except Exception:
        # Git이 없는 환경에서는 스킵
        pass
    
    return repo_path


@pytest.fixture
def test_config(temp_dir: Path) -> Config:
    """테스트용 설정 픽스처"""
    config = Config()
    config.database.path = str(temp_dir / "test.db")
    config.vector_store.path = str(temp_dir / "test_chroma")
    config.repository.path = str(temp_dir)
    return config


@pytest.fixture
def db_manager(test_config: Config) -> Generator[SQLiteManager, None, None]:
    """SQLite 관리자 픽스처"""
    manager = SQLiteManager(test_config.database.path)
    try:
        yield manager
    finally:
        manager.close()


@pytest.fixture
def vector_store(test_config: Config) -> Generator[VectorStore, None, None]:
    """벡터 스토어 픽스처"""
    store = VectorStore(
        persist_directory=test_config.vector_store.path,
        model_name="all-MiniLM-L6-v2"  # 테스트용 경량 모델
    )
    try:
        yield store
    finally:
        # 정리 작업
        pass


@pytest.fixture
def sample_code_file():
    """샘플 코드 파일 내용"""
    return '''
def fibonacci(n: int) -> int:
    """
    Calculate the nth Fibonacci number.
    
    Args:
        n: The position in the Fibonacci sequence
        
    Returns:
        The nth Fibonacci number
    """
    if n <= 1:
        return n
    return fibonacci(n - 1) + fibonacci(n - 2)


class MathUtils:
    """Mathematical utility functions"""
    
    @staticmethod
    def factorial(n: int) -> int:
        """Calculate factorial of n"""
        if n <= 1:
            return 1
        return n * MathUtils.factorial(n - 1)
    
    @staticmethod
    def gcd(a: int, b: int) -> int:
        """Calculate greatest common divisor"""
        while b:
            a, b = b, a % b
        return a
'''
