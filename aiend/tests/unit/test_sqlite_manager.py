"""
Unit tests for SQLiteManager
"""

import pytest
from datetime import datetime

from codebase_intelligence.core.persistence.sqlite_manager import SQLiteManager
from codebase_intelligence.core.persistence.models import (
    CodeFile, CodeFunction, CodeClass, GitCommit, CodeChange,
    LanguageType, ChangeType
)


class TestSQLiteManager:
    """SQLiteManager 테스트"""
    
    def test_initialization(self, db_manager: SQLiteManager):
        """데이터베이스 초기화 테스트"""
        stats = db_manager.get_database_stats()
        assert isinstance(stats, dict)
        assert all(count == 0 for count in stats.values())
    
    def test_add_code_file(self, db_manager: SQLiteManager):
        """코드 파일 추가 테스트"""
        code_file = CodeFile(
            path="test.py",
            language=LanguageType.PYTHON,
            size_bytes=1024,
            lines_count=50,
            hash_sha256="abc123",
            last_modified=datetime.now()
        )
        
        file_id = db_manager.add_code_file(code_file)
        assert file_id > 0
        
        # 조회 테스트
        retrieved_file = db_manager.get_code_file(file_id)
        assert retrieved_file is not None
        assert retrieved_file.path == "test.py"
        assert retrieved_file.language == LanguageType.PYTHON
    
    def test_get_code_file_by_path(self, db_manager: SQLiteManager):
        """경로로 코드 파일 조회 테스트"""
        code_file = CodeFile(
            path="utils/helper.py",
            language=LanguageType.PYTHON,
            size_bytes=512,
            lines_count=25,
            hash_sha256="def456",
            last_modified=datetime.now()
        )
        
        file_id = db_manager.add_code_file(code_file)
        
        retrieved_file = db_manager.get_code_file_by_path("utils/helper.py")
        assert retrieved_file is not None
        assert retrieved_file.id == file_id
    
    def test_add_code_function(self, db_manager: SQLiteManager):
        """함수 추가 테스트"""
        # 먼저 파일 추가
        code_file = CodeFile(
            path="functions.py",
            language=LanguageType.PYTHON,
            size_bytes=2048,
            lines_count=100,
            hash_sha256="func123",
            last_modified=datetime.now()
        )
        file_id = db_manager.add_code_file(code_file)
        
        # 함수 추가
        code_function = CodeFunction(
            file_id=file_id,
            name="test_function",
            full_name="test_function",
            start_line=10,
            end_line=20,
            parameters=[{"name": "x", "type": "int"}],
            return_type="str",
            docstring="Test function",
            is_async=False,
            is_method=False,
            complexity=2
        )
        
        func_id = db_manager.add_code_function(code_function)
        assert func_id > 0
        
        # 파일의 함수들 조회
        functions = db_manager.get_functions_by_file(file_id)
        assert len(functions) == 1
        assert functions[0].name == "test_function"
    
    def test_search_functions(self, db_manager: SQLiteManager):
        """함수 검색 테스트"""
        # 테스트 데이터 준비
        code_file = CodeFile(
            path="search_test.py",
            language=LanguageType.PYTHON,
            size_bytes=1024,
            lines_count=50,
            hash_sha256="search123",
            last_modified=datetime.now()
        )
        file_id = db_manager.add_code_file(code_file)
        
        # 여러 함수 추가
        functions = [
            ("calculate_sum", "Calculate sum of numbers"),
            ("calculate_average", "Calculate average"),
            ("process_data", "Process input data")
        ]
        
        for name, docstring in functions:
            code_function = CodeFunction(
                file_id=file_id,
                name=name,
                full_name=name,
                start_line=1,
                end_line=10,
                docstring=docstring
            )
            db_manager.add_code_function(code_function)
        
        # 검색 테스트
        results = db_manager.search_functions("calculate")
        assert len(results) == 2
        assert all("calculate" in func.name.lower() for func in results)
    
    def test_add_git_commit(self, db_manager: SQLiteManager):
        """Git 커밋 추가 테스트"""
        git_commit = GitCommit(
            hash="abc123def456",
            author_name="Test Author",
            author_email="<EMAIL>",
            committer_name="Test Author",
            committer_email="<EMAIL>",
            message="Test commit message",
            timestamp=datetime.now(),
            parent_hashes=["parent123"]
        )
        
        commit_id = db_manager.add_git_commit(git_commit)
        assert commit_id > 0
        
        # 해시로 조회
        retrieved_commit = db_manager.get_git_commit_by_hash("abc123def456")
        assert retrieved_commit is not None
        assert retrieved_commit.author_name == "Test Author"
    
    def test_add_code_change(self, db_manager: SQLiteManager):
        """코드 변경 추가 테스트"""
        # 커밋과 파일 먼저 추가
        git_commit = GitCommit(
            hash="change123",
            author_name="Change Author",
            author_email="<EMAIL>",
            committer_name="Change Author",
            committer_email="<EMAIL>",
            message="Change commit",
            timestamp=datetime.now()
        )
        commit_id = db_manager.add_git_commit(git_commit)
        
        code_file = CodeFile(
            path="changed_file.py",
            language=LanguageType.PYTHON,
            size_bytes=1024,
            lines_count=50,
            hash_sha256="change456",
            last_modified=datetime.now()
        )
        file_id = db_manager.add_code_file(code_file)
        
        # 변경 추가
        code_change = CodeChange(
            commit_id=commit_id,
            file_id=file_id,
            change_type=ChangeType.MODIFIED,
            new_path="changed_file.py",
            lines_added=10,
            lines_deleted=5
        )
        
        change_id = db_manager.add_code_change(code_change)
        assert change_id > 0
        
        # 파일의 변경 이력 조회
        changes = db_manager.get_file_changes(file_id)
        assert len(changes) == 1
        assert changes[0].change_type == ChangeType.MODIFIED
    
    def test_database_stats(self, db_manager: SQLiteManager):
        """데이터베이스 통계 테스트"""
        # 초기 상태
        stats = db_manager.get_database_stats()
        assert stats['files'] == 0
        assert stats['functions'] == 0
        
        # 데이터 추가 후
        code_file = CodeFile(
            path="stats_test.py",
            language=LanguageType.PYTHON,
            size_bytes=1024,
            lines_count=50,
            hash_sha256="stats123",
            last_modified=datetime.now()
        )
        db_manager.add_code_file(code_file)
        
        stats = db_manager.get_database_stats()
        assert stats['files'] == 1
    
    def test_update_code_file(self, db_manager: SQLiteManager):
        """코드 파일 업데이트 테스트"""
        code_file = CodeFile(
            path="update_test.py",
            language=LanguageType.PYTHON,
            size_bytes=1024,
            lines_count=50,
            hash_sha256="update123",
            last_modified=datetime.now()
        )
        file_id = db_manager.add_code_file(code_file)
        
        # 업데이트
        updates = {
            "size_bytes": 2048,
            "lines_count": 100,
            "hash_sha256": "updated456"
        }
        
        success = db_manager.update_code_file(file_id, updates)
        assert success
        
        # 확인
        updated_file = db_manager.get_code_file(file_id)
        assert updated_file.size_bytes == 2048
        assert updated_file.lines_count == 100
        assert updated_file.hash_sha256 == "updated456"
    
    def test_delete_code_file(self, db_manager: SQLiteManager):
        """코드 파일 삭제 테스트"""
        code_file = CodeFile(
            path="delete_test.py",
            language=LanguageType.PYTHON,
            size_bytes=1024,
            lines_count=50,
            hash_sha256="delete123",
            last_modified=datetime.now()
        )
        file_id = db_manager.add_code_file(code_file)
        
        # 삭제
        success = db_manager.delete_code_file(file_id)
        assert success
        
        # 확인
        deleted_file = db_manager.get_code_file(file_id)
        assert deleted_file is None
    
    def test_list_code_files(self, db_manager: SQLiteManager):
        """코드 파일 목록 조회 테스트"""
        # 여러 파일 추가
        languages = [LanguageType.PYTHON, LanguageType.JAVASCRIPT, LanguageType.PYTHON]
        for i, lang in enumerate(languages):
            code_file = CodeFile(
                path=f"file_{i}.{lang.value}",
                language=lang,
                size_bytes=1024,
                lines_count=50,
                hash_sha256=f"hash{i}",
                last_modified=datetime.now()
            )
            db_manager.add_code_file(code_file)
        
        # 전체 목록
        all_files = db_manager.list_code_files()
        assert len(all_files) == 3
        
        # Python 파일만
        python_files = db_manager.list_code_files(language=LanguageType.PYTHON)
        assert len(python_files) == 2
        assert all(f.language == LanguageType.PYTHON for f in python_files)
