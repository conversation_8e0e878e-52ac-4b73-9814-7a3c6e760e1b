"""
Configuration Management

시스템 설정 관리 클래스
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
import yaml
from dotenv import load_dotenv

# .env 파일 로드
load_dotenv()


@dataclass
class DatabaseConfig:
    """데이터베이스 설정"""
    path: str = "codebase.db"
    backup_enabled: bool = True
    backup_interval: int = 3600  # seconds
    vacuum_interval: int = 86400  # seconds


@dataclass
class VectorStoreConfig:
    """벡터 스토어 설정"""
    type: str = "qdrant"  # qdrant or chromadb
    host: str = "qdrant"  # Docker 환경에서는 서비스명 사용
    port: int = 6333
    path: str = "./chroma_db"  # for chromadb fallback
    model: str = "dengcao/Qwen3-Embedding-8B:Q5_K_M"  # Ollama 모델
    collection_name: str = "codebase"
    chunk_size: int = 512
    chunk_overlap: int = 50
    vector_size: int = 4096  # Qwen3-Embedding-8B 모델의 벡터 크기


@dataclass
class RepositoryConfig:
    """저장소 설정"""
    path: str = "."
    exclude_patterns: List[str] = field(default_factory=lambda: [
        "__pycache__", ".git", ".svn", ".hg",
        "node_modules", ".venv", "venv", "env",
        ".pytest_cache", ".mypy_cache", ".tox",
        "build", "dist", "target", "out",
        ".idea", ".vscode", ".vs"
    ])
    include_extensions: List[str] = field(default_factory=lambda: [
        ".py", ".js", ".ts", ".java", ".cpp", ".cc", ".cxx",
        ".cs", ".go", ".rs", ".rb", ".php", ".swift", ".kt"
    ])
    max_file_size: int = 1024 * 1024  # 1MB


@dataclass
class LLMProviderConfig:
    """LLM 제공자 설정"""
    name: str
    api_key: str
    base_url: Optional[str] = None
    models: List[str] = field(default_factory=list)
    default_model: Optional[str] = None
    max_tokens: int = 4096
    temperature: float = 0.1
    enabled: bool = True


def _default_llm_providers():
    return {
        "ollama": LLMProviderConfig(
            name="ollama",
            api_key="ollama",  # Not required for Ollama, but field is mandatory
            base_url="http://host.docker.internal:11434",
            models=["gemma3:12b", "phi4:latest"],
            default_model="gemma3:12b",
            enabled=True
        )
    }


@dataclass
class LLMConfig:
    """LLM 설정"""
    providers: Dict[str, LLMProviderConfig] = field(default_factory=_default_llm_providers)
    default_provider: str = "ollama"
    routing_strategy: str = "auto"  # auto, single, multi, hybrid
    max_context_tokens: int = 8000
    response_timeout: int = 60


@dataclass
class SearchConfig:
    """검색 설정"""
    default_mode: str = "auto"  # auto, semantic, structural, temporal, hybrid
    max_results: int = 10
    semantic_weight: float = 0.6
    structural_weight: float = 0.2
    temporal_weight: float = 0.2
    enable_caching: bool = True
    cache_ttl: int = 3600  # seconds


@dataclass
class LoggingConfig:
    """로깅 설정"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5


@dataclass
class RerankerConfig:
    """Reranker 설정"""
    enabled: bool = True
    model: str = "dengcao/Qwen3-Reranker-4B:Q5_K_M"
    api_base: str = "http://host.docker.internal:11434/api/generate"
    top_n: int = 5


@dataclass
class Config:
    """메인 설정 클래스"""
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    vector_store: VectorStoreConfig = field(default_factory=VectorStoreConfig)
    repository: RepositoryConfig = field(default_factory=RepositoryConfig)
    llm: LLMConfig = field(default_factory=LLMConfig)
    search: SearchConfig = field(default_factory=SearchConfig)
    reranker: RerankerConfig = field(default_factory=RerankerConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    @classmethod
    def load(cls, config_path: Optional[str] = None) -> "Config":
        """설정 파일에서 설정 로드"""
        config = cls()
        
        # 기본 설정 파일 경로들
        default_paths = [
            "codebase-intel.yaml",
            "codebase-intel.yml", 
            ".codebase-intel.yaml",
            ".codebase-intel.yml",
            os.path.expanduser("~/.codebase-intel.yaml"),
            os.path.expanduser("~/.config/codebase-intel/config.yaml")
        ]
        
        # 설정 파일 찾기
        if config_path:
            config_file = Path(config_path)
        else:
            config_file = None
            for path in default_paths:
                if Path(path).exists():
                    config_file = Path(path)
                    break
        
        # 설정 파일 로드
        if config_file and config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = yaml.safe_load(f)
                config = config._update_from_dict(config_data)
            except Exception as e:
                print(f"Warning: Failed to load config file {config_file}: {e}")
        
        # 환경 변수로 오버라이드
        config = config._update_from_env()
        
        return config
    
    def _update_from_dict(self, data: Dict[str, Any]) -> "Config":
        """딕셔너리에서 설정 업데이트"""
        if not data:
            return self
        
        # Database 설정
        if "database" in data:
            db_data = data["database"]
            self.database = DatabaseConfig(
                path=db_data.get("path", self.database.path),
                backup_enabled=db_data.get("backup_enabled", self.database.backup_enabled),
                backup_interval=db_data.get("backup_interval", self.database.backup_interval),
                vacuum_interval=db_data.get("vacuum_interval", self.database.vacuum_interval)
            )
        
        # Vector Store 설정
        if "vector_store" in data:
            vs_data = data["vector_store"]
            self.vector_store = VectorStoreConfig(
                type=vs_data.get("type", self.vector_store.type),
                host=vs_data.get("host", self.vector_store.host),
                port=vs_data.get("port", self.vector_store.port),
                path=vs_data.get("path", self.vector_store.path),
                model=vs_data.get("model", self.vector_store.model),
                collection_name=vs_data.get("collection_name", self.vector_store.collection_name),
                chunk_size=vs_data.get("chunk_size", self.vector_store.chunk_size),
                chunk_overlap=vs_data.get("chunk_overlap", self.vector_store.chunk_overlap),
                vector_size=vs_data.get("vector_size", self.vector_store.vector_size)
            )
        
        # Repository 설정
        if "repository" in data:
            repo_data = data["repository"]
            self.repository = RepositoryConfig(
                path=repo_data.get("path", self.repository.path),
                exclude_patterns=repo_data.get("exclude_patterns", self.repository.exclude_patterns),
                include_extensions=repo_data.get("include_extensions", self.repository.include_extensions),
                max_file_size=repo_data.get("max_file_size", self.repository.max_file_size)
            )
        
        # LLM 설정
        if "llm" in data:
            llm_data = data["llm"]
            
            # 제공자 설정
            providers = {}
            if "providers" in llm_data:
                for name, provider_data in llm_data["providers"].items():
                    providers[name] = LLMProviderConfig(
                        name=name,
                        api_key=provider_data.get("api_key", ""),
                        base_url=provider_data.get("base_url"),
                        models=provider_data.get("models", []),
                        default_model=provider_data.get("default_model"),
                        max_tokens=provider_data.get("max_tokens", 4096),
                        temperature=provider_data.get("temperature", 0.1),
                        enabled=provider_data.get("enabled", True)
                    )
            
            self.llm = LLMConfig(
                providers=providers,
                default_provider=llm_data.get("default_provider", self.llm.default_provider),
                routing_strategy=llm_data.get("routing_strategy", self.llm.routing_strategy),
                max_context_tokens=llm_data.get("max_context_tokens", self.llm.max_context_tokens),
                response_timeout=llm_data.get("response_timeout", self.llm.response_timeout)
            )
        
        # Search 설정
        if "search" in data:
            search_data = data["search"]
            self.search = SearchConfig(
                default_mode=search_data.get("default_mode", self.search.default_mode),
                max_results=search_data.get("max_results", self.search.max_results),
                semantic_weight=search_data.get("semantic_weight", self.search.semantic_weight),
                structural_weight=search_data.get("structural_weight", self.search.structural_weight),
                temporal_weight=search_data.get("temporal_weight", self.search.temporal_weight),
                enable_caching=search_data.get("enable_caching", self.search.enable_caching),
                cache_ttl=search_data.get("cache_ttl", self.search.cache_ttl)
            )
        
        # Logging 설정
        if "logging" in data:
            log_data = data["logging"]
            self.logging = LoggingConfig(
                level=log_data.get("level", self.logging.level),
                format=log_data.get("format", self.logging.format),
                file_path=log_data.get("file_path", self.logging.file_path),
                max_file_size=log_data.get("max_file_size", self.logging.max_file_size),
                backup_count=log_data.get("backup_count", self.logging.backup_count)
            )
        
        return self
    
    def _update_from_env(self) -> "Config":
        """환경 변수에서 설정 업데이트"""
        # 데이터베이스 설정
        if os.getenv("CODEBASE_DB_PATH"):
            self.database.path = os.getenv("CODEBASE_DB_PATH")
        
        # 벡터 스토어 설정
        if os.getenv("CODEBASE_VECTOR_PATH"):
            self.vector_store.path = os.getenv("CODEBASE_VECTOR_PATH")
        if os.getenv("CODEBASE_VECTOR_MODEL"):
            self.vector_store.model = os.getenv("CODEBASE_VECTOR_MODEL")
        
        # 저장소 설정
        if os.getenv("CODEBASE_REPO_PATH"):
            self.repository.path = os.getenv("CODEBASE_REPO_PATH")
        
        # LLM API 키들
        openai_key = os.getenv("OPENAI_API_KEY")
        if openai_key:
            if "openai" not in self.llm.providers:
                self.llm.providers["openai"] = LLMProviderConfig(
                    name="openai",
                    api_key=openai_key,
                    models=["gpt-4", "gpt-3.5-turbo"],
                    default_model="gpt-4"
                )
            else:
                self.llm.providers["openai"].api_key = openai_key
        
        anthropic_key = os.getenv("ANTHROPIC_API_KEY")
        if anthropic_key:
            if "anthropic" not in self.llm.providers:
                self.llm.providers["anthropic"] = LLMProviderConfig(
                    name="anthropic",
                    api_key=anthropic_key,
                    models=["claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
                    default_model="claude-3-sonnet-20240229"
                )
            else:
                self.llm.providers["anthropic"].api_key = anthropic_key
        
        # 로깅 레벨
        if os.getenv("CODEBASE_LOG_LEVEL"):
            self.logging.level = os.getenv("CODEBASE_LOG_LEVEL")
        
        return self
    
    def to_dict(self) -> Dict[str, Any]:
        """설정을 딕셔너리로 변환"""
        return {
            "database": {
                "path": self.database.path,
                "backup_enabled": self.database.backup_enabled,
                "backup_interval": self.database.backup_interval,
                "vacuum_interval": self.database.vacuum_interval
            },
            "vector_store": {
                "path": self.vector_store.path,
                "model": self.vector_store.model,
                "collection_name": self.vector_store.collection_name,
                "chunk_size": self.vector_store.chunk_size,
                "chunk_overlap": self.vector_store.chunk_overlap
            },
            "repository": {
                "path": self.repository.path,
                "exclude_patterns": self.repository.exclude_patterns,
                "include_extensions": self.repository.include_extensions,
                "max_file_size": self.repository.max_file_size
            },
            "llm": {
                "providers": {
                    name: {
                        "api_key": "***" if provider.api_key else "",
                        "base_url": provider.base_url,
                        "models": provider.models,
                        "default_model": provider.default_model,
                        "max_tokens": provider.max_tokens,
                        "temperature": provider.temperature,
                        "enabled": provider.enabled
                    }
                    for name, provider in self.llm.providers.items()
                },
                "default_provider": self.llm.default_provider,
                "routing_strategy": self.llm.routing_strategy,
                "max_context_tokens": self.llm.max_context_tokens,
                "response_timeout": self.llm.response_timeout
            },
            "search": {
                "default_mode": self.search.default_mode,
                "max_results": self.search.max_results,
                "semantic_weight": self.search.semantic_weight,
                "structural_weight": self.search.structural_weight,
                "temporal_weight": self.search.temporal_weight,
                "enable_caching": self.search.enable_caching,
                "cache_ttl": self.search.cache_ttl
            },
            "logging": {
                "level": self.logging.level,
                "format": self.logging.format,
                "file_path": self.logging.file_path,
                "max_file_size": self.logging.max_file_size,
                "backup_count": self.logging.backup_count
            }
        }
    
    def to_yaml(self) -> str:
        """설정을 YAML 문자열로 변환"""
        return yaml.dump(self.to_dict(), default_flow_style=False, allow_unicode=True)
    
    def save(self, config_path: str):
        """설정을 파일로 저장"""
        config_file = Path(config_path)
        config_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(self.to_yaml())
