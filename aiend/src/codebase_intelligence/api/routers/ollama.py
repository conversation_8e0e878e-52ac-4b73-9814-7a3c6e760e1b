"""
Ollama Management API Router

Ollama 모델 관리 API
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field

from ...core.llm.providers.ollama_provider import OllamaProvider
from ..dependencies import require_permission

logger = logging.getLogger(__name__)

router = APIRouter()

# 전역 Ollama 제공자 인스턴스
_ollama_provider: Optional[OllamaProvider] = None


def get_ollama_provider() -> OllamaProvider:
    """Ollama 제공자 의존성"""
    global _ollama_provider
    if _ollama_provider is None:
        _ollama_provider = OllamaProvider()
    return _ollama_provider


class ModelInfo(BaseModel):
    """모델 정보"""
    name: str
    size: Optional[str] = None
    digest: Optional[str] = None
    modified_at: Optional[str] = None
    details: Optional[Dict[str, Any]] = None


class ModelsResponse(BaseModel):
    """모델 목록 응답"""
    models: List[ModelInfo]
    total: int


class PullRequest(BaseModel):
    """모델 다운로드 요청"""
    model_name: str = Field(..., description="다운로드할 모델명")


class PullResponse(BaseModel):
    """모델 다운로드 응답"""
    task_id: str
    status: str
    message: str


@router.get("/models", response_model=ModelsResponse)
async def list_models(
    ollama: OllamaProvider = Depends(get_ollama_provider),
    user=Depends(require_permission("read"))
):
    """설치된 모델 목록"""
    try:
        if not await ollama.is_available():
            raise HTTPException(status_code=503, detail="Ollama service not available")
        
        model_names = await ollama.get_available_models()
        
        models = []
        for name in model_names:
            model_info = await ollama.get_model_info(name)
            models.append(ModelInfo(
                name=name,
                size=model_info.get("size") if model_info else None,
                digest=model_info.get("digest") if model_info else None,
                modified_at=model_info.get("modified_at") if model_info else None,
                details=model_info.get("details") if model_info else None
            ))
        
        return ModelsResponse(
            models=models,
            total=len(models)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to list models: {str(e)}")


@router.post("/models/pull", response_model=PullResponse)
async def pull_model(
    request: PullRequest,
    background_tasks: BackgroundTasks,
    ollama: OllamaProvider = Depends(get_ollama_provider),
    user=Depends(require_permission("write"))
):
    """모델 다운로드"""
    import uuid
    
    try:
        if not await ollama.is_available():
            raise HTTPException(status_code=503, detail="Ollama service not available")
        
        task_id = str(uuid.uuid4())
        
        # 백그라운드에서 모델 다운로드
        background_tasks.add_task(
            _pull_model_task,
            task_id,
            request.model_name,
            ollama
        )
        
        return PullResponse(
            task_id=task_id,
            status="started",
            message=f"Model {request.model_name} download started"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start model pull: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to start model pull: {str(e)}")


@router.delete("/models/{model_name}")
async def delete_model(
    model_name: str,
    ollama: OllamaProvider = Depends(get_ollama_provider),
    user=Depends(require_permission("write"))
):
    """모델 삭제"""
    try:
        if not await ollama.is_available():
            raise HTTPException(status_code=503, detail="Ollama service not available")
        
        success = await ollama.delete_model(model_name)
        
        if success:
            return {"message": f"Model {model_name} deleted successfully"}
        else:
            raise HTTPException(status_code=400, detail=f"Failed to delete model {model_name}")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete model: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to delete model: {str(e)}")


@router.get("/models/{model_name}")
async def get_model_info(
    model_name: str,
    ollama: OllamaProvider = Depends(get_ollama_provider),
    user=Depends(require_permission("read"))
):
    """모델 상세 정보"""
    try:
        if not await ollama.is_available():
            raise HTTPException(status_code=503, detail="Ollama service not available")
        
        model_info = await ollama.get_model_info(model_name)
        
        if model_info:
            return model_info
        else:
            raise HTTPException(status_code=404, detail=f"Model {model_name} not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get model info: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get model info: {str(e)}")


@router.get("/status")
async def get_ollama_status(
    ollama: OllamaProvider = Depends(get_ollama_provider),
    user=Depends(require_permission("read"))
):
    """Ollama 서비스 상태"""
    try:
        is_available = await ollama.is_available()
        
        if is_available:
            models = await ollama.get_available_models()
            return {
                "status": "available",
                "base_url": ollama.base_url,
                "models_count": len(models),
                "models": models
            }
        else:
            return {
                "status": "unavailable",
                "base_url": ollama.base_url,
                "error": "Cannot connect to Ollama service"
            }
            
    except Exception as e:
        logger.error(f"Failed to get Ollama status: {e}")
        return {
            "status": "error",
            "base_url": ollama.base_url,
            "error": str(e)
        }


@router.get("/recommended")
async def get_recommended_models():
    """추천 모델 목록"""
    return {
        "recommended_models": [
            {
                "name": "codellama:7b",
                "description": "Code Llama 7B - 코드 생성 및 이해에 특화",
                "size": "3.8GB",
                "use_case": "코드 생성, 코드 설명, 디버깅"
            },
            {
                "name": "codellama:13b",
                "description": "Code Llama 13B - 더 큰 모델, 더 나은 성능",
                "size": "7.3GB",
                "use_case": "복잡한 코드 분석, 리팩토링"
            },
            {
                "name": "deepseek-coder:6.7b",
                "description": "DeepSeek Coder - 코딩 전문 모델",
                "size": "3.7GB",
                "use_case": "코드 완성, 코드 리뷰"
            },
            {
                "name": "llama3:8b",
                "description": "Llama 3 8B - 범용 언어 모델",
                "size": "4.7GB",
                "use_case": "일반적인 질문 답변, 문서 작성"
            },
            {
                "name": "mistral:7b",
                "description": "Mistral 7B - 빠르고 효율적인 모델",
                "size": "4.1GB",
                "use_case": "빠른 응답이 필요한 작업"
            }
        ]
    }


async def _pull_model_task(task_id: str, model_name: str, ollama: OllamaProvider):
    """모델 다운로드 작업 (백그라운드)"""
    logger.info(f"Starting model pull task {task_id} for {model_name}")
    
    try:
        success = await ollama.pull_model(model_name)
        
        if success:
            logger.info(f"Model pull task {task_id} completed successfully")
        else:
            logger.error(f"Model pull task {task_id} failed")
            
    except Exception as e:
        logger.error(f"Model pull task {task_id} failed with exception: {e}")
        # TODO: 작업 상태를 실패로 업데이트
