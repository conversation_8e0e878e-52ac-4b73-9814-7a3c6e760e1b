"""
Webhook Router

Backend에서 git 변경사항을 수신하는 webhook API
"""

import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks, Query
from pydantic import BaseModel, Field

from ...core.persistence.codebase_analyzer import CodebaseAnalyzer
from ...core.persistence.git_analyzer import GitAnalyzer
from ...core.persistence.vector_store import VectorStore
from ...utils.config import Config
from ..dependencies import get_config, get_codebase_analyzer

logger = logging.getLogger(__name__)

router = APIRouter()


class GitChangeNotification(BaseModel):
    """Git 변경 알림 스키마"""
    repository_id: str = Field(..., description="저장소 ID")
    repository_name: str = Field(..., description="저장소 이름")
    repository_path: str = Field(..., description="저장소 로컬 경로")
    action: str = Field(..., description="액션 타입 (clone, pull, branch_switch)")
    branch: str = Field(..., description="브랜치명")
    commit_hash: Optional[str] = Field(None, description="최신 커밋 해시")
    changed_files: Optional[List[str]] = Field(None, description="변경된 파일 목록")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="알림 시간")
    metadata: Optional[Dict[str, Any]] = Field(None, description="추가 메타데이터")


class WebhookResponse(BaseModel):
    """Webhook 응답 스키마"""
    success: bool = Field(..., description="처리 성공 여부")
    message: str = Field(..., description="응답 메시지")
    task_id: Optional[str] = Field(None, description="백그라운드 작업 ID")
    processed_files: Optional[int] = Field(None, description="처리된 파일 수")


@router.post("/git-changes", response_model=WebhookResponse)
async def receive_git_changes(
    notification: GitChangeNotification,
    background_tasks: BackgroundTasks,
    config: Config = Depends(get_config)
):
    """
    Backend에서 git 변경사항 수신
    
    Backend에서 git clone, pull, branch switch 등의 작업 후
    변경사항을 aiend에 알리는 webhook
    """
    logger.info(f"Received git change notification: {notification.action} for {notification.repository_name}")
    
    try:
        # 저장소 경로 검증
        repo_path = notification.repository_path
        if not repo_path or not Path(repo_path).exists():
            raise HTTPException(
                status_code=400,
                detail=f"Repository path not found: {repo_path}"
            )
        
        # 백그라운드에서 분석 작업 수행
        task_id = f"git_change_{notification.repository_id}_{int(datetime.utcnow().timestamp())}"
        
        background_tasks.add_task(
            process_git_changes,
            notification,
            config,
            task_id
        )
        
        return WebhookResponse(
            success=True,
            message=f"Git change notification received for {notification.repository_name}",
            task_id=task_id
        )
        
    except Exception as e:
        logger.error(f"Failed to process git change notification: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def parse_repository_branch_id(repository_branch_id: str) -> tuple[str, str]:
    """
    repository_branch_id를 파싱하여 repository_id와 branch를 분리

    '_' 문자를 오른쪽부터 찾아서 마지막 '_' 이후를 branch로, 그 앞을 repository_id로 분리

    Args:
        repository_branch_id: "repository_id_branch" 형태의 문자열

    Returns:
        tuple: (repository_id, branch)

    Examples:
        "123_main" -> ("123", "main")
        "my_repo_123_feature_branch" -> ("my_repo_123", "feature_branch")
        "repo_with_underscores_456_dev_test" -> ("repo_with_underscores_456", "dev_test")
    """
    if '_' not in repository_branch_id:
        raise ValueError(f"Invalid repository_branch_id format: {repository_branch_id}")

    # 오른쪽부터 첫 번째 '_' 찾기
    last_underscore_index = repository_branch_id.rfind('_')

    repository_id = repository_branch_id[:last_underscore_index]
    branch = repository_branch_id[last_underscore_index + 1:]

    if not repository_id or not branch:
        raise ValueError(f"Invalid repository_branch_id format: {repository_branch_id}")

    return repository_id, branch


@router.post("/repository-branch-scan", response_model=WebhookResponse)
async def trigger_repository_branch_scan(
    repository_branch_id: str = Query(..., description="Repository Branch ID (format: repository_id_branch)"),
    force_reindex: bool = False,
    config: Config = Depends(get_config)
):
    """
    repository_branch_id 형태로 특정 리포지토리의 특정 브랜치만 스캔

    Frontend에서 repository_branch_id 형태로 요청하면
    해당 리포지토리의 해당 브랜치만 스캔하여 벡터 DB에 임베딩
    """
    logger.info(f"Repository branch scan triggered for: {repository_branch_id}")

    try:
        # repository_branch_id 파싱
        repository_id, branch = parse_repository_branch_id(repository_branch_id)
        logger.info(f"Parsed repository_id: {repository_id}, branch: {branch}")

        # repos 폴더 경로 구성 (repository_id 폴더명 사용)
        repos_base_path = Path("/app/repos")  # Docker 환경 기준
        repository_path = repos_base_path / repository_id

        if not repository_path.exists():
            raise HTTPException(
                status_code=400,
                detail=f"Repository path not found: {repository_path}"
            )

        # 브랜치 체크아웃 수행
        checkout_success = await checkout_branch(str(repository_path), branch)
        if not checkout_success:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to checkout branch: {branch}"
            )

        # 전체 분석 수행 (동기적으로 실행)
        task_id = f"branch_scan_{repository_branch_id}_{int(datetime.utcnow().timestamp())}"

        # 직접 함수 호출
        await perform_full_repository_scan(
            str(repository_path),
            f"repo_{repository_id}",  # repository_name
            repository_id,
            branch,
            force_reindex,
            config,
            task_id
        )

        return WebhookResponse(
            success=True,
            message=f"Repository branch scan started for {repository_branch_id}",
            task_id=task_id
        )

    except ValueError as e:
        logger.error(f"Invalid repository_branch_id format: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Failed to trigger repository branch scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/repository-scan", response_model=WebhookResponse)
async def trigger_repository_scan(
    repository_path: str,
    repository_name: str,
    repository_id: str = Query(..., description="Repository ID"),
    branch: str = Query("main", description="Branch name"),
    force_reindex: bool = False,
    config: Config = Depends(get_config)
):
    """
    저장소 전체 스캔 트리거

    Backend에서 새로운 저장소가 클론되었거나
    전체 재분석이 필요한 경우 호출
    """
    logger.info(f"Repository scan triggered for: {repository_name} (branch: {branch})")

    try:
        from pathlib import Path

        # 저장소 경로 검증
        repo_path = Path(repository_path)
        if not repo_path.exists():
            raise HTTPException(
                status_code=400,
                detail=f"Repository path not found: {repository_path}"
            )

        # 브랜치 체크아웃 수행
        checkout_success = await checkout_branch(repository_path, branch)
        if not checkout_success:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to checkout branch: {branch}"
            )

        # 전체 분석 수행 (동기적으로 실행)
        task_id = f"full_scan_{repository_id}_{branch}_{int(datetime.utcnow().timestamp())}"

        # 직접 함수 호출
        await perform_full_repository_scan(
            repository_path,
            repository_name,
            repository_id,
            branch,
            force_reindex,
            config,
            task_id
        )
        
        return WebhookResponse(
            success=True,
            message=f"Repository scan started for {repository_name}",
            task_id=task_id
        )
        
    except Exception as e:
        logger.error(f"Failed to trigger repository scan: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/repository", response_model=WebhookResponse)
async def delete_repository(
    repository_id: str = Query(..., description="Repository ID"),
    repository_name: str = Query(..., description="Repository name"),
    branch: str = Query(None, description="Branch name (optional, if not provided, deletes all branches)"),
    config: Config = Depends(get_config)
):
    """저장소 삭제 - 벡터 DB 컬렉션과 데이터베이스 데이터 정리"""
    logger.info(f"Repository deletion triggered: {repository_name} (ID: {repository_id}, branch: {branch})")

    try:
        # 컬렉션 이름 생성
        if branch:
            # 특정 브랜치만 삭제
            safe_branch = branch.replace('/', '_').replace('-', '_').replace('.', '_')
            collection_name = f"repo_{repository_id}_branch_{safe_branch}"
        else:
            # 전체 리포지토리 삭제 (모든 브랜치)
            collection_name = f"repo_{repository_id}"

        # VectorStore 초기화
        vector_store = VectorStore(
            host=config.vector_store.host,
            port=config.vector_store.port,
            collection_name=collection_name,
            model_name=config.vector_store.model,
            vector_size=config.vector_store.vector_size,
            ollama_url="http://**********:11434"
        )

        # 1. Qdrant 컬렉션 삭제
        if vector_store.client:
            try:
                if branch:
                    # 특정 브랜치 컬렉션만 삭제
                    vector_store.client.delete_collection(collection_name)
                    logger.info(f"Deleted Qdrant collection: {collection_name}")
                else:
                    # 전체 리포지토리의 모든 브랜치 컬렉션 삭제
                    collections = vector_store.client.get_collections()
                    repo_prefix = f"repo_{repository_id}_branch_"
                    deleted_count = 0

                    for collection in collections.collections:
                        if collection.name.startswith(repo_prefix):
                            try:
                                vector_store.client.delete_collection(collection.name)
                                logger.info(f"Deleted Qdrant collection: {collection.name}")
                                deleted_count += 1
                            except Exception as e:
                                logger.warning(f"Failed to delete Qdrant collection {collection.name}: {e}")

                    logger.info(f"Deleted {deleted_count} collections for repository {repository_id}")
            except Exception as e:
                logger.warning(f"Failed to delete Qdrant collections: {e}")

        # 2. SQLite 데이터베이스에서 관련 데이터 삭제
        try:
            from ...core.persistence.sqlite_manager import SQLiteManager
            db_manager = SQLiteManager(config.database.path)

            # Repository 관련 데이터 삭제
            with db_manager.get_session() as session:
                if branch:
                    # 특정 브랜치만 삭제
                    # 파일 데이터 삭제
                    session.execute(
                        "DELETE FROM code_files WHERE repository_id = ? AND branch = ?",
                        (repository_id, branch)
                    )

                    # 커밋 데이터 삭제
                    session.execute(
                        "DELETE FROM git_commits WHERE repository_id = ? AND branch = ?",
                        (repository_id, branch)
                    )
                else:
                    # 전체 리포지토리 삭제
                    # 파일 데이터 삭제
                    session.execute(
                        "DELETE FROM code_files WHERE repository_id = ?",
                        (repository_id,)
                    )

                    # 커밋 데이터 삭제
                    session.execute(
                        "DELETE FROM git_commits WHERE repository_id = ?",
                        (repository_id,)
                    )

                session.commit()
                logger.info(f"Deleted database records for repository: {repository_id}")

        except Exception as e:
            logger.warning(f"Failed to delete database records for repository {repository_id}: {e}")

        return WebhookResponse(
            success=True,
            message=f"Repository {repository_name} deleted successfully from Aiend",
            task_id=f"delete_{repository_id}_{int(datetime.utcnow().timestamp())}"
        )

    except Exception as e:
        logger.error(f"Failed to delete repository: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def cleanup_branch_data(db_manager, repository_id: str, branch: str):
    """특정 브랜치의 SQLite 데이터 정리"""
    try:
        with db_manager.get_session() as session:
            # 해당 브랜치의 파일 데이터 삭제
            from ...core.persistence.models import CodeFile, CodeFunction, CodeClass

            # 파일 데이터 삭제
            files_deleted = session.query(CodeFile).filter(
                CodeFile.repository_id == repository_id,
                CodeFile.branch == branch
            ).delete()

            # 함수 데이터는 파일과 연결되어 있으므로 CASCADE로 자동 삭제됨
            # 하지만 명시적으로 삭제할 수도 있음
            functions_deleted = session.query(CodeFunction).filter(
                CodeFunction.file_id.in_(
                    session.query(CodeFile.id).filter(
                        CodeFile.repository_id == repository_id,
                        CodeFile.branch == branch
                    )
                )
            ).delete(synchronize_session=False)

            # 클래스 데이터도 마찬가지
            classes_deleted = session.query(CodeClass).filter(
                CodeClass.file_id.in_(
                    session.query(CodeFile.id).filter(
                        CodeFile.repository_id == repository_id,
                        CodeFile.branch == branch
                    )
                )
            ).delete(synchronize_session=False)

            session.commit()
            logger.info(f"Cleaned up branch data - Files: {files_deleted}, Functions: {functions_deleted}, Classes: {classes_deleted}")

    except Exception as e:
        logger.error(f"Failed to cleanup branch data: {e}")


@router.get("/status/{task_id}")
async def get_task_status(task_id: str):
    """
    백그라운드 작업 상태 조회
    
    실제 구현에서는 Redis나 데이터베이스를 사용하여
    작업 상태를 추적해야 함
    """
    # 임시 구현 - 실제로는 작업 상태 저장소에서 조회
    return {
        "task_id": task_id,
        "status": "completed",  # pending, running, completed, failed
        "progress": 100,
        "message": "Task completed successfully"
    }


async def process_git_changes(
    notification: GitChangeNotification,
    config: Config,
    task_id: str
):
    """Git 변경사항 처리 (백그라운드 작업)"""
    logger.info(f"Processing git changes for task: {task_id}")
    
    try:
        from pathlib import Path
        from ...core.persistence.sqlite_manager import SQLiteManager
        from ...core.persistence.vector_store import VectorStore
        from ...core.persistence.git_analyzer import GitAnalyzer
        from ...core.persistence.codebase_analyzer import CodebaseAnalyzer
        
        # 컴포넌트 초기화
        db_manager = SQLiteManager(config.database.path)
        vector_store = VectorStore(
            host=config.vector_store.host if hasattr(config.vector_store, 'host') else "qdrant",
            port=config.vector_store.port if hasattr(config.vector_store, 'port') else 6333,
            collection_name=f"repo_{notification.repository_id}",
            model_name=config.vector_store.model if hasattr(config.vector_store, 'model') else "dengcao/Qwen3-Embedding-8B:Q5_K_M",
            vector_size=config.vector_store.vector_size if hasattr(config.vector_store, 'vector_size') else 4096,
            ollama_url="http://**********:11434"
        )
        git_analyzer = GitAnalyzer(notification.repository_path)
        
        analyzer = CodebaseAnalyzer(
            repo_path=notification.repository_path,
            db_manager=db_manager,
            vector_store=vector_store,
            git_analyzer=git_analyzer
        )
        
        processed_files = 0
        
        if notification.action == "clone":
            # 새로운 저장소 클론 - 전체 분석
            logger.info(f"Performing full analysis for cloned repository: {notification.repository_name}")
            stats = analyzer.analyze_repository(force_reindex=True)
            processed_files = stats.total_files
            
        elif notification.action in ["pull", "branch_switch"]:
            # 변경된 파일들만 재분석
            if notification.changed_files:
                logger.info(f"Analyzing {len(notification.changed_files)} changed files")
                success = analyzer.analyze_file_changes(notification.changed_files)
                processed_files = len(notification.changed_files) if success else 0
            else:
                # 변경된 파일 목록이 없는 경우 Git에서 추출
                if git_analyzer.is_available() and notification.commit_hash:
                    # 이전 커밋과 현재 커밋 사이의 변경사항 추출
                    try:
                        # 간단한 구현 - 최근 커밋의 변경사항만 분석
                        recent_commits = git_analyzer.get_recent_commits(max_count=1)
                        if recent_commits:
                            changed_files = recent_commits[0].changed_files
                            if changed_files:
                                success = analyzer.analyze_file_changes(changed_files)
                                processed_files = len(changed_files) if success else 0
                    except Exception as e:
                        logger.error(f"Failed to extract changed files from git: {e}")
        
        logger.info(f"Git change processing completed for task {task_id}: {processed_files} files processed")
        
        # 실제 구현에서는 작업 상태를 저장소에 업데이트
        # update_task_status(task_id, "completed", processed_files)
        
    except Exception as e:
        logger.error(f"Failed to process git changes for task {task_id}: {e}")
        # update_task_status(task_id, "failed", 0, str(e))


async def perform_full_repository_scan(
    repository_path: str,
    repository_name: str,
    repository_id: str,
    branch: str,
    force_reindex: bool,
    config: Config,
    task_id: str
):
    """저장소 전체 스캔 수행 (백그라운드 작업)"""
    logger.info(f"Performing full repository scan for task: {task_id} (repo: {repository_id}, branch: {branch})")

    try:
        from ...core.persistence.sqlite_manager import SQLiteManager
        from ...core.persistence.vector_store import VectorStore
        from ...core.persistence.git_analyzer import GitAnalyzer
        from ...core.persistence.codebase_analyzer import CodebaseAnalyzer

        # 브랜치별 컬렉션 이름 생성
        safe_branch = branch.replace('/', '_').replace('-', '_').replace('.', '_')
        collection_name = f"repo_{repository_id}_branch_{safe_branch}"

        # 컴포넌트 초기화
        db_manager = SQLiteManager(config.database.path)

        # 기존 브랜치 데이터 정리 (SQLite)
        logger.info(f"Cleaning existing data for repository {repository_id}, branch {branch}")
        await cleanup_branch_data(db_manager, repository_id, branch)

        vector_store = VectorStore(
            host=config.vector_store.host if hasattr(config.vector_store, 'host') else "qdrant",
            port=config.vector_store.port if hasattr(config.vector_store, 'port') else 6333,
            collection_name=collection_name,
            model_name=config.vector_store.model if hasattr(config.vector_store, 'model') else "dengcao/Qwen3-Embedding-8B:Q5_K_M",
            vector_size=config.vector_store.vector_size if hasattr(config.vector_store, 'vector_size') else 4096,
            ollama_url="http://**********:11434"
        )

        # 기존 브랜치 벡터 데이터 정리 (Qdrant 컬렉션 재생성)
        if vector_store.client and force_reindex:
            try:
                vector_store.client.delete_collection(collection_name)
                logger.info(f"Deleted existing Qdrant collection: {collection_name}")
            except Exception as e:
                logger.info(f"Collection {collection_name} did not exist or failed to delete: {e}")

            # 컬렉션 재생성
            vector_store._ensure_collection_exists()
            logger.info(f"Recreated Qdrant collection: {collection_name}")
        git_analyzer = GitAnalyzer(repository_path)
        
        analyzer = CodebaseAnalyzer(
            repo_path=repository_path,
            db_manager=db_manager,
            vector_store=vector_store,
            git_analyzer=git_analyzer,
            repository_id=repository_id,
            branch=branch
        )
        
        # 전체 분석 수행
        stats = analyzer.analyze_repository(force_reindex=force_reindex)
        
        logger.info(f"Full repository scan completed for task {task_id}: {stats.total_files} files analyzed")
        
        # 실제 구현에서는 작업 상태를 저장소에 업데이트
        # update_task_status(task_id, "completed", stats.total_files)
        
    except Exception as e:
        logger.error(f"Failed to perform full repository scan for task {task_id}: {e}")
        # update_task_status(task_id, "failed", 0, str(e))
