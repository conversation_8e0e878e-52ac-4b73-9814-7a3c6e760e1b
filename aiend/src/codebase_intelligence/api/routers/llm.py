"""
LLM API Router

LLM 관련 API
"""

import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, Field

from ...core.llm.orchestrator import LLMOrchestrator
from ...utils.config import Config
from ..dependencies import get_llm_orchestrator, require_permission, get_config

logger = logging.getLogger(__name__)

router = APIRouter()


class ChatRequest(BaseModel):
    """채팅 요청"""
    message: str = Field(..., description="사용자 메시지", min_length=1, max_length=5000)
    max_tokens: int = Field(8000, description="최대 토큰 수", ge=100, le=32000)
    provider: Optional[str] = Field(None, description="LLM 제공자 (openai, anthropic)")
    model: Optional[str] = Field(None, description="사용할 모델")
    strategy: Optional[str] = Field(None, description="응답 전략")
    context_level: str = Field("standard", description="컨텍스트 레벨")
    include_history: bool = Field(True, description="히스토리 포함 여부")
    save_context: bool = Field(False, description="컨텍스트 저장 여부")


class ChatResponse(BaseModel):
    """채팅 응답"""
    content: str
    confidence: float
    strategy_used: str
    execution_time: float
    context_tokens: int
    providers_used: List[str]
    suggestions: List[str]
    context_id: Optional[str] = None


class ProviderInfo(BaseModel):
    """제공자 정보"""
    name: str
    available: bool
    models: List[str]
    default_model: str
    usage_stats: Dict[str, Any]


class ProvidersResponse(BaseModel):
    """제공자 목록 응답"""
    providers: List[ProviderInfo]
    default_provider: str


@router.post("/chat", response_model=ChatResponse)
async def chat_with_llm(
    request: ChatRequest,
    orchestrator: LLMOrchestrator = Depends(get_llm_orchestrator),
    user=Depends(require_permission("read"))
):
    """LLM과 채팅"""
    logger.info(f"Chat request: '{request.message[:100]}...'")
    
    try:
        # LLM 오케스트레이터를 통해 응답 생성
        response = await orchestrator.process_query(
            query=request.message,
            max_tokens=request.max_tokens,
            strategy=request.strategy,
            context_level=request.context_level,
            include_history=request.include_history
        )
        
        # 컨텍스트 저장 (요청시)
        context_id = None
        if request.save_context:
            # TODO: 컨텍스트 저장 구현
            context_id = "ctx_" + str(hash(request.message))[:8]
        
        return ChatResponse(
            content=response.content,
            confidence=response.confidence,
            strategy_used=response.strategy_used,
            execution_time=response.execution_time,
            context_tokens=response.context_used.total_tokens,
            providers_used=[resp.provider_name for resp in response.llm_responses],
            suggestions=response.context_used.suggestions,
            context_id=context_id
        )
        
    except Exception as e:
        logger.error(f"Chat failed: {e}")
        raise HTTPException(status_code=500, detail=f"Chat failed: {str(e)}")


@router.get("/providers", response_model=ProvidersResponse)
async def get_providers(
    orchestrator: LLMOrchestrator = Depends(get_llm_orchestrator),
    config: Config = Depends(get_config),
    user=Depends(require_permission("read"))
):
    """LLM 제공자 목록"""
    try:
        provider_stats = orchestrator.get_provider_stats()
        
        providers = []
        for name, stats in provider_stats.items():
            provider_config = config.llm.providers.get(name)
            default_model = ""
            if provider_config:
                default_model = provider_config.default_model or (stats["models"][0] if stats["models"] else "")

            provider_info = ProviderInfo(
                name=name,
                available=stats["available"],
                models=stats["models"],
                default_model=default_model,
                usage_stats=stats.get("usage", {})
            )
            providers.append(provider_info)
        
        return ProvidersResponse(
            providers=providers,
            default_provider=config.llm.default_provider
        )
        
    except Exception as e:
        logger.error(f"Failed to get providers: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get providers: {str(e)}")


@router.get("/models")
async def get_models(
    provider: Optional[str] = None,
    orchestrator: LLMOrchestrator = Depends(get_llm_orchestrator),
    user=Depends(require_permission("read"))
):
    """사용 가능한 모델 목록"""
    try:
        provider_stats = orchestrator.get_provider_stats()
        
        if provider:
            if provider not in provider_stats:
                raise HTTPException(status_code=404, detail=f"Provider '{provider}' not found")
            
            return {
                "provider": provider,
                "models": provider_stats[provider]["models"]
            }
        else:
            models_by_provider = {}
            for name, stats in provider_stats.items():
                models_by_provider[name] = stats["models"]
            
            return {
                "models_by_provider": models_by_provider
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get models: {str(e)}")


@router.get("/strategies")
async def get_strategies():
    """사용 가능한 응답 전략"""
    return {
        "strategies": [
            {
                "name": "single_best",
                "description": "최적 LLM 하나만 사용"
            },
            {
                "name": "multi_vote",
                "description": "여러 LLM 결과 투표"
            },
            {
                "name": "hybrid",
                "description": "하이브리드 접근"
            },
            {
                "name": "consensus",
                "description": "합의 기반"
            }
        ]
    }


@router.get("/context/{context_id}")
async def get_saved_context(
    context_id: str,
    user=Depends(require_permission("read"))
):
    """저장된 컨텍스트 조회"""
    # TODO: 실제 컨텍스트 저장/조회 구현
    return {
        "context_id": context_id,
        "content": "Saved context content...",
        "created_at": "2024-01-01T00:00:00Z",
        "tokens": 1500
    }


@router.delete("/context/{context_id}")
async def delete_saved_context(
    context_id: str,
    user=Depends(require_permission("write"))
):
    """저장된 컨텍스트 삭제"""
    # TODO: 실제 컨텍스트 삭제 구현
    return {
        "message": f"Context {context_id} deleted"
    }


@router.get("/usage")
async def get_usage_stats(
    orchestrator: LLMOrchestrator = Depends(get_llm_orchestrator),
    user=Depends(require_permission("read"))
):
    """사용량 통계"""
    try:
        provider_stats = orchestrator.get_provider_stats()
        
        total_requests = 0
        total_tokens = 0
        total_errors = 0
        
        for stats in provider_stats.values():
            usage = stats.get("usage", {})
            total_requests += usage.get("total_requests", 0)
            total_tokens += usage.get("total_tokens", 0)
            total_errors += usage.get("errors", 0)
        
        return {
            "total_requests": total_requests,
            "total_tokens": total_tokens,
            "total_errors": total_errors,
            "providers": {
                name: stats.get("usage", {})
                for name, stats in provider_stats.items()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get usage stats: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get usage stats: {str(e)}")
