"""
Health Check Router

시스템 상태 확인 API
"""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends
from pydantic import BaseModel

from ...utils.config import Config
from ..dependencies import get_config

logger = logging.getLogger(__name__)

router = APIRouter()


class HealthResponse(BaseModel):
    """헬스 체크 응답"""
    status: str
    timestamp: datetime
    version: str
    components: Dict[str, Any]


@router.get("/", response_model=HealthResponse)
async def health_check(config: Config = Depends(get_config)):
    """기본 헬스 체크"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now(),
        version="0.1.0",
        components={
            "api": "healthy",
            "database": "healthy",  # TODO: 실제 DB 상태 확인
            "vector_store": "healthy",  # TODO: 실제 벡터 스토어 상태 확인
        }
    )


@router.get("/detailed")
async def detailed_health_check(config: Config = Depends(get_config)):
    """상세 헬스 체크"""
    components = {}
    
    # 데이터베이스 상태 확인
    try:
        # TODO: 실제 DB 연결 테스트
        components["database"] = {
            "status": "healthy",
            "path": config.database.path,
            "last_check": datetime.now().isoformat()
        }
    except Exception as e:
        components["database"] = {
            "status": "unhealthy",
            "error": str(e),
            "last_check": datetime.now().isoformat()
        }
    
    # 벡터 스토어 상태 확인
    try:
        # TODO: 실제 벡터 스토어 연결 테스트
        components["vector_store"] = {
            "status": "healthy",
            "path": config.vector_store.path,
            "model": config.vector_store.model,
            "last_check": datetime.now().isoformat()
        }
    except Exception as e:
        components["vector_store"] = {
            "status": "unhealthy",
            "error": str(e),
            "last_check": datetime.now().isoformat()
        }
    
    # LLM 제공자 상태 확인
    llm_providers = {}
    for name, provider_config in config.llm.providers.items():
        try:
            # TODO: 실제 LLM 제공자 연결 테스트
            llm_providers[name] = {
                "status": "healthy" if provider_config.enabled else "disabled",
                "models": provider_config.models,
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            llm_providers[name] = {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.now().isoformat()
            }
    
    components["llm_providers"] = llm_providers
    
    # 전체 상태 결정
    overall_status = "healthy"
    for component_name, component_info in components.items():
        if isinstance(component_info, dict) and component_info.get("status") == "unhealthy":
            overall_status = "unhealthy"
            break
        elif component_name == "llm_providers":
            # LLM 제공자는 하나라도 healthy면 OK
            provider_statuses = [p.get("status") for p in component_info.values()]
            if not any(status == "healthy" for status in provider_statuses):
                overall_status = "degraded"
    
    return {
        "status": overall_status,
        "timestamp": datetime.now(),
        "version": "0.1.0",
        "components": components,
        "config": {
            "repository_path": config.repository.path,
            "database_path": config.database.path,
            "vector_store_path": config.vector_store.path,
            "default_llm_provider": config.llm.default_provider
        }
    }


@router.get("/ready")
async def readiness_check():
    """준비 상태 확인 (Kubernetes readiness probe용)"""
    # TODO: 실제 준비 상태 확인 로직
    return {"status": "ready"}


@router.get("/live")
async def liveness_check():
    """생존 상태 확인 (Kubernetes liveness probe용)"""
    return {"status": "alive", "timestamp": datetime.now()}
