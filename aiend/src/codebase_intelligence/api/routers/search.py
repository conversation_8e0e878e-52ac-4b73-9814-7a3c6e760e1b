"""
Search API Router

코드베이스 검색 API
"""

import logging
from typing import List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from ...core.intelligence.smart_search import SmartSearch, SearchQuery
from ...core.persistence.vector_store import VectorStore
from ...utils.config import Config
from ..dependencies import get_search_system, get_vector_store, get_config, require_permission

logger = logging.getLogger(__name__)

router = APIRouter()


class SearchRequest(BaseModel):
    """검색 요청"""
    query: str = Field(..., description="검색 쿼리", min_length=1, max_length=1000)
    mode: str = Field("auto", description="검색 모드 (auto, semantic, structural, temporal, hybrid)")
    context_types: Optional[List[str]] = Field(None, description="컨텍스트 타입 필터")
    file_patterns: Optional[List[str]] = Field(None, description="파일 패턴 필터")
    max_results: int = Field(10, description="최대 결과 수", ge=1, le=100)
    include_history: bool = Field(True, description="히스토리 포함 여부")


class SearchResultResponse(BaseModel):
    """검색 결과 응답"""
    file_path: str
    content: str
    score: float
    start_line: int
    end_line: int
    context_type: str
    explanation: str
    metadata: dict


class SearchResponse(BaseModel):
    """검색 응답"""
    results: List[SearchResultResponse]
    total_count: int
    query: str
    mode: str
    execution_time: float
    suggestions: List[str]


class EncodeRequest(BaseModel):
    """임베딩 요청"""
    text: str = Field(..., description="임베딩할 텍스트")
    repository_id: Optional[str] = Field(None, description="리포지토리 ID")


class EncodeResponse(BaseModel):
    """임베딩 결과"""
    vector: List[float]
    model: str


class QueryRequest(BaseModel):
    """자연어 쿼리 요청"""
    query: str = Field(..., description="자연어 질문")
    repository_id: Optional[int] = Field(None, description="리포지토리 ID")
    max_results: int = Field(5, description="컨텍스트로 사용할 최대 검색 결과 수", ge=1, le=20)


class QueryResponse(BaseModel):
    """자연어 쿼리 응답"""
    answer: str
    search_results: List[SearchResultResponse]
    execution_time: float


@router.post("/", response_model=SearchResponse)
async def search_codebase(
    request: SearchRequest,
    search_system: SmartSearch = Depends(get_search_system),
    user=Depends(require_permission("read"))
):
    """코드베이스 검색"""
    import time
    start_time = time.time()
    
    logger.info(f"Search request: '{request.query}' (mode: {request.mode})")
    
    try:
        # 검색 쿼리 구성
        search_query = SearchQuery(
            text=request.query,
            mode=request.mode,
            context_types=request.context_types,
            file_patterns=request.file_patterns,
            max_results=request.max_results,
            include_history=request.include_history
        )
        
        # 검색 실행
        enhanced_results = search_system.search(search_query)
        
        # 응답 변환
        results = []
        for enhanced_result in enhanced_results:
            result = SearchResultResponse(
                file_path=enhanced_result.search_result.file_path,
                content=enhanced_result.search_result.content,
                score=enhanced_result.combined_score,
                start_line=enhanced_result.search_result.start_line,
                end_line=enhanced_result.search_result.end_line,
                context_type=enhanced_result.search_result.context_type,
                explanation=enhanced_result.explanation,
                metadata=enhanced_result.search_result.metadata
            )
            results.append(result)
        
        # 관련 쿼리 제안
        suggestions = search_system.suggest_related_queries(request.query)
        
        execution_time = time.time() - start_time
        
        return SearchResponse(
            results=results,
            total_count=len(results),
            query=request.query,
            mode=request.mode,
            execution_time=execution_time,
            suggestions=suggestions
        )
        
    except Exception as e:
        logger.error(f"Search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/suggestions")
async def get_search_suggestions(
    query: str = Query(..., description="부분 쿼리"),
    limit: int = Query(10, description="제안 수", ge=1, le=20),
    search_system: SmartSearch = Depends(get_search_system),
    user=Depends(require_permission("read"))
):
    """검색 제안"""
    try:
        suggestions = search_system.suggest_related_queries(query)
        return {
            "query": query,
            "suggestions": suggestions[:limit]
        }
    except Exception as e:
        logger.error(f"Failed to get suggestions: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")


@router.get("/modes")
async def get_search_modes():
    """사용 가능한 검색 모드"""
    return {
        "modes": [
            {
                "name": "auto",
                "description": "쿼리를 분석하여 최적 모드 자동 선택"
            },
            {
                "name": "semantic",
                "description": "의미적 검색 - 코드의 의미와 기능 기반"
            },
            {
                "name": "structural", 
                "description": "구조적 검색 - 코드 구조와 관계 기반"
            },
            {
                "name": "temporal",
                "description": "시간적 검색 - Git 히스토리 기반"
            },
            {
                "name": "hybrid",
                "description": "하이브리드 검색 - 모든 방법 조합"
            }
        ]
    }


@router.get("/context-types")
async def get_context_types():
    """사용 가능한 컨텍스트 타입"""
    return {
        "context_types": [
            {
                "name": "function",
                "description": "함수 및 메서드"
            },
            {
                "name": "class",
                "description": "클래스 및 객체"
            },
            {
                "name": "file",
                "description": "파일 전체"
            },
            {
                "name": "documentation",
                "description": "문서 및 주석"
            }
        ]
    }


@router.post("/vector", response_model=SearchResponse)
async def vector_search(
    request: SearchRequest,
    repository_id: str = None,
    config: Config = Depends(get_config),
    user=Depends(require_permission("read"))
):
    """벡터 기반 의미적 검색"""
    import time
    start_time = time.time()

    logger.info(f"Vector search request: '{request.query}' for repository: {repository_id}")

    try:
        # Repository별 VectorStore 생성
        collection_name = f"repo_{repository_id}" if repository_id else config.vector_store.collection_name
        vector_store = VectorStore(
            host=config.vector_store.host,
            port=config.vector_store.port,
            collection_name=collection_name,
            model_name=config.vector_store.model,
            vector_size=config.vector_store.vector_size,
            ollama_url="http://**********:11434"
        )

        if not vector_store.is_available():
            raise HTTPException(
                status_code=503,
                detail="Vector store not available"
            )

        # 필터 조건 구성
        filter_conditions = {}
        if request.context_types:
            # 첫 번째 컨텍스트 타입만 사용 (Qdrant 필터 제한)
            filter_conditions["chunk_type"] = request.context_types[0]

        # 벡터 검색 수행
        vector_results = vector_store.search_similar(
            query=request.query,
            limit=request.max_results,
            score_threshold=0.7,
            filter_conditions=filter_conditions
        )

        # 결과 변환
        results = []
        for vector_result in vector_results:
            payload = vector_result.payload
            result = SearchResultResponse(
                file_path=payload.get("file_path", ""),
                content=payload.get("content", ""),
                score=vector_result.score,
                start_line=payload.get("start_line", 0),
                end_line=payload.get("end_line", 0),
                context_type=payload.get("chunk_type", "unknown"),
                explanation=f"Vector similarity: {vector_result.score:.3f}",
                metadata={
                    "vector_id": vector_result.id,
                    "language": payload.get("language", ""),
                    "function_name": payload.get("function_name", ""),
                    "class_name": payload.get("class_name", "")
                }
            )
            results.append(result)

        execution_time = time.time() - start_time

        return SearchResponse(
            results=results,
            total_count=len(results),
            query=request.query,
            mode="vector",
            execution_time=execution_time,
            suggestions=[]
        )

    except Exception as e:
        logger.error(f"Vector search failed: {e}")
        raise HTTPException(status_code=500, detail=f"Vector search failed: {str(e)}")


@router.post("/encode", response_model=EncodeResponse)
async def encode_text(
    request: EncodeRequest,
    config: Config = Depends(get_config),
    user=Depends(require_permission("read"))
):
    """텍스트를 벡터 임베딩으로 변환"""
    try:
        collection_name = f"repo_{request.repository_id}" if request.repository_id else config.vector_store.collection_name
        
        ollama_provider_config = config.llm.providers.get('ollama')
        ollama_base_url = ollama_provider_config.base_url if ollama_provider_config else "http://host.docker.internal:11434"

        vector_store = VectorStore(
            host=config.vector_store.host,
            port=config.vector_store.port,
            collection_name=collection_name,
            model_name=config.vector_store.model,
            vector_size=config.vector_store.vector_size,
            ollama_url=ollama_base_url
        )

        vector = vector_store.create_embedding(request.text)
        return EncodeResponse(vector=vector, model=vector_store.model_name)

    except Exception as e:
        logger.error(f"Failed to encode text: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to encode text: {str(e)}")


@router.post("/query", response_model=QueryResponse)
async def query_codebase(
    request: QueryRequest,
    search_system: SmartSearch = Depends(get_search_system),
    user=Depends(require_permission("read"))
):
    """벡터 DB를 사용한 자연어 검색 (단순 검색, LLM 없음)"""
    import time
    start_time = time.time()

    try:
        # 벡터 DB에서 관련 코드 검색
        search_query = SearchQuery(
            text=request.query,
            mode='semantic',  # 벡터 검색만 사용
            max_results=request.max_results,
            repository_id=str(request.repository_id) if request.repository_id is not None else None
        )
        enhanced_results = search_system.search(search_query)

        # 검색 결과를 응답 형식으로 변환
        search_results_response = [
            SearchResultResponse(
                file_path=er.search_result.file_path,
                content=er.search_result.content,
                score=er.combined_score,
                start_line=er.search_result.start_line,
                end_line=er.search_result.end_line,
                context_type=er.search_result.context_type,
                explanation=er.explanation,
                metadata=er.search_result.metadata
            ) for er in enhanced_results
        ]

        execution_time = time.time() - start_time

        # 단순한 요약 답변 (LLM 없이)
        if search_results_response:
            answer = f"Found {len(search_results_response)} relevant code sections related to '{request.query}'"
        else:
            answer = f"No relevant code sections found for '{request.query}'"

        return QueryResponse(
            answer=answer,
            search_results=search_results_response,
            execution_time=execution_time
        )

    except Exception as e:
        logger.error(f"Query failed: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Query failed: {str(e)}")


@router.get("/vector/status")
async def get_vector_store_status(
    repository_id: str = None,
    config: Config = Depends(get_config),
    user=Depends(require_permission("read"))
):
    """벡터 스토어 상태 조회"""
    try:
        # Repository별 VectorStore 생성
        collection_name = f"repo_{repository_id}" if repository_id else config.vector_store.collection_name
        vector_store = VectorStore(
            host=config.vector_store.host,
            port=config.vector_store.port,
            collection_name=collection_name,
            model_name=config.vector_store.model,
            vector_size=config.vector_store.vector_size,
            ollama_url="http://**********:11434"
        )

        if not vector_store.is_available():
            return {
                "available": False,
                "message": "Vector store not available"
            }

        collection_info = vector_store.get_collection_info()

        return {
            "available": True,
            "collection_info": collection_info,
            "host": vector_store.host,
            "port": vector_store.port,
            "collection_name": vector_store.collection_name,
            "model_name": vector_store.model_name
        }

    except Exception as e:
        logger.error(f"Failed to get vector store status: {e}")
        return {
            "available": False,
            "error": str(e)
        }
