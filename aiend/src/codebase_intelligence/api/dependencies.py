"""
FastAPI Dependencies

의존성 주입을 위한 함수들
"""

import logging
from typing import Optional

from fastapi import Depends, HTTPException, Request

from ..utils.config import Config
from ..core.intelligence.smart_search import SmartSearch
from ..core.llm.orchestrator import LLMOrchestrator
from ..core.persistence.sqlite_manager import SQLiteManager
from ..core.persistence.vector_store import VectorStore
from ..core.persistence.git_analyzer import GitAnalyzer
from ..core.persistence.codebase_analyzer import CodebaseAnalyzer

logger = logging.getLogger(__name__)

# 전역 인스턴스들 (실제로는 더 나은 의존성 관리 필요)
_config: Optional[Config] = None
_search_system: Optional[SmartSearch] = None
_llm_orchestrator: Optional[LLMOrchestrator] = None
_db_manager: Optional[SQLiteManager] = None
_vector_store: Optional[VectorStore] = None
_git_analyzer: Optional[GitAnalyzer] = None
_codebase_analyzer: Optional[CodebaseAnalyzer] = None


def get_config() -> Config:
    """설정 의존성"""
    global _config
    if _config is None:
        _config = Config.load()
    return _config


def get_search_system() -> SmartSearch:
    """검색 시스템 의존성"""
    if _search_system is None:
        raise HTTPException(
            status_code=503,
            detail="Search system is not available. Check server startup logs."
        )
    return _search_system


def get_llm_orchestrator() -> LLMOrchestrator:
    """LLM 오케스트레이터 의존성"""
    if _llm_orchestrator is None:
        raise HTTPException(
            status_code=503,
            detail="LLM orchestrator is not available. Check server startup logs."
        )
    return _llm_orchestrator


def get_current_user(request: Request):
    """현재 사용자 정보 (향후 인증 구현용)"""
    # TODO: 실제 인증 구현
    return {"user_id": "anonymous", "permissions": ["read", "write"]}


def get_db_manager() -> SQLiteManager:
    """SQLite 데이터베이스 관리자 의존성"""
    if _db_manager is None:
        raise HTTPException(status_code=503, detail="DB manager is not available.")
    return _db_manager


def get_vector_store() -> VectorStore:
    """벡터 스토어 의존성"""
    if _vector_store is None:
        raise HTTPException(status_code=503, detail="Vector store is not available.")
    return _vector_store


def get_git_analyzer() -> GitAnalyzer:
    """Git 분석기 의존성"""
    if _git_analyzer is None:
        raise HTTPException(status_code=503, detail="Git analyzer is not available.")
    return _git_analyzer


def get_codebase_analyzer(
    config: Config = Depends(get_config),
    db_manager: SQLiteManager = Depends(get_db_manager),
    vector_store: VectorStore = Depends(get_vector_store),
    git_analyzer: GitAnalyzer = Depends(get_git_analyzer)
) -> CodebaseAnalyzer:
    """코드베이스 분석기 의존성"""
    global _codebase_analyzer
    if _codebase_analyzer is None:
        _codebase_analyzer = CodebaseAnalyzer(
            repo_path=config.repository.path,
            db_manager=db_manager,
            vector_store=vector_store,
            git_analyzer=git_analyzer
        )
    return _codebase_analyzer


def require_permission(permission: str):
    """권한 확인 데코레이터"""
    def permission_checker(user=Depends(get_current_user)):
        if permission not in user.get("permissions", []):
            raise HTTPException(
                status_code=403,
                detail=f"Permission '{permission}' required"
            )
        return user
    return permission_checker
