"""
FastAPI Main Application

CodeBase Intelligence System의 메인 API 서버
"""

import logging
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse

from ..utils.config import Config
from ..utils.logger import setup_logging
from .routers import search, analysis, llm, health, ollama, webhook
from . import dependencies
from .dependencies import get_config
from ..core.persistence.sqlite_manager import SQLiteManager
from ..core.persistence.vector_store import VectorStore
from ..core.persistence.git_analyzer import GitAnalyzer
from ..core.intelligence.reranker import Reranker
from ..core.intelligence.smart_search import SmartSearch
from ..core.llm.orchestrator import LLMOrchestrator
from ..core.llm.router import LLMRouter
from ..core.llm.providers.ollama_provider import OllamaProvider
from ..core.context.context_builder import ContextBuilder

# 로깅 설정
setup_logging()
logger = logging.getLogger(__name__)


def _create_llm_orchestrator(config: Config, search_system: SmartSearch) -> LLMOrchestrator:
    """LLM Orchestrator 팩토리 함수"""
    # 1. Context Builder 생성
    context_builder = ContextBuilder(smart_search=search_system)

    # 2. LLM Router 생성 (Ollama만 포함)
    ollama_only_providers = {}
    if "ollama" in config.llm.providers and config.llm.providers["ollama"].enabled:
        ollama_only_providers["ollama"] = config.llm.providers["ollama"]

    llm_router = LLMRouter(ollama_only_providers)

    # 3. LLM Providers 생성 (Ollama만 사용)
    providers = {}

    # Ollama Provider 설정만 처리
    if "ollama" in config.llm.providers:
        ollama_config = config.llm.providers["ollama"]
        if ollama_config.enabled:
            base_url = ollama_config.base_url or "http://host.docker.internal:11434"

            ollama_provider = OllamaProvider(
                base_url=base_url,
                models=ollama_config.models or [],
                default_model=ollama_config.default_model or "gemma3:12b",
                max_tokens=ollama_config.max_tokens,
                temperature=ollama_config.temperature
            )
            providers["ollama"] = ollama_provider
            logger.info("Only Ollama provider will be used for LLM operations")

    # 다른 제공자들은 명시적으로 제외
    if not providers:
        logger.warning("No LLM providers available. Ollama provider not found or disabled.")

    # 4. LLM Orchestrator 생성
    orchestrator = LLMOrchestrator(
        context_builder=context_builder,
        llm_router=llm_router,
        providers=providers
    )

    return orchestrator


@asynccontextmanager
async def lifespan(app: FastAPI):
    """애플리케이션 라이프사이클 관리"""
    # 시작 시
    logger.info("Starting CodeBase Intelligence API Server")
    
    # 설정 로드
    config = Config.load()
    app.state.config = config
    
    # 컴포넌트 초기화 및 전역 의존성 설정
    dependencies._config = config

    db_manager = SQLiteManager(config.database.path)
    dependencies._db_manager = db_manager

    # Docker 환경에서는 Docker 게이트웨이 IP 대신 서비스 이름을 사용해야 할 수 있습니다.
    # 여기서는 config.py에 설정된 기본값을 사용합니다.
    ollama_provider_config = config.llm.providers.get('ollama')
    ollama_base_url = ollama_provider_config.base_url if ollama_provider_config else "http://host.docker.internal:11434"

    vector_store = VectorStore(
        host=config.vector_store.host,
        port=config.vector_store.port,
        collection_name=config.vector_store.collection_name,
        model_name=config.vector_store.model,
        vector_size=config.vector_store.vector_size,
        ollama_url=ollama_base_url
    )
    dependencies._vector_store = vector_store

    git_analyzer = GitAnalyzer(config.repository.path)
    dependencies._git_analyzer = git_analyzer

    reranker = Reranker(config.reranker)

    search_system = SmartSearch(
        db_manager=db_manager,
        vector_store=vector_store,
        reranker=reranker,
        git_analyzer=git_analyzer
    )
    dependencies._search_system = search_system

    # LLM Orchestrator 초기화
    try:
        llm_orchestrator = _create_llm_orchestrator(config, search_system)
        dependencies._llm_orchestrator = llm_orchestrator
        logger.info("LLM Orchestrator initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize LLM Orchestrator: {e}")
        dependencies._llm_orchestrator = None
    
    logger.info("API Server started successfully")
    
    yield
    
    # 종료 시
    logger.info("Shutting down CodeBase Intelligence API Server")


# FastAPI 앱 생성
app = FastAPI(
    title="CodeBase Intelligence API",
    description="LLM 페어프로그래밍 최적화를 위한 차세대 코드베이스 관리 시스템",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 미들웨어 설정
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 개발 환경용, 프로덕션에서는 제한 필요
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)

# 라우터 등록
app.include_router(health.router, prefix="/health", tags=["health"])
app.include_router(search.router, prefix="/api/v1/search", tags=["search"])
app.include_router(analysis.router, prefix="/api/v1/analysis", tags=["analysis"])
app.include_router(llm.router, prefix="/api/v1/llm", tags=["llm"])
app.include_router(ollama.router, prefix="/api/v1/ollama", tags=["ollama"])
app.include_router(webhook.router, prefix="/api/v1/webhook", tags=["webhook"])


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """전역 예외 처리"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


@app.get("/")
async def root():
    """루트 엔드포인트"""
    return {
        "message": "CodeBase Intelligence API",
        "version": "0.1.0",
        "docs": "/docs"
    }


@app.get("/info")
async def info(config: Config = Depends(get_config)):
    """시스템 정보"""
    return {
        "name": "CodeBase Intelligence System",
        "version": "0.1.0",
        "description": "LLM 페어프로그래밍 최적화를 위한 차세대 코드베이스 관리 시스템",
        "features": [
            "지능형 컨텍스트 생성",
            "시간 여행 코드 분석", 
            "적응형 LLM 라우팅",
            "프로액티브 코드 인사이트"
        ],
        "config": {
            "repository_path": config.repository.path,
            "database_path": config.database.path,
            "vector_store_path": config.vector_store.path
        }
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "codebase_intelligence.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
