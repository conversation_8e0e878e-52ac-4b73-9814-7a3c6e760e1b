"""
Core modules for CodeBase Intelligence System

이 패키지는 시스템의 핵심 기능들을 포함합니다:
- Data Layer: SQLite, Vector DB, Git 분석
- Intelligence Layer: 의미적 검색, 구조적 분석, 시간적 분석
- Context Layer: 컨텍스트 최적화, 토큰 관리
- LLM Layer: 다중 LLM 통합 및 라우팅
"""

# Data Layer 모듈들
from .persistence.codebase_analyzer import CodebaseAnalyzer
from .persistence.sqlite_manager import SQLiteManager
from .persistence.vector_store import VectorStore
from .persistence.git_analyzer import GitAnalyzer

# 다른 레이어 모듈들
from .context.context_builder import ContextBuilder
from .llm.orchestrator import LLMOrchestrator
from .intelligence.smart_search import SmartSearch

__all__ = [
    # Data Layer
    "CodebaseAnalyzer",
    "SQLiteManager",
    "VectorStore",
    "GitAnalyzer",

    # Other Layers
    "ContextBuilder",
    "LLMOrchestrator",
    "SmartSearch",
]
