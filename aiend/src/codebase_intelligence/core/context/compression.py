"""
Semantic Compression

의미적 압축을 수행하는 클래스
"""

import logging
import re
from typing import Optional

logger = logging.getLogger(__name__)


class SemanticCompressor:
    """의미적 압축기"""
    
    def __init__(self):
        """의미적 압축기 초기화"""
        logger.info("Semantic compressor initialized")
    
    def compress_code(self, code: str, target_ratio: float = 0.7) -> str:
        """
        코드를 의미적으로 압축
        
        Args:
            code: 원본 코드
            target_ratio: 목표 압축 비율 (0.0 ~ 1.0)
            
        Returns:
            압축된 코드
        """
        if target_ratio >= 1.0:
            return code
        
        lines = code.split('\n')
        target_lines = int(len(lines) * target_ratio)
        
        if target_lines >= len(lines):
            return code
        
        # 라인별 중요도 계산
        line_importance = []
        
        for i, line in enumerate(lines):
            importance = self._calculate_line_importance(line)
            line_importance.append((i, line, importance))
        
        # 중요도순으로 정렬
        line_importance.sort(key=lambda x: x[2], reverse=True)
        
        # 상위 라인들 선택
        selected_lines = line_importance[:target_lines]
        selected_lines.sort(key=lambda x: x[0])  # 원래 순서로 정렬
        
        compressed_code = '\n'.join([line[1] for line in selected_lines])
        
        return compressed_code
    
    def _calculate_line_importance(self, line: str) -> float:
        """
        라인의 중요도 계산
        
        Args:
            line: 코드 라인
            
        Returns:
            중요도 점수 (0.0 ~ 1.0)
        """
        line_stripped = line.strip()
        
        if not line_stripped:
            return 0.0  # 빈 줄
        
        if line_stripped.startswith('#'):
            return 0.2  # 주석
        
        # 키워드 기반 중요도
        high_importance_keywords = [
            'def ', 'class ', 'import ', 'from ', 'return ', 'raise ',
            'if __name__', 'async def', '@'
        ]
        
        medium_importance_keywords = [
            'if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except',
            'with ', 'assert ', 'yield '
        ]
        
        for keyword in high_importance_keywords:
            if keyword in line_stripped:
                return 0.9
        
        for keyword in medium_importance_keywords:
            if keyword in line_stripped:
                return 0.6
        
        # 독스트링
        if '"""' in line or "'''" in line:
            return 0.7
        
        # 일반 코드
        return 0.4
    
    def extract_function_signature(self, code: str) -> str:
        """
        함수 시그니처와 독스트링만 추출
        
        Args:
            code: 함수 코드
            
        Returns:
            시그니처와 독스트링
        """
        lines = code.split('\n')
        result_lines = []
        
        in_docstring = False
        docstring_quote = None
        
        for line in lines:
            line_stripped = line.strip()
            
            # 함수 정의
            if line_stripped.startswith('def ') or line_stripped.startswith('async def '):
                result_lines.append(line)
                continue
            
            # 독스트링 시작
            if not in_docstring and ('"""' in line or "'''" in line):
                in_docstring = True
                docstring_quote = '"""' if '"""' in line else "'''"
                result_lines.append(line)
                
                # 같은 줄에 독스트링이 끝나는 경우
                if line.count(docstring_quote) >= 2:
                    in_docstring = False
                continue
            
            # 독스트링 내부
            if in_docstring:
                result_lines.append(line)
                if docstring_quote in line:
                    in_docstring = False
                continue
            
            # 데코레이터
            if line_stripped.startswith('@'):
                result_lines.append(line)
                continue
            
            # 첫 번째 실행 라인에서 중단
            if line_stripped and not line_stripped.startswith('#'):
                result_lines.append('    # ... (구현 생략) ...')
                break
        
        return '\n'.join(result_lines)
    
    def extract_class_signature(self, code: str) -> str:
        """
        클래스 시그니처와 메서드 시그니처만 추출
        
        Args:
            code: 클래스 코드
            
        Returns:
            클래스와 메서드 시그니처
        """
        lines = code.split('\n')
        result_lines = []
        
        in_method = False
        in_docstring = False
        docstring_quote = None
        current_indent = 0
        
        for line in lines:
            line_stripped = line.strip()
            
            # 클래스 정의
            if line_stripped.startswith('class '):
                result_lines.append(line)
                current_indent = len(line) - len(line.lstrip())
                continue
            
            # 클래스 독스트링
            if not in_method and ('"""' in line or "'''" in line):
                in_docstring = True
                docstring_quote = '"""' if '"""' in line else "'''"
                result_lines.append(line)
                
                if line.count(docstring_quote) >= 2:
                    in_docstring = False
                continue
            
            if in_docstring and not in_method:
                result_lines.append(line)
                if docstring_quote in line:
                    in_docstring = False
                continue
            
            # 메서드 정의
            if line_stripped.startswith('def ') or line_stripped.startswith('async def '):
                if in_method:
                    result_lines.append('        # ... (구현 생략) ...')
                    result_lines.append('')
                
                result_lines.append(line)
                in_method = True
                continue
            
            # 메서드 데코레이터
            if in_method and line_stripped.startswith('@'):
                result_lines.append(line)
                continue
            
            # 메서드 독스트링
            if in_method and ('"""' in line or "'''" in line):
                result_lines.append(line)
                # 간단한 독스트링 처리 (완전하지 않음)
                continue
            
            # 클래스 변수나 상수
            if not in_method and '=' in line_stripped and not line_stripped.startswith('#'):
                result_lines.append(line)
                continue
        
        if in_method:
            result_lines.append('        # ... (구현 생략) ...')
        
        return '\n'.join(result_lines)
    
    def compress_to_tokens(self, content: str, max_tokens: int) -> Optional[str]:
        """
        지정된 토큰 수에 맞게 압축
        
        Args:
            content: 원본 콘텐츠
            max_tokens: 최대 토큰 수
            
        Returns:
            압축된 콘텐츠 (실패시 None)
        """
        # 간단한 토큰 추정 (4 문자 = 1 토큰)
        current_chars = len(content)
        target_chars = max_tokens * 4
        
        if current_chars <= target_chars:
            return content
        
        # 압축 비율 계산
        compression_ratio = target_chars / current_chars
        
        # 너무 많이 압축해야 하는 경우
        if compression_ratio < 0.1:
            return None
        
        return self.compress_code(content, compression_ratio)
