"""
Token Manager

토큰 계산 및 예산 관리를 담당하는 클래스
"""

import logging
import re
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class TokenManager:
    """토큰 관리자"""
    
    def __init__(self):
        """토큰 관리자 초기화"""
        # 대략적인 토큰 계산을 위한 상수들
        self.avg_chars_per_token = 4  # 평균 문자 수 per 토큰
        self.code_token_ratio = 3.5   # 코드의 경우 더 적은 문자 수 per 토큰
        
        logger.info("Token manager initialized")
    
    def count_tokens(self, text: str, is_code: bool = True) -> int:
        """
        텍스트의 토큰 수 계산 (근사치)
        
        Args:
            text: 토큰을 계산할 텍스트
            is_code: 코드 텍스트 여부
            
        Returns:
            예상 토큰 수
        """
        if not text:
            return 0
        
        # 기본 문자 수 기반 계산
        char_count = len(text)
        
        if is_code:
            # 코드의 경우 더 정확한 계산
            token_count = self._count_code_tokens(text)
        else:
            # 일반 텍스트
            token_count = char_count / self.avg_chars_per_token
        
        return int(token_count)
    
    def _count_code_tokens(self, code: str) -> int:
        """
        코드 토큰 수 계산
        
        Args:
            code: 코드 텍스트
            
        Returns:
            예상 토큰 수
        """
        # 공백 제거
        code_no_whitespace = re.sub(r'\s+', ' ', code.strip())
        
        # 특수 문자들은 별도 토큰으로 계산
        special_chars = len(re.findall(r'[{}()\[\];,.]', code))
        
        # 키워드와 식별자 계산
        words = re.findall(r'\b\w+\b', code)
        word_tokens = len(words)
        
        # 문자열 리터럴 계산
        strings = re.findall(r'["\'].*?["\']', code)
        string_tokens = sum(len(s) // self.avg_chars_per_token for s in strings)
        
        # 주석 계산
        comments = re.findall(r'#.*?$|//.*?$|/\*.*?\*/', code, re.MULTILINE | re.DOTALL)
        comment_tokens = sum(len(c) // self.avg_chars_per_token for c in comments)
        
        total_tokens = word_tokens + special_chars + string_tokens + comment_tokens
        
        return max(total_tokens, len(code_no_whitespace) // self.code_token_ratio)
    
    def estimate_context_tokens(self, context_data: Dict[str, Any]) -> int:
        """
        컨텍스트 데이터의 토큰 수 추정
        
        Args:
            context_data: 컨텍스트 데이터
            
        Returns:
            예상 토큰 수
        """
        total_tokens = 0
        
        # 텍스트 필드들 계산
        text_fields = ['content', 'description', 'docstring', 'message']
        for field in text_fields:
            if field in context_data and context_data[field]:
                is_code = field in ['content']
                total_tokens += self.count_tokens(context_data[field], is_code)
        
        # 메타데이터 계산 (JSON 형태로 가정)
        if 'metadata' in context_data:
            metadata_str = str(context_data['metadata'])
            total_tokens += self.count_tokens(metadata_str, is_code=False)
        
        return total_tokens
    
    def calculate_budget_allocation(
        self,
        total_budget: int,
        priorities: Dict[str, float]
    ) -> Dict[str, int]:
        """
        우선순위에 따른 토큰 예산 할당
        
        Args:
            total_budget: 총 토큰 예산
            priorities: 컴포넌트별 우선순위 (0.0 ~ 1.0)
            
        Returns:
            컴포넌트별 할당된 토큰 수
        """
        if not priorities:
            return {}
        
        # 우선순위 정규화
        total_priority = sum(priorities.values())
        if total_priority == 0:
            return {key: 0 for key in priorities.keys()}
        
        # 예산 할당
        allocation = {}
        allocated_total = 0
        
        for component, priority in priorities.items():
            allocated = int((priority / total_priority) * total_budget)
            allocation[component] = allocated
            allocated_total += allocated
        
        # 남은 토큰을 가장 높은 우선순위에 할당
        remaining = total_budget - allocated_total
        if remaining > 0:
            highest_priority_component = max(priorities.items(), key=lambda x: x[1])[0]
            allocation[highest_priority_component] += remaining
        
        return allocation
    
    def optimize_token_usage(
        self,
        content: str,
        target_tokens: int,
        is_code: bool = True
    ) -> str:
        """
        토큰 사용량을 목표에 맞게 최적화
        
        Args:
            content: 원본 콘텐츠
            target_tokens: 목표 토큰 수
            is_code: 코드 여부
            
        Returns:
            최적화된 콘텐츠
        """
        current_tokens = self.count_tokens(content, is_code)
        
        if current_tokens <= target_tokens:
            return content
        
        # 압축 비율 계산
        compression_ratio = target_tokens / current_tokens
        
        if is_code:
            return self._compress_code(content, compression_ratio)
        else:
            return self._compress_text(content, compression_ratio)
    
    def _compress_code(self, code: str, ratio: float) -> str:
        """
        코드 압축
        
        Args:
            code: 원본 코드
            ratio: 압축 비율 (0.0 ~ 1.0)
            
        Returns:
            압축된 코드
        """
        lines = code.split('\n')
        target_lines = int(len(lines) * ratio)
        
        if target_lines >= len(lines):
            return code
        
        # 중요도 기반 라인 선택
        important_lines = []
        
        for i, line in enumerate(lines):
            line_stripped = line.strip()
            
            # 빈 줄과 주석 제외
            if not line_stripped or line_stripped.startswith('#'):
                continue
            
            # 함수/클래스 정의는 높은 우선순위
            if any(keyword in line_stripped for keyword in ['def ', 'class ', 'import ', 'from ']):
                important_lines.append((i, line, 3))
            # 독스트링은 중간 우선순위
            elif '"""' in line or "'''" in line:
                important_lines.append((i, line, 2))
            # 일반 코드는 낮은 우선순위
            else:
                important_lines.append((i, line, 1))
        
        # 우선순위순으로 정렬하고 상위 라인들 선택
        important_lines.sort(key=lambda x: x[2], reverse=True)
        selected_lines = important_lines[:target_lines]
        selected_lines.sort(key=lambda x: x[0])  # 원래 순서로 정렬
        
        compressed_code = '\n'.join([line[1] for line in selected_lines])
        
        # 너무 많이 압축된 경우 요약 추가
        if ratio < 0.3:
            compressed_code += '\n\n# ... (코드 생략) ...'
        
        return compressed_code
    
    def _compress_text(self, text: str, ratio: float) -> str:
        """
        텍스트 압축
        
        Args:
            text: 원본 텍스트
            ratio: 압축 비율
            
        Returns:
            압축된 텍스트
        """
        sentences = re.split(r'[.!?]+', text)
        target_sentences = int(len(sentences) * ratio)
        
        if target_sentences >= len(sentences):
            return text
        
        # 문장 길이 기반으로 중요한 문장 선택
        sentence_scores = []
        for sentence in sentences:
            sentence = sentence.strip()
            if sentence:
                # 길이와 키워드 기반 점수
                score = len(sentence.split())
                sentence_scores.append((sentence, score))
        
        # 점수순으로 정렬하고 상위 문장들 선택
        sentence_scores.sort(key=lambda x: x[1], reverse=True)
        selected_sentences = [s[0] for s in sentence_scores[:target_sentences]]
        
        compressed_text = '. '.join(selected_sentences)
        
        if ratio < 0.5:
            compressed_text += '... (내용 생략)'
        
        return compressed_text
