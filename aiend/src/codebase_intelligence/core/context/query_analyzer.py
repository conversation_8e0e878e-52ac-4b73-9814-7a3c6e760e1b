"""
Query Analyzer

사용자 쿼리를 분석하여 의도를 파악하는 클래스
"""

import logging
import re
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class IntentType(str, Enum):
    """쿼리 의도 타입"""
    SEARCH = "search"           # 일반 검색
    UNDERSTAND = "understand"   # 코드 이해
    DEBUG = "debug"            # 디버깅
    IMPLEMENT = "implement"    # 구현
    REFACTOR = "refactor"      # 리팩토링
    DOCUMENT = "document"      # 문서화
    TEST = "test"             # 테스트
    HISTORY = "history"       # 히스토리 조회
    COMPARE = "compare"       # 비교
    OPTIMIZE = "optimize"     # 최적화


@dataclass
class QueryIntent:
    """쿼리 의도"""
    intent_type: IntentType
    target_type: str  # 'function', 'class', 'file', 'general'
    keywords: List[str]
    entities: List[str]
    confidence: float
    metadata: Dict[str, Any]


class QueryAnalyzer:
    """쿼리 분석기"""
    
    def __init__(self):
        """쿼리 분석기 초기화"""
        self._init_patterns()
        logger.info("Query analyzer initialized")
    
    def _init_patterns(self):
        """패턴 초기화"""
        # 의도별 키워드 패턴
        self.intent_patterns = {
            IntentType.SEARCH: [
                r'\b(find|search|look|locate|where)\b',
                r'\b(show|list|get)\b'
            ],
            IntentType.UNDERSTAND: [
                r'\b(what|how|why|explain|understand)\b',
                r'\b(does|works|means|purpose)\b'
            ],
            IntentType.DEBUG: [
                r'\b(debug|error|bug|issue|problem|fix)\b',
                r'\b(wrong|broken|fail|crash)\b'
            ],
            IntentType.IMPLEMENT: [
                r'\b(implement|create|build|make|add)\b',
                r'\b(write|code|develop)\b'
            ],
            IntentType.REFACTOR: [
                r'\b(refactor|improve|clean|optimize|restructure)\b',
                r'\b(better|enhance|modify)\b'
            ],
            IntentType.DOCUMENT: [
                r'\b(document|comment|explain|describe)\b',
                r'\b(documentation|docs|readme)\b'
            ],
            IntentType.TEST: [
                r'\b(test|testing|unittest|spec)\b',
                r'\b(verify|validate|check)\b'
            ],
            IntentType.HISTORY: [
                r'\b(history|when|changed|modified|evolution)\b',
                r'\b(commit|version|git|recent)\b'
            ],
            IntentType.COMPARE: [
                r'\b(compare|difference|diff|versus|vs)\b',
                r'\b(similar|different|like)\b'
            ],
            IntentType.OPTIMIZE: [
                r'\b(optimize|performance|speed|faster)\b',
                r'\b(efficient|memory|cpu)\b'
            ]
        }
        
        # 대상 타입 패턴
        self.target_patterns = {
            'function': [
                r'\b(function|method|def|func)\b',
                r'\b(call|invoke|execute)\b'
            ],
            'class': [
                r'\b(class|object|instance|type)\b',
                r'\b(inherit|extend|implement)\b'
            ],
            'file': [
                r'\b(file|module|script|package)\b',
                r'\b(import|include|require)\b'
            ],
            'variable': [
                r'\b(variable|var|field|property|attribute)\b'
            ]
        }
        
        # 엔티티 추출 패턴
        self.entity_patterns = [
            r'\b[A-Z][a-zA-Z]*\b',  # CamelCase (클래스명 등)
            r'\b[a-z_][a-z0-9_]*\b',  # snake_case (함수명, 변수명 등)
            r'\b\w+\.\w+\b',  # 모듈.함수 형태
            r'\"[^\"]+\"',  # 따옴표로 둘러싸인 문자열
            r"\'[^\']+\'"   # 작은따옴표로 둘러싸인 문자열
        ]
    
    def analyze(self, query: str) -> QueryIntent:
        """
        쿼리 분석
        
        Args:
            query: 분석할 쿼리
            
        Returns:
            쿼리 의도
        """
        logger.debug(f"Analyzing query: '{query}'")
        
        query_lower = query.lower()
        
        # 의도 타입 분석
        intent_type, intent_confidence = self._analyze_intent(query_lower)
        
        # 대상 타입 분석
        target_type = self._analyze_target_type(query_lower)
        
        # 키워드 추출
        keywords = self._extract_keywords(query_lower)
        
        # 엔티티 추출
        entities = self._extract_entities(query)
        
        # 메타데이터 생성
        metadata = {
            "query_length": len(query),
            "word_count": len(query.split()),
            "has_quotes": '"' in query or "'" in query,
            "has_camelcase": bool(re.search(r'\b[A-Z][a-zA-Z]*\b', query)),
            "has_snake_case": bool(re.search(r'\b[a-z_][a-z0-9_]*\b', query))
        }
        
        query_intent = QueryIntent(
            intent_type=intent_type,
            target_type=target_type,
            keywords=keywords,
            entities=entities,
            confidence=intent_confidence,
            metadata=metadata
        )
        
        logger.debug(f"Query intent: {intent_type}, target: {target_type}, confidence: {intent_confidence:.2f}")
        return query_intent
    
    def _analyze_intent(self, query: str) -> tuple[IntentType, float]:
        """의도 타입 분석"""
        intent_scores = {}
        
        for intent_type, patterns in self.intent_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query, re.IGNORECASE))
                score += matches
            
            if score > 0:
                intent_scores[intent_type] = score
        
        if not intent_scores:
            return IntentType.SEARCH, 0.5  # 기본값
        
        # 최고 점수의 의도 선택
        best_intent = max(intent_scores.items(), key=lambda x: x[1])
        intent_type = best_intent[0]
        
        # 신뢰도 계산 (0.5 ~ 1.0 범위)
        max_score = best_intent[1]
        total_score = sum(intent_scores.values())
        confidence = 0.5 + (max_score / total_score) * 0.5
        
        return intent_type, confidence
    
    def _analyze_target_type(self, query: str) -> str:
        """대상 타입 분석"""
        target_scores = {}
        
        for target_type, patterns in self.target_patterns.items():
            score = 0
            for pattern in patterns:
                matches = len(re.findall(pattern, query, re.IGNORECASE))
                score += matches
            
            if score > 0:
                target_scores[target_type] = score
        
        if not target_scores:
            return "general"
        
        # 최고 점수의 대상 타입 선택
        return max(target_scores.items(), key=lambda x: x[1])[0]
    
    def _extract_keywords(self, query: str) -> List[str]:
        """키워드 추출"""
        # 불용어 제거
        stop_words = {
            'a', 'an', 'and', 'are', 'as', 'at', 'be', 'by', 'for', 'from',
            'has', 'he', 'in', 'is', 'it', 'its', 'of', 'on', 'that', 'the',
            'to', 'was', 'will', 'with', 'this', 'that', 'these', 'those',
            'i', 'me', 'my', 'myself', 'we', 'our', 'ours', 'ourselves',
            'you', 'your', 'yours', 'yourself', 'yourselves'
        }
        
        # 단어 추출 및 필터링
        words = re.findall(r'\b\w+\b', query.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def _extract_entities(self, query: str) -> List[str]:
        """엔티티 추출"""
        entities = []
        
        for pattern in self.entity_patterns:
            matches = re.findall(pattern, query)
            entities.extend(matches)
        
        # 중복 제거 및 정리
        entities = list(set(entities))
        entities = [entity.strip('"\'') for entity in entities]
        
        return entities
