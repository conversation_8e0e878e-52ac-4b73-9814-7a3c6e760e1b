"""
Context Builder

LLM을 위한 최적화된 컨텍스트를 생성하는 클래스
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..intelligence.smart_search import SmartSearch, SearchQuery, EnhancedSearchResult
from .query_analyzer import QueryAnalyzer, QueryIntent
from .token_manager import TokenManager
from .compression import SemanticCompressor

logger = logging.getLogger(__name__)


class ContextLevel(str, Enum):
    """컨텍스트 레벨"""
    MINIMAL = "minimal"      # 함수 시그니처 + 독스트링만
    STANDARD = "standard"    # 핵심 로직 + 의존성
    DETAILED = "detailed"    # 전체 구현 + 테스트
    COMPREHENSIVE = "comprehensive"  # 모든 관련 정보


@dataclass
class ContextChunk:
    """컨텍스트 청크"""
    content: str
    file_path: str
    start_line: int
    end_line: int
    context_type: str  # 'function', 'class', 'file', 'documentation'
    priority: float
    token_count: int
    metadata: Dict[str, Any]


@dataclass
class OptimizedContext:
    """최적화된 컨텍스트"""
    chunks: List[ContextChunk]
    total_tokens: int
    compression_ratio: float
    query_intent: QueryIntent
    explanation: str
    suggestions: List[str]


class ContextBuilder:
    """컨텍스트 빌더"""
    
    def __init__(
        self,
        smart_search: SmartSearch,
        query_analyzer: Optional[QueryAnalyzer] = None,
        token_manager: Optional[TokenManager] = None,
        compressor: Optional[SemanticCompressor] = None
    ):
        """
        Args:
            smart_search: 지능형 검색 엔진
            query_analyzer: 쿼리 분석기
            token_manager: 토큰 관리자
            compressor: 의미적 압축기
        """
        self.smart_search = smart_search
        self.query_analyzer = query_analyzer or QueryAnalyzer()
        self.token_manager = token_manager or TokenManager()
        self.compressor = compressor or SemanticCompressor()
        
        logger.info("Context builder initialized")
    
    def build_context(
        self,
        query: str,
        max_tokens: int = 8000,
        context_level: ContextLevel = ContextLevel.STANDARD,
        include_history: bool = True,
        include_tests: bool = False
    ) -> OptimizedContext:
        """최적화된 컨텍스트 생성"""
        logger.info(f"Building context for query: '{query}' (max_tokens: {max_tokens})")
        
        # 1. 쿼리 의도 분석
        query_intent = self.query_analyzer.analyze(query)
        logger.debug(f"Query intent: {query_intent.intent_type}")
        
        # 2. 검색 쿼리 구성
        search_query = self._create_search_query(query, query_intent, include_history)
        
        # 3. 관련 코드 검색
        search_results = self.smart_search.search(search_query)
        logger.debug(f"Found {len(search_results)} search results")
        
        # 4. 컨텍스트 청크 생성
        chunks = self._create_context_chunks(
            search_results, 
            query_intent, 
            context_level,
            include_tests
        )
        
        # 5. 우선순위 기반 정렬
        chunks = self._prioritize_chunks(chunks, query_intent)
        
        # 6. 토큰 예산에 맞게 선택
        selected_chunks = self._select_chunks_by_budget(chunks, max_tokens)
        
        # 7. 압축 적용 (필요시)
        if self._should_compress(selected_chunks, max_tokens):
            selected_chunks = self._compress_chunks(selected_chunks, max_tokens)
        
        # 8. 최종 컨텍스트 구성
        total_tokens = sum(chunk.token_count for chunk in selected_chunks)
        compression_ratio = self._calculate_compression_ratio(chunks, selected_chunks)
        
        # 9. 설명 및 제안 생성
        explanation = self._generate_explanation(query_intent, selected_chunks)
        suggestions = self.smart_search.suggest_related_queries(query)
        
        optimized_context = OptimizedContext(
            chunks=selected_chunks,
            total_tokens=total_tokens,
            compression_ratio=compression_ratio,
            query_intent=query_intent,
            explanation=explanation,
            suggestions=suggestions
        )
        
        logger.info(f"Context built: {len(selected_chunks)} chunks, {total_tokens} tokens")
        return optimized_context
    
    def _create_search_query(
        self, 
        query: str, 
        intent: QueryIntent, 
        include_history: bool
    ) -> SearchQuery:
        """검색 쿼리 생성"""
        # 의도에 따른 컨텍스트 타입 결정
        context_types = []
        if intent.target_type == "function":
            context_types = ["function"]
        elif intent.target_type == "class":
            context_types = ["class"]
        elif intent.target_type == "file":
            context_types = ["file"]
        
        # 검색 모드 결정
        if intent.intent_type in ["debug", "understand", "history"]:
            search_mode = "hybrid"
        elif intent.intent_type in ["implement", "refactor"]:
            search_mode = "structural"
        else:
            search_mode = "auto"
        
        return SearchQuery(
            text=query,
            mode=search_mode,
            context_types=context_types,
            max_results=20,  # 충분한 결과 수집
            include_history=include_history
        )
    
    def _create_context_chunks(
        self,
        search_results: List[EnhancedSearchResult],
        query_intent: QueryIntent,
        context_level: ContextLevel,
        include_tests: bool
    ) -> List[ContextChunk]:
        """검색 결과를 컨텍스트 청크로 변환"""
        chunks = []
        
        for result in search_results:
            # 기본 청크 생성
            chunk = self._result_to_chunk(result, context_level)
            if chunk:
                chunks.append(chunk)
            
            # 관련 정보 추가
            if context_level in [ContextLevel.DETAILED, ContextLevel.COMPREHENSIVE]:
                # 의존성 정보 추가
                dependency_chunks = self._get_dependency_chunks(result, context_level)
                chunks.extend(dependency_chunks)
                
                # 테스트 코드 추가
                if include_tests:
                    test_chunks = self._get_test_chunks(result)
                    chunks.extend(test_chunks)
        
        return chunks
    
    def _result_to_chunk(
        self, 
        result: EnhancedSearchResult, 
        context_level: ContextLevel
    ) -> Optional[ContextChunk]:
        """검색 결과를 컨텍스트 청크로 변환"""
        try:
            content = result.search_result.content
            
            # 컨텍스트 레벨에 따른 내용 조정
            if context_level == ContextLevel.MINIMAL:
                content = self._extract_minimal_content(content, result.search_result.context_type)
            elif context_level == ContextLevel.STANDARD:
                content = self._extract_standard_content(content, result.search_result.context_type)
            
            # 토큰 수 계산
            token_count = self.token_manager.count_tokens(content)
            
            # 우선순위 계산
            priority = self._calculate_chunk_priority(result)
            
            return ContextChunk(
                content=content,
                file_path=result.search_result.file_path,
                start_line=result.search_result.start_line,
                end_line=result.search_result.end_line,
                context_type=result.search_result.context_type,
                priority=priority,
                token_count=token_count,
                metadata=result.search_result.metadata
            )
        except Exception as e:
            logger.error(f"Failed to convert result to chunk: {e}")
            return None
    
    def _extract_minimal_content(self, content: str, context_type: str) -> str:
        """최소한의 내용만 추출 (시그니처 + 독스트링)"""
        if context_type == "function":
            return self.compressor.extract_function_signature(content)
        elif context_type == "class":
            return self.compressor.extract_class_signature(content)
        else:
            # 파일의 경우 상위 몇 줄만
            lines = content.split('\n')
            return '\n'.join(lines[:10]) + '\n...' if len(lines) > 10 else content
    
    def _extract_standard_content(self, content: str, context_type: str) -> str:
        """표준 내용 추출 (핵심 로직 포함)"""
        if context_type in ["function", "class"]:
            return self.compressor.compress_code(content, target_ratio=0.7)
        else:
            return content
    
    def _calculate_chunk_priority(self, result: EnhancedSearchResult) -> float:
        """청크 우선순위 계산"""
        priority = result.combined_score
        
        # 컨텍스트 타입별 가중치
        type_weights = {
            "function": 1.0,
            "class": 0.9,
            "file": 0.7,
            "documentation": 0.8
        }
        
        context_type = result.search_result.context_type
        priority *= type_weights.get(context_type, 0.5)
        
        # 최근 변경 가중치
        if result.related_changes:
            priority *= 1.2
        
        return priority
    
    def _get_dependency_chunks(
        self, 
        result: EnhancedSearchResult, 
        context_level: ContextLevel
    ) -> List[ContextChunk]:
        """의존성 관련 청크 조회"""
        # TODO: 의존성 분석 구현
        return []
    
    def _get_test_chunks(self, result: EnhancedSearchResult) -> List[ContextChunk]:
        """테스트 관련 청크 조회"""
        # TODO: 테스트 파일 검색 구현
        return []
    
    def _prioritize_chunks(
        self, 
        chunks: List[ContextChunk], 
        query_intent: QueryIntent
    ) -> List[ContextChunk]:
        """우선순위 기반 청크 정렬"""
        return sorted(chunks, key=lambda x: x.priority, reverse=True)
    
    def _select_chunks_by_budget(
        self, 
        chunks: List[ContextChunk], 
        max_tokens: int
    ) -> List[ContextChunk]:
        """토큰 예산에 맞게 청크 선택"""
        selected_chunks = []
        total_tokens = 0
        
        for chunk in chunks:
            if total_tokens + chunk.token_count <= max_tokens:
                selected_chunks.append(chunk)
                total_tokens += chunk.token_count
            else:
                # 남은 토큰으로 부분 청크 생성 시도
                remaining_tokens = max_tokens - total_tokens
                if remaining_tokens > 100:  # 최소 토큰 수
                    partial_chunk = self._create_partial_chunk(chunk, remaining_tokens)
                    if partial_chunk:
                        selected_chunks.append(partial_chunk)
                break
        
        return selected_chunks
    
    def _create_partial_chunk(
        self, 
        chunk: ContextChunk, 
        max_tokens: int
    ) -> Optional[ContextChunk]:
        """부분 청크 생성"""
        try:
            compressed_content = self.compressor.compress_to_tokens(
                chunk.content, max_tokens
            )
            
            if compressed_content:
                return ContextChunk(
                    content=compressed_content,
                    file_path=chunk.file_path,
                    start_line=chunk.start_line,
                    end_line=chunk.end_line,
                    context_type=chunk.context_type,
                    priority=chunk.priority * 0.8,  # 부분 청크는 우선순위 감소
                    token_count=max_tokens,
                    metadata={**chunk.metadata, "partial": True}
                )
        except Exception as e:
            logger.error(f"Failed to create partial chunk: {e}")
        
        return None
    
    def _should_compress(self, chunks: List[ContextChunk], max_tokens: int) -> bool:
        """압축이 필요한지 판단"""
        total_tokens = sum(chunk.token_count for chunk in chunks)
        return total_tokens > max_tokens * 0.9  # 90% 이상 사용시 압축 고려
    
    def _compress_chunks(
        self, 
        chunks: List[ContextChunk], 
        max_tokens: int
    ) -> List[ContextChunk]:
        """청크들 압축"""
        compressed_chunks = []
        
        for chunk in chunks:
            if chunk.context_type in ["function", "class"]:
                compressed_content = self.compressor.compress_code(
                    chunk.content, target_ratio=0.8
                )
                compressed_tokens = self.token_manager.count_tokens(compressed_content)
                
                compressed_chunk = ContextChunk(
                    content=compressed_content,
                    file_path=chunk.file_path,
                    start_line=chunk.start_line,
                    end_line=chunk.end_line,
                    context_type=chunk.context_type,
                    priority=chunk.priority,
                    token_count=compressed_tokens,
                    metadata={**chunk.metadata, "compressed": True}
                )
                compressed_chunks.append(compressed_chunk)
            else:
                compressed_chunks.append(chunk)
        
        return compressed_chunks
    
    def _calculate_compression_ratio(
        self, 
        original_chunks: List[ContextChunk], 
        selected_chunks: List[ContextChunk]
    ) -> float:
        """압축 비율 계산"""
        original_tokens = sum(chunk.token_count for chunk in original_chunks)
        selected_tokens = sum(chunk.token_count for chunk in selected_chunks)
        
        if original_tokens == 0:
            return 1.0
        
        return selected_tokens / original_tokens
    
    def _generate_explanation(
        self, 
        query_intent: QueryIntent, 
        chunks: List[ContextChunk]
    ) -> str:
        """컨텍스트 설명 생성"""
        explanation_parts = [
            f"Query intent: {query_intent.intent_type}",
            f"Target: {query_intent.target_type}",
            f"Context chunks: {len(chunks)}",
            f"Total tokens: {sum(chunk.token_count for chunk in chunks)}"
        ]
        
        # 청크 타입별 통계
        type_counts = {}
        for chunk in chunks:
            type_counts[chunk.context_type] = type_counts.get(chunk.context_type, 0) + 1
        
        if type_counts:
            type_summary = ", ".join([f"{k}: {v}" for k, v in type_counts.items()])
            explanation_parts.append(f"Types: {type_summary}")
        
        return "; ".join(explanation_parts)
    
    def format_context_for_llm(self, context: OptimizedContext) -> str:
        """LLM을 위한 컨텍스트 포맷팅"""
        formatted_parts = []
        
        # 헤더 정보
        formatted_parts.append("# Codebase Context")
        formatted_parts.append(f"Query Intent: {context.query_intent.intent_type}")
        formatted_parts.append(f"Total Tokens: {context.total_tokens}")
        formatted_parts.append("")
        
        # 각 청크 포맷팅
        for i, chunk in enumerate(context.chunks, 1):
            formatted_parts.append(f"## Context {i}: {chunk.context_type}")
            formatted_parts.append(f"File: {chunk.file_path} (lines {chunk.start_line}-{chunk.end_line})")
            formatted_parts.append(f"Priority: {chunk.priority:.2f}")
            formatted_parts.append("")
            formatted_parts.append("```")
            formatted_parts.append(chunk.content)
            formatted_parts.append("```")
            formatted_parts.append("")
        
        return "\n".join(formatted_parts)
