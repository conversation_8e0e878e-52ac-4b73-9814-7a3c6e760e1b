"""
Base LLM Provider

LLM 제공자의 기본 인터페이스
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)


@dataclass
class LLMResponse:
    """LLM 응답"""
    content: str
    provider_name: str
    model_name: str
    confidence: float
    usage: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime


class BaseLLMProvider(ABC):
    """LLM 제공자 기본 클래스"""
    
    def __init__(self, name: str, api_key: str, **kwargs):
        """
        Args:
            name: 제공자 이름
            api_key: API 키
            **kwargs: 추가 설정
        """
        self.name = name
        self.api_key = api_key
        self.config = kwargs
        self.usage_stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "errors": 0
        }
        
        logger.info(f"Initialized {name} provider")
    
    @abstractmethod
    async def generate_response(
        self,
        query: str,
        context: str,
        model: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> LLMResponse:
        """
        응답 생성
        
        Args:
            query: 사용자 쿼리
            context: 컨텍스트
            model: 모델명
            parameters: 추가 파라미터
            
        Returns:
            LLM 응답
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """제공자 사용 가능 여부 확인"""
        pass
    
    @abstractmethod
    def get_available_models(self) -> List[str]:
        """사용 가능한 모델 목록"""
        pass
    
    def update_usage_stats(self, tokens_used: int, success: bool = True):
        """사용량 통계 업데이트"""
        self.usage_stats["total_requests"] += 1
        if success:
            self.usage_stats["total_tokens"] += tokens_used
        else:
            self.usage_stats["errors"] += 1
