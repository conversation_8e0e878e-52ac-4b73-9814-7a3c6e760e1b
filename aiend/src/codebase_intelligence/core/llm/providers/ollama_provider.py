"""
Ollama Provider

Ollama를 사용하는 로컬 LLM 제공자
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime
import httpx

from .base import BaseLLMProvider, LLMResponse

logger = logging.getLogger(__name__)


class OllamaProvider(BaseLLMProvider):
    """Ollama LLM 제공자"""
    
    def __init__(self, base_url: str = "http://localhost:11434", **kwargs):
        """
        Args:
            base_url: Ollama 서버 URL
            **kwargs: 추가 설정
        """
        super().__init__("ollama", "", **kwargs)
        self.base_url = base_url.rstrip('/')
        self.client = httpx.AsyncClient(timeout=600.0)  # 10분으로 증가 (백그라운드 작업용)
        
        # 사용 가능한 모델 목록 (동적으로 로드)
        self.available_models = []
        
    async def _load_available_models(self):
        """사용 가능한 모델 목록 로드"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                self.available_models = [model["name"] for model in data.get("models", [])]
            else:
                logger.warning(f"Failed to load Ollama models: {response.status_code}")
        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {e}")
    
    async def generate_response(
        self,
        query: str,
        context: str,
        model: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> LLMResponse:
        """Ollama API를 사용하여 응답 생성"""
        
        logger.debug(f"Generating response with Ollama {model}")
        
        try:
            # 파라미터 설정
            params = {
                "temperature": 0.2,
                "top_p": 0.9,
                "top_k": 40,
                **(parameters or {})
            }
            
            # 프롬프트 구성
            prompt = self._build_prompt(query, context)
            
            # Ollama API 호출
            request_data = {
                "model": model,
                "prompt": prompt,
                "stream": False,
                "options": params
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/generate",
                json=request_data
            )
            
            if response.status_code != 200:
                raise Exception(f"Ollama API error: {response.status_code} - {response.text}")
            
            result = response.json()
            content = result.get("response", "")
            
            # 사용량 통계 업데이트
            tokens_used = len(prompt) // 4 + len(content) // 4
            self.update_usage_stats(tokens_used, success=True)
            
            return LLMResponse(
                content=content,
                provider_name=self.name,
                model_name=model,
                confidence=0.80,  # Ollama는 신뢰도 점수를 제공하지 않음
                usage={
                    "prompt_tokens": len(prompt) // 4,
                    "completion_tokens": len(content) // 4,
                    "total_tokens": tokens_used,
                    "eval_count": result.get("eval_count", 0),
                    "eval_duration": result.get("eval_duration", 0)
                },
                metadata={
                    "parameters": params,
                    "prompt_length": len(prompt),
                    "model_info": result.get("model", {}),
                    "done": result.get("done", False)
                },
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Ollama API error: {e}")
            self.update_usage_stats(0, success=False)
            raise
    
    def _build_prompt(self, query: str, context: str) -> str:
        """프롬프트 구성"""
        return f"""You are an expert code assistant. Use the following codebase context to answer the user's question accurately and helpfully.

<codebase_context>
{context}
</codebase_context>

<user_question>
{query}
</user_question>

Please provide a comprehensive answer based on the codebase context. Be specific and reference the relevant code when appropriate."""
    
    async def is_available(self) -> bool:
        """Ollama 제공자 사용 가능 여부"""
        try:
            response = await self.client.get(f"{self.base_url}/api/version")
            return response.status_code == 200
        except Exception:
            return False
    
    async def get_available_models(self) -> List[str]:
        """사용 가능한 모델 목록"""
        if not self.available_models:
            await self._load_available_models()
        return self.available_models.copy()
    
    async def pull_model(self, model_name: str) -> bool:
        """모델 다운로드"""
        try:
            logger.info(f"Pulling Ollama model: {model_name}")
            
            request_data = {"name": model_name}
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/pull",
                json=request_data
            ) as response:
                if response.status_code != 200:
                    logger.error(f"Failed to pull model {model_name}: {response.status_code}")
                    return False
                
                async for line in response.aiter_lines():
                    if line:
                        # 진행 상황 로깅 (실제로는 더 정교한 처리 필요)
                        logger.debug(f"Pull progress: {line}")
            
            # 모델 목록 새로고침
            await self._load_available_models()
            
            logger.info(f"Successfully pulled model: {model_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pull model {model_name}: {e}")
            return False
    
    async def delete_model(self, model_name: str) -> bool:
        """모델 삭제"""
        try:
            request_data = {"name": model_name}
            
            response = await self.client.delete(
                f"{self.base_url}/api/delete",
                json=request_data
            )
            
            if response.status_code == 200:
                # 모델 목록 새로고침
                await self._load_available_models()
                logger.info(f"Successfully deleted model: {model_name}")
                return True
            else:
                logger.error(f"Failed to delete model {model_name}: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to delete model {model_name}: {e}")
            return False
    
    async def get_model_info(self, model_name: str) -> Optional[Dict[str, Any]]:
        """모델 정보 조회"""
        try:
            request_data = {"name": model_name}
            
            response = await self.client.post(
                f"{self.base_url}/api/show",
                json=request_data
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"Failed to get model info for {model_name}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Failed to get model info for {model_name}: {e}")
            return None
    
    async def close(self):
        """클라이언트 정리"""
        await self.client.aclose()
