"""
LLM Router

LLM 라우팅을 담당하는 클래스
"""

import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..context.context_builder import OptimizedContext

logger = logging.getLogger(__name__)


class TaskType(str, Enum):
    """작업 타입"""
    CODE_GENERATION = "code_generation"
    CODE_REVIEW = "code_review"
    DEBUGGING = "debugging"
    DOCUMENTATION = "documentation"
    REFACTORING = "refactoring"
    ANALYSIS = "analysis"
    GENERAL = "general"


@dataclass
class RoutingDecision:
    """라우팅 결정"""
    provider_name: str
    model_name: str
    task_type: TaskType
    confidence: float
    parameters: Dict[str, Any]
    reasoning: str


class LLMRouter:
    """LLM 라우터"""
    
    def __init__(self, provider_configs: Dict[str, Any]):
        """
        Args:
            provider_configs: 제공자별 설정
        """
        self.provider_configs = provider_configs
        logger.info("LLM router initialized")
    
    def route_query(
        self,
        query: str,
        context: OptimizedContext,
        strategy: str = "auto"
    ) -> List[RoutingDecision]:
        """
        쿼리를 분석하여 최적의 LLM 라우팅 결정
        
        Args:
            query: 사용자 쿼리
            context: 최적화된 컨텍스트
            strategy: 라우팅 전략
            
        Returns:
            라우팅 결정 리스트 (우선순위순)
        """
        logger.debug(f"Routing query with strategy: {strategy}")
        
        # 작업 타입 분석
        task_type = self._analyze_task_type(query, context)
        
        # 제공자별 점수 계산
        provider_scores = self._calculate_provider_scores(task_type, context)
        
        # 라우팅 결정 생성
        decisions = []
        for provider_name, score in provider_scores.items():
            if provider_name in self.provider_configs:
                config = self.provider_configs[provider_name]

                decision = RoutingDecision(
                    provider_name=provider_name,
                    model_name=config.default_model or "",
                    task_type=task_type,
                    confidence=score,
                    parameters=self._get_task_parameters(task_type),
                    reasoning=f"Best for {task_type.value} tasks"
                )
                decisions.append(decision)
        
        # 점수순으로 정렬
        decisions.sort(key=lambda x: x.confidence, reverse=True)
        
        return decisions
    
    def _analyze_task_type(self, query: str, context: OptimizedContext) -> TaskType:
        """작업 타입 분석"""
        query_lower = query.lower()
        intent_type = context.query_intent.intent_type
        
        # 의도 기반 작업 타입 매핑
        intent_to_task = {
            "implement": TaskType.CODE_GENERATION,
            "debug": TaskType.DEBUGGING,
            "document": TaskType.DOCUMENTATION,
            "refactor": TaskType.REFACTORING,
            "understand": TaskType.ANALYSIS,
        }
        
        if intent_type in intent_to_task:
            return intent_to_task[intent_type]
        
        # 키워드 기반 분석
        if any(kw in query_lower for kw in ["create", "implement", "build", "write"]):
            return TaskType.CODE_GENERATION
        elif any(kw in query_lower for kw in ["review", "check", "analyze"]):
            return TaskType.CODE_REVIEW
        elif any(kw in query_lower for kw in ["bug", "error", "fix", "debug"]):
            return TaskType.DEBUGGING
        elif any(kw in query_lower for kw in ["document", "explain", "describe"]):
            return TaskType.DOCUMENTATION
        elif any(kw in query_lower for kw in ["refactor", "improve", "optimize"]):
            return TaskType.REFACTORING
        else:
            return TaskType.GENERAL
    
    def _calculate_provider_scores(
        self, 
        task_type: TaskType, 
        context: OptimizedContext
    ) -> Dict[str, float]:
        """제공자별 점수 계산"""
        # 기본 점수 (실제로는 더 정교한 로직 필요)
        base_scores = {
            "openai": 0.8,
            "anthropic": 0.9,
        }
        
        # 작업 타입별 가중치
        task_weights = {
            TaskType.CODE_GENERATION: {"openai": 1.0, "anthropic": 0.9},
            TaskType.CODE_REVIEW: {"openai": 0.9, "anthropic": 1.0},
            TaskType.DEBUGGING: {"openai": 1.0, "anthropic": 0.8},
            TaskType.DOCUMENTATION: {"openai": 1.0, "anthropic": 0.9},
            TaskType.REFACTORING: {"openai": 0.8, "anthropic": 1.0},
            TaskType.ANALYSIS: {"openai": 0.9, "anthropic": 1.0},
            TaskType.GENERAL: {"openai": 0.9, "anthropic": 0.9},
        }
        
        scores = {}
        weights = task_weights.get(task_type, {})
        
        for provider, base_score in base_scores.items():
            weight = weights.get(provider, 1.0)
            scores[provider] = base_score * weight
        
        return scores
    
    def _get_task_parameters(self, task_type: TaskType) -> Dict[str, Any]:
        """작업 타입별 파라미터 설정"""
        task_params = {
            TaskType.CODE_GENERATION: {
                "temperature": 0.2,
                "max_tokens": 2048,
                "top_p": 0.9
            },
            TaskType.CODE_REVIEW: {
                "temperature": 0.1,
                "max_tokens": 1024,
                "top_p": 0.8
            },
            TaskType.DEBUGGING: {
                "temperature": 0.1,
                "max_tokens": 1024,
                "top_p": 0.8
            },
            TaskType.DOCUMENTATION: {
                "temperature": 0.3,
                "max_tokens": 1024,
                "top_p": 0.9
            },
            TaskType.REFACTORING: {
                "temperature": 0.2,
                "max_tokens": 2048,
                "top_p": 0.9
            },
            TaskType.ANALYSIS: {
                "temperature": 0.1,
                "max_tokens": 1024,
                "top_p": 0.8
            },
            TaskType.GENERAL: {
                "temperature": 0.2,
                "max_tokens": 1024,
                "top_p": 0.9
            }
        }
        
        return task_params.get(task_type, {
            "temperature": 0.2,
            "max_tokens": 1024,
            "top_p": 0.9
        })
