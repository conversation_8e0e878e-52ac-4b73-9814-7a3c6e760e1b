"""
LLM Orchestrator

다중 LLM을 조율하고 최적의 응답을 생성하는 오케스트레이터
"""

import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum
import asyncio

from ..context.context_builder import ContextBuilder, OptimizedContext
from .router import LLMRouter, RoutingDecision
from .providers.base import BaseLLMProvider, LLMResponse

logger = logging.getLogger(__name__)


class ResponseStrategy(str, Enum):
    """응답 전략"""
    SINGLE_BEST = "single_best"      # 최적 LLM 하나만 사용
    MULTI_VOTE = "multi_vote"        # 여러 LLM 결과 투표
    HYBRID = "hybrid"                # 하이브리드 접근
    CONSENSUS = "consensus"          # 합의 기반


@dataclass
class OrchestratedResponse:
    """오케스트레이션된 응답"""
    content: str
    confidence: float
    strategy_used: ResponseStrategy
    llm_responses: List[LLMResponse]
    context_used: OptimizedContext
    metadata: Dict[str, Any]
    execution_time: float


class LLMOrchestrator:
    """LLM 오케스트레이터"""
    
    def __init__(
        self,
        context_builder: Con<PERSON><PERSON>uild<PERSON>,
        llm_router: LLMRouter,
        providers: Dict[str, BaseLLMProvider],
        default_strategy: ResponseStrategy = ResponseStrategy.SINGLE_BEST
    ):
        """
        Args:
            context_builder: 컨텍스트 빌더
            llm_router: LLM 라우터
            providers: LLM 제공자들
            default_strategy: 기본 응답 전략
        """
        self.context_builder = context_builder
        self.llm_router = llm_router
        self.providers = providers
        self.default_strategy = default_strategy
        
        logger.info(f"LLM Orchestrator initialized with {len(providers)} providers")
    
    async def process_query(
        self,
        query: str,
        max_tokens: int = 8000,
        strategy: Optional[ResponseStrategy] = None,
        context_level: str = "standard",
        include_history: bool = True
    ) -> OrchestratedResponse:
        """쿼리 처리 및 응답 생성"""
        import time
        start_time = time.time()
        
        logger.info(f"Processing query: '{query[:100]}...'")
        
        try:
            # 1. 컨텍스트 생성
            context = self.context_builder.build_context(
                query=query,
                max_tokens=max_tokens,
                context_level=context_level,
                include_history=include_history
            )
            
            # 2. 응답 전략 결정
            response_strategy = strategy or self._determine_strategy(query, context)
            
            # 3. LLM 라우팅
            routing_decisions = self.llm_router.route_query(
                query=query,
                context=context,
                strategy=response_strategy
            )
            
            # 4. 전략별 응답 생성
            if response_strategy == ResponseStrategy.SINGLE_BEST:
                llm_responses = await self._single_best_response(
                    query, context, routing_decisions
                )
            elif response_strategy == ResponseStrategy.MULTI_VOTE:
                llm_responses = await self._multi_vote_response(
                    query, context, routing_decisions
                )
            elif response_strategy == ResponseStrategy.HYBRID:
                llm_responses = await self._hybrid_response(
                    query, context, routing_decisions
                )
            else:  # CONSENSUS
                llm_responses = await self._consensus_response(
                    query, context, routing_decisions
                )
            
            # 5. 최종 응답 통합
            final_content, confidence = self._integrate_responses(
                llm_responses, response_strategy
            )
            
            execution_time = time.time() - start_time
            
            orchestrated_response = OrchestratedResponse(
                content=final_content,
                confidence=confidence,
                strategy_used=response_strategy,
                llm_responses=llm_responses,
                context_used=context,
                metadata={
                    "query_length": len(query),
                    "context_tokens": context.total_tokens,
                    "providers_used": [resp.provider_name for resp in llm_responses]
                },
                execution_time=execution_time
            )
            
            logger.info(f"Query processed successfully in {execution_time:.2f}s")
            return orchestrated_response
            
        except Exception as e:
            logger.error(f"Failed to process query: {e}")
            raise
    
    def _determine_strategy(
        self, 
        query: str, 
        context: OptimizedContext
    ) -> ResponseStrategy:
        """쿼리와 컨텍스트를 분석하여 최적 전략 결정"""
        query_lower = query.lower()
        
        # 복잡한 쿼리는 다중 LLM 사용
        complex_keywords = [
            "complex", "difficult", "multiple", "various", "compare",
            "analyze", "review", "architecture", "design"
        ]
        
        if any(keyword in query_lower for keyword in complex_keywords):
            return ResponseStrategy.MULTI_VOTE
        
        # 중요한 결정이 필요한 경우 합의 기반
        critical_keywords = [
            "security", "performance", "critical", "important", 
            "production", "deploy", "release"
        ]
        
        if any(keyword in query_lower for keyword in critical_keywords):
            return ResponseStrategy.CONSENSUS
        
        # 창의적 작업은 하이브리드
        creative_keywords = [
            "implement", "create", "design", "build", "develop",
            "refactor", "optimize", "improve"
        ]
        
        if any(keyword in query_lower for keyword in creative_keywords):
            return ResponseStrategy.HYBRID
        
        # 기본적으로 단일 최적 LLM 사용
        return ResponseStrategy.SINGLE_BEST
    
    async def _single_best_response(
        self,
        query: str,
        context: OptimizedContext,
        routing_decisions: List[RoutingDecision]
    ) -> List[LLMResponse]:
        """최적 LLM 하나만 사용"""
        if not routing_decisions:
            raise ValueError("No routing decisions available")
        
        best_decision = routing_decisions[0]  # 이미 점수순 정렬됨
        provider = self.providers[best_decision.provider_name]
        
        formatted_context = self.context_builder.format_context_for_llm(context)
        
        response = await provider.generate_response(
            query=query,
            context=formatted_context,
            model=best_decision.model_name,
            parameters=best_decision.parameters
        )
        
        return [response]
    
    async def _multi_vote_response(
        self,
        query: str,
        context: OptimizedContext,
        routing_decisions: List[RoutingDecision]
    ) -> List[LLMResponse]:
        """여러 LLM 결과 수집"""
        formatted_context = self.context_builder.format_context_for_llm(context)
        
        # 상위 3개 LLM 사용
        top_decisions = routing_decisions[:3]
        tasks = []
        
        for decision in top_decisions:
            if decision.provider_name in self.providers:
                provider = self.providers[decision.provider_name]
                task = provider.generate_response(
                    query=query,
                    context=formatted_context,
                    model=decision.model_name,
                    parameters=decision.parameters
                )
                tasks.append(task)
        
        if not tasks:
            raise ValueError("No valid providers available")
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 성공한 응답만 반환
        valid_responses = [
            resp for resp in responses 
            if isinstance(resp, LLMResponse)
        ]
        
        return valid_responses
    
    async def _hybrid_response(
        self,
        query: str,
        context: OptimizedContext,
        routing_decisions: List[RoutingDecision]
    ) -> List[LLMResponse]:
        """하이브리드 접근 (특화된 LLM들 조합)"""
        # 코드 생성과 리뷰를 분리하여 처리
        code_decisions = [
            d for d in routing_decisions 
            if d.task_type in ["code_generation", "implementation"]
        ]
        review_decisions = [
            d for d in routing_decisions 
            if d.task_type in ["code_review", "analysis"]
        ]
        
        formatted_context = self.context_builder.format_context_for_llm(context)
        responses = []
        
        # 코드 생성
        if code_decisions:
            provider = self.providers[code_decisions[0].provider_name]
            code_response = await provider.generate_response(
                query=f"Generate code for: {query}",
                context=formatted_context,
                model=code_decisions[0].model_name,
                parameters=code_decisions[0].parameters
            )
            responses.append(code_response)
        
        # 코드 리뷰 (생성된 코드가 있는 경우)
        if review_decisions and responses:
            provider = self.providers[review_decisions[0].provider_name]
            review_query = f"Review and improve: {query}"
            if responses:
                review_query += f"\n\nGenerated code:\n{responses[0].content}"
            
            review_response = await provider.generate_response(
                query=review_query,
                context=formatted_context,
                model=review_decisions[0].model_name,
                parameters=review_decisions[0].parameters
            )
            responses.append(review_response)
        
        return responses
    
    async def _consensus_response(
        self,
        query: str,
        context: OptimizedContext,
        routing_decisions: List[RoutingDecision]
    ) -> List[LLMResponse]:
        """합의 기반 응답"""
        # 먼저 여러 LLM에서 응답 수집
        multi_responses = await self._multi_vote_response(
            query, context, routing_decisions
        )
        
        if len(multi_responses) < 2:
            return multi_responses
        
        # 합의를 위한 메타 쿼리 생성
        consensus_query = self._create_consensus_query(query, multi_responses)
        formatted_context = self.context_builder.format_context_for_llm(context)
        
        # 최고 성능 LLM으로 합의 생성
        best_decision = routing_decisions[0]
        provider = self.providers[best_decision.provider_name]
        
        consensus_response = await provider.generate_response(
            query=consensus_query,
            context=formatted_context,
            model=best_decision.model_name,
            parameters=best_decision.parameters
        )
        
        # 원본 응답들과 합의 응답 모두 반환
        return multi_responses + [consensus_response]
    
    def _create_consensus_query(
        self, 
        original_query: str, 
        responses: List[LLMResponse]
    ) -> str:
        """합의를 위한 메타 쿼리 생성"""
        consensus_parts = [
            f"Original query: {original_query}",
            "",
            "Multiple AI responses have been generated. Please analyze them and provide a consensus response that:",
            "1. Combines the best aspects of each response",
            "2. Resolves any conflicts or contradictions", 
            "3. Provides the most accurate and helpful answer",
            "",
            "Responses to analyze:"
        ]
        
        for i, response in enumerate(responses, 1):
            consensus_parts.append(f"\n--- Response {i} ({response.provider_name}) ---")
            consensus_parts.append(response.content)
        
        consensus_parts.append("\n--- Consensus Response ---")
        
        return "\n".join(consensus_parts)
    
    def _integrate_responses(
        self,
        responses: List[LLMResponse],
        strategy: ResponseStrategy
    ) -> tuple[str, float]:
        """응답들을 통합하여 최종 결과 생성"""
        if not responses:
            return "No responses generated", 0.0
        
        if len(responses) == 1:
            return responses[0].content, responses[0].confidence
        
        if strategy == ResponseStrategy.SINGLE_BEST:
            best_response = max(responses, key=lambda r: r.confidence)
            return best_response.content, best_response.confidence
        
        elif strategy == ResponseStrategy.MULTI_VOTE:
            # 신뢰도 가중 평균으로 최종 응답 선택
            weighted_responses = [
                (resp.content, resp.confidence) for resp in responses
            ]
            best_content = max(weighted_responses, key=lambda x: x[1])[0]
            avg_confidence = sum(resp.confidence for resp in responses) / len(responses)
            return best_content, avg_confidence
        
        elif strategy == ResponseStrategy.CONSENSUS:
            # 마지막 응답이 합의 응답
            consensus_response = responses[-1]
            return consensus_response.content, consensus_response.confidence
        
        else:  # HYBRID
            # 응답들을 결합
            combined_content = "\n\n".join([
                f"## {resp.provider_name} Response:\n{resp.content}"
                for resp in responses
            ])
            avg_confidence = sum(resp.confidence for resp in responses) / len(responses)
            return combined_content, avg_confidence
    
    def get_provider_stats(self) -> Dict[str, Any]:
        """제공자별 통계 정보"""
        stats = {}
        for name, provider in self.providers.items():
            stats[name] = {
                "available": provider.is_available(),
                "models": provider.get_available_models(),
                "usage": getattr(provider, 'usage_stats', {})
            }
        return stats
