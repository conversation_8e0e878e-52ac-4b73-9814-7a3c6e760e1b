"""
Temporal Analysis Engine

시간적 분석을 수행하는 클래스
"""

import logging
from typing import List, Dict, Any, Optional

from ..persistence.sqlite_manager import SQLiteManager
from ..persistence.git_analyzer import GitAnalyzer

logger = logging.getLogger(__name__)


class TemporalAnalyzer:
    """시간적 분석 엔진"""
    
    def __init__(self, db_manager: SQLiteManager, git_analyzer: GitAnalyzer):
        """
        Args:
            db_manager: SQLite 데이터베이스 관리자
            git_analyzer: Git 분석기
        """
        self.db_manager = db_manager
        self.git_analyzer = git_analyzer
        logger.info("Temporal analyzer initialized")
    
    def analyze_query(self, query: str) -> List[Dict[str, Any]]:
        """
        쿼리를 시간적으로 분석
        
        Args:
            query: 분석할 쿼리
            
        Returns:
            시간적 분석 결과
        """
        logger.debug(f"Performing temporal analysis: '{query}'")
        
        results = []
        query_lower = query.lower()
        
        # 최근 변경 관련 쿼리
        if any(keyword in query_lower for keyword in ["recent", "latest", "new", "changed"]):
            results.extend(self._analyze_recent_changes(query))
        
        # 버그 수정 관련 쿼리
        if any(keyword in query_lower for keyword in ["bug", "fix", "error", "issue"]):
            results.extend(self._analyze_bug_fixes(query))
        
        # 히스토리 관련 쿼리
        if any(keyword in query_lower for keyword in ["history", "evolution", "when"]):
            results.extend(self._analyze_history(query))
        
        logger.debug(f"Found {len(results)} temporal analysis results")
        return results
    
    def _analyze_recent_changes(self, query: str) -> List[Dict[str, Any]]:
        """최근 변경사항 분석"""
        try:
            recent_commits = self.git_analyzer.get_recent_commits(max_count=20)
            
            results = []
            for commit in recent_commits:
                results.append({
                    "file_path": "recent_commit",
                    "content": f"Recent commit: {commit.message}",
                    "score": 0.9,
                    "start_line": 1,
                    "end_line": 1,
                    "context_type": "temporal",
                    "explanation": f"Recent commit by {commit.author_name}",
                    "metadata": {
                        "commit_hash": commit.hash,
                        "author": commit.author_name,
                        "timestamp": commit.timestamp.isoformat()
                    },
                    "related_changes": [{
                        "type": "commit",
                        "hash": commit.hash,
                        "message": commit.message,
                        "author": commit.author_name,
                        "timestamp": commit.timestamp.isoformat()
                    }]
                })
            
            return results[:10]  # 상위 10개만 반환
            
        except Exception as e:
            logger.error(f"Failed to analyze recent changes: {e}")
            return []
    
    def _analyze_bug_fixes(self, query: str) -> List[Dict[str, Any]]:
        """버그 수정 이력 분석"""
        try:
            bug_fix_commits = self.git_analyzer.find_bug_fix_commits()
            
            results = []
            for commit in bug_fix_commits:
                results.append({
                    "file_path": "bug_fix_commit",
                    "content": f"Bug fix: {commit.message}",
                    "score": 0.85,
                    "start_line": 1,
                    "end_line": 1,
                    "context_type": "temporal",
                    "explanation": f"Bug fix commit by {commit.author_name}",
                    "metadata": {
                        "commit_hash": commit.hash,
                        "author": commit.author_name,
                        "timestamp": commit.timestamp.isoformat()
                    },
                    "related_changes": [{
                        "type": "bug_fix",
                        "hash": commit.hash,
                        "message": commit.message,
                        "author": commit.author_name,
                        "timestamp": commit.timestamp.isoformat()
                    }]
                })
            
            return results[:10]
            
        except Exception as e:
            logger.error(f"Failed to analyze bug fixes: {e}")
            return []
    
    def _analyze_history(self, query: str) -> List[Dict[str, Any]]:
        """히스토리 분석"""
        # TODO: 더 정교한 히스토리 분석 구현
        return []
