"""
Semantic Search Engine

의미적 검색을 수행하는 클래스
"""

import logging
from typing import List, Optional

from ..persistence.vector_store import VectorStore
from ..persistence.models import SearchResult
from ..persistence.models import SearchResult

logger = logging.getLogger(__name__)


class SemanticSearch:
    """의미적 검색 엔진"""
    
    def __init__(self, vector_store: VectorStore):
        """
        Args:
            vector_store: 벡터 스토어
        """
        self.vector_store = vector_store
        logger.info("Semantic search engine initialized")
    
    def search(
        self,
        query: str,
        n_results: int = 10,
        context_types: Optional[List[str]] = None,
        file_patterns: Optional[List[str]] = None
    ) -> List[SearchResult]:
        """
        의미적 검색 수행
        
        Args:
            query: 검색 쿼리
            n_results: 반환할 결과 수
            context_types: 컨텍스트 타입 필터
            file_patterns: 파일 패턴 필터
            
        Returns:
            검색 결과 리스트
        """
        logger.debug(f"Performing semantic search: '{query}'")
        
        try:
            # VectorStore의 search_similar 메서드 사용
            vector_results = self.vector_store.search_similar(
                query=query,
                limit=n_results,
                score_threshold=0.5  # 적절한 임계값 설정
            )

            # VectorSearchResult를 SearchResult로 변환
            results = []
            for vector_result in vector_results:
                payload = vector_result.payload

                # 필터링 적용 (context_types, file_patterns)
                if context_types and payload.get("chunk_type") not in context_types:
                    continue

                if file_patterns:
                    file_path = payload.get("file_path", "")
                    if not any(pattern in file_path for pattern in file_patterns):
                        continue

                search_result = SearchResult(
                    file_path=payload.get("file_path", ""),
                    content=payload.get("content", ""),
                    score=vector_result.score,
                    start_line=payload.get("start_line", 1),
                    end_line=payload.get("end_line", 1),
                    context_type=payload.get("chunk_type", "chunk"),
                    explanation=f"Semantic similarity: {vector_result.score:.3f}",
                    metadata={
                        "vector_id": vector_result.id,
                        "language": payload.get("language", ""),
                        "function_name": payload.get("function_name", ""),
                        "class_name": payload.get("class_name", "")
                    }
                )
                results.append(search_result)

            logger.debug(f"Found {len(results)} semantic search results")
            return results

        except Exception as e:
            logger.error(f"Semantic search failed: {e}")
            return []
