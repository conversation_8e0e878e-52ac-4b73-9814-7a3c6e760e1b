"""
Smart Search Engine

의미적 검색, 구조적 분석, 시간적 분석을 통합한 지능형 검색 엔진
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from ..persistence.models import SearchResult
from ..persistence.sqlite_manager import SQLiteManager
from ..persistence.vector_store import VectorStore
from ..persistence.git_analyzer import GitAnalyzer
from .semantic_search import SemanticSearch
from .structural_analysis import StructuralAnalyzer
from .temporal_analysis import TemporalAnalyzer
from .reranker import Reranker

logger = logging.getLogger(__name__)


class SearchMode(str, Enum):
    """검색 모드"""
    SEMANTIC = "semantic"           # 의미적 검색
    STRUCTURAL = "structural"       # 구조적 검색
    TEMPORAL = "temporal"          # 시간적 검색
    HYBRID = "hybrid"              # 하이브리드 검색
    AUTO = "auto"                  # 자동 모드 선택


@dataclass
class SearchQuery:
    """검색 쿼리 정보를 담는 데이터 클래스"""
    text: str
    mode: SearchMode = SearchMode.AUTO
    context_types: Optional[List[str]] = None
    file_patterns: Optional[List[str]] = None
    max_results: int = 10
    include_history: bool = True
    repository_id: Optional[str] = None
    time_weight: float = 0.2
    semantic_weight: float = 0.6
    structural_weight: float = 0.2


@dataclass
class EnhancedSearchResult:
    """향상된 검색 결과"""
    search_result: SearchResult
    relevance_score: float
    temporal_score: float
    structural_score: float
    combined_score: float
    explanation: str
    related_changes: List[Dict[str, Any]]
    rerank_score: Optional[float] = None


class SmartSearch:
    """지능형 검색 엔진"""
    
    def __init__(
        self,
        db_manager: SQLiteManager,
        vector_store: VectorStore,
        reranker: Reranker,
        git_analyzer: Optional[GitAnalyzer] = None
    ):
        """
        Args:
            db_manager: SQLite 데이터베이스 관리자
            vector_store: 벡터 스토어
            git_analyzer: Git 분석기 (선택사항)
        """
        self.db_manager = db_manager
        self.vector_store = vector_store
        self.reranker = reranker
        self.git_analyzer = git_analyzer
        
        # 하위 검색 엔진들 초기화
        self.semantic_search = SemanticSearch(vector_store)
        self.structural_analyzer = StructuralAnalyzer(db_manager)
        
        if git_analyzer:
            self.temporal_analyzer = TemporalAnalyzer(db_manager, git_analyzer)
        else:
            self.temporal_analyzer = None
            logger.warning("Git analyzer not available, temporal analysis disabled")
    
    def search(self, query: SearchQuery) -> List[EnhancedSearchResult]:
        """통합 검색 수행"""
        logger.info(f"Performing smart search: '{query.text}' (mode: {query.mode})")
        
        # 검색 모드 결정
        if query.mode == SearchMode.AUTO:
            search_mode = self._determine_search_mode(query.text)
        else:
            search_mode = query.mode
        
        logger.debug(f"Selected search mode: {search_mode}")
        
        # 모드별 검색 수행
        if search_mode == SearchMode.SEMANTIC:
            results = self._perform_semantic_search(query)
        elif search_mode == SearchMode.STRUCTURAL:
            results = self._structural_search(query)
        elif search_mode == SearchMode.TEMPORAL:
            results = self._temporal_search(query)
        else:  # HYBRID
            results = self._hybrid_search(query)
        
        # 의미적 검색이 포함된 경우 Reranking 수행
        if search_mode in [SearchMode.SEMANTIC, SearchMode.HYBRID, SearchMode.AUTO]:
            # EnhancedSearchResult에서 document 딕셔너리 리스트로 변환
            docs_to_rerank = [
                {
                    'content': r.search_result.content,
                    'file_path': r.search_result.file_path,
                    'start_line': r.search_result.start_line,
                    'end_line': r.search_result.end_line,
                    # 원래 점수와 기타 메타데이터 보존
                    'original_score': r.combined_score,
                    'search_result': r # 원본 EnhancedSearchResult 객체 저장
                }
                for r in results
            ]
            
            reranked_docs = self.reranker.rerank_sync(query.text, docs_to_rerank)
            
            # rerank_score를 기반으로 새로운 EnhancedSearchResult 리스트 생성
            reranked_results = []
            for doc in reranked_docs:
                original_result = doc['search_result']
                original_result.rerank_score = doc.get('rerank_score')
                reranked_results.append(original_result)
            
            # 점수가 높은 순으로 정렬
            results = sorted(reranked_results, key=lambda r: r.rerank_score or -1, reverse=True)

        return results
    
    def _determine_search_mode(self, query_text: str) -> SearchMode:
        """쿼리 텍스트를 분석하여 최적 검색 모드 결정"""
        query_lower = query_text.lower()
        
        # 시간 관련 키워드
        temporal_keywords = [
            "when", "history", "changed", "modified", "recent", "last",
            "commit", "version", "evolution", "bug", "fix", "introduced"
        ]
        
        # 구조 관련 키워드
        structural_keywords = [
            "class", "function", "method", "inherits", "extends", "implements",
            "calls", "uses", "depends", "imports", "structure", "architecture"
        ]
        
        # 키워드 매칭 점수 계산
        temporal_score = sum(1 for kw in temporal_keywords if kw in query_lower)
        structural_score = sum(1 for kw in structural_keywords if kw in query_lower)
        
        # 모드 결정
        if temporal_score > structural_score and temporal_score > 0:
            return SearchMode.TEMPORAL
        elif structural_score > 0:
            return SearchMode.STRUCTURAL
        else:
            return SearchMode.HYBRID
    
    def _perform_semantic_search(self, query: SearchQuery) -> List[EnhancedSearchResult]:
        """의미적 검색"""
        # TODO: repository_id를 사용한 컬렉션 필터링은 VectorStore 레벨에서 처리해야 함

        results = self.semantic_search.search(
            query.text,
            n_results=query.max_results,
            context_types=query.context_types,
            file_patterns=query.file_patterns
        )
        
        enhanced_results = []
        for result in results:
            enhanced_result = EnhancedSearchResult(
                search_result=result,
                relevance_score=result.score,
                temporal_score=0.0,
                structural_score=0.0,
                combined_score=result.score,
                explanation="Semantic similarity match",
                related_changes=[]
            )
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
    
    def _structural_search(self, query: SearchQuery) -> List[EnhancedSearchResult]:
        """구조적 검색"""
        # 구조적 분석 수행
        structural_results = self.structural_analyzer.analyze_query(query.text)
        
        enhanced_results = []
        for result in structural_results:
            # 구조적 결과를 SearchResult로 변환
            search_result = SearchResult(
                file_path=result.get("file_path", ""),
                content=result.get("content", ""),
                score=result.get("score", 0.0),
                start_line=result.get("start_line", 1),
                end_line=result.get("end_line", 1),
                context_type=result.get("context_type", "structural"),
                metadata=result.get("metadata", {})
            )
            
            enhanced_result = EnhancedSearchResult(
                search_result=search_result,
                relevance_score=0.0,
                temporal_score=0.0,
                structural_score=result.get("score", 0.0),
                combined_score=result.get("score", 0.0),
                explanation=result.get("explanation", "Structural match"),
                related_changes=[]
            )
            enhanced_results.append(enhanced_result)
        
        return enhanced_results[:query.max_results]
    
    def _temporal_search(self, query: SearchQuery) -> List[EnhancedSearchResult]:
        """시간적 검색"""
        if not self.temporal_analyzer:
            logger.warning("Temporal analysis not available")
            return self._perform_semantic_search(query)
        
        # 시간적 분석 수행
        temporal_results = self.temporal_analyzer.analyze_query(query.text)
        
        enhanced_results = []
        for result in temporal_results:
            # 시간적 결과를 SearchResult로 변환
            search_result = SearchResult(
                file_path=result.get("file_path", ""),
                content=result.get("content", ""),
                score=result.get("score", 0.0),
                start_line=result.get("start_line", 1),
                end_line=result.get("end_line", 1),
                context_type=result.get("context_type", "temporal"),
                metadata=result.get("metadata", {})
            )
            
            enhanced_result = EnhancedSearchResult(
                search_result=search_result,
                relevance_score=0.0,
                temporal_score=result.get("score", 0.0),
                structural_score=0.0,
                combined_score=result.get("score", 0.0),
                explanation=result.get("explanation", "Temporal relevance"),
                related_changes=result.get("related_changes", [])
            )
            enhanced_results.append(enhanced_result)
        
        return enhanced_results[:query.max_results]
    
    def _hybrid_search(self, query: SearchQuery) -> List[EnhancedSearchResult]:
        """하이브리드 검색 (의미적 + 구조적 + 시간적)"""
        # 각 검색 방법으로 결과 수집
        semantic_results = self._perform_semantic_search(query)
        
        structural_results = self.structural_analyzer.analyze_query(query.text)
        
        temporal_results = []
        if self.temporal_analyzer and query.include_history:
            temporal_results = self.temporal_analyzer.analyze_query(query.text)
        
        # 결과 통합 및 점수 계산
        combined_results = self._combine_search_results(
            semantic_results,
            structural_results,
            temporal_results,
            query
        )
        
        # 점수순 정렬 및 상위 결과 반환
        combined_results.sort(key=lambda x: x.combined_score, reverse=True)
        return combined_results[:query.max_results]
    
    def _combine_search_results(
        self,
        semantic_results: List[SearchResult],
        structural_results: List[Dict[str, Any]],
        temporal_results: List[Dict[str, Any]],
        query: SearchQuery
    ) -> List[EnhancedSearchResult]:
        """검색 결과들을 통합하고 점수 계산"""
        # 파일 경로별로 결과 그룹화
        file_results = {}
        
        # 의미적 검색 결과 처리
        for result in semantic_results:
            key = f"{result.file_path}:{result.start_line}:{result.end_line}"
            if key not in file_results:
                file_results[key] = {
                    "search_result": result,
                    "semantic_score": result.score,
                    "structural_score": 0.0,
                    "temporal_score": 0.0,
                    "related_changes": []
                }
        
        # 구조적 검색 결과 처리
        for result in structural_results:
            file_path = result.get("file_path", "")
            start_line = result.get("start_line", 1)
            end_line = result.get("end_line", 1)
            key = f"{file_path}:{start_line}:{end_line}"
            
            if key in file_results:
                file_results[key]["structural_score"] = result.get("score", 0.0)
            else:
                # 새로운 결과 생성
                search_result = SearchResult(
                    file_path=file_path,
                    content=result.get("content", ""),
                    score=result.get("score", 0.0),
                    start_line=start_line,
                    end_line=end_line,
                    context_type=result.get("context_type", "structural"),
                    metadata=result.get("metadata", {})
                )
                file_results[key] = {
                    "search_result": search_result,
                    "semantic_score": 0.0,
                    "structural_score": result.get("score", 0.0),
                    "temporal_score": 0.0,
                    "related_changes": []
                }
        
        # 시간적 검색 결과 처리
        for result in temporal_results:
            file_path = result.get("file_path", "")
            start_line = result.get("start_line", 1)
            end_line = result.get("end_line", 1)
            key = f"{file_path}:{start_line}:{end_line}"
            
            if key in file_results:
                file_results[key]["temporal_score"] = result.get("score", 0.0)
                file_results[key]["related_changes"] = result.get("related_changes", [])
        
        # 최종 점수 계산 및 EnhancedSearchResult 생성
        enhanced_results = []
        for key, data in file_results.items():
            # 가중 평균으로 최종 점수 계산
            combined_score = (
                data["semantic_score"] * query.semantic_weight +
                data["structural_score"] * query.structural_weight +
                data["temporal_score"] * query.time_weight
            )
            
            # Rerank 점수가 있으면 가중치 부여
            rerank_score = data.get("rerank_score")
            if rerank_score is not None:
                # 예: rerank 점수를 50% 가중치로 추가
                combined_score = (combined_score * 0.5) + (rerank_score * 0.5)
            
            # 설명 생성
            explanation_parts = []
            if data["semantic_score"] > 0:
                explanation_parts.append(f"Semantic: {data['semantic_score']:.2f}")
            if data["structural_score"] > 0:
                explanation_parts.append(f"Structural: {data['structural_score']:.2f}")
            if data["temporal_score"] > 0:
                explanation_parts.append(f"Temporal: {data['temporal_score']:.2f}")
            
            explanation = "Combined score: " + ", ".join(explanation_parts)
            
            enhanced_result = EnhancedSearchResult(
                search_result=data["search_result"],
                relevance_score=data["semantic_score"],
                temporal_score=data["temporal_score"],
                structural_score=data["structural_score"],
                combined_score=combined_score,
                explanation=explanation,
                related_changes=data["related_changes"],
                rerank_score=rerank_score
            )
            enhanced_results.append(enhanced_result)
        
        return enhanced_results
    
    def suggest_related_queries(self, query: str) -> List[str]:
        """관련 쿼리 제안"""
        suggestions = []
        
        # 기본 제안들
        base_suggestions = [
            f"How to use {query}",
            f"Examples of {query}",
            f"Tests for {query}",
            f"Documentation for {query}",
            f"History of {query}",
            f"Dependencies of {query}"
        ]
        
        # 의미적 유사성 기반 제안
        try:
            semantic_results = self.semantic_search.search(query, n_results=5)
            for result in semantic_results:
                if "function_name" in result.metadata:
                    suggestions.append(f"Function: {result.metadata['function_name']}")
                elif "class_name" in result.metadata:
                    suggestions.append(f"Class: {result.metadata['class_name']}")
        except Exception as e:
            logger.error(f"Failed to generate semantic suggestions: {e}")
        
        # 중복 제거 및 정리
        unique_suggestions = list(set(suggestions + base_suggestions))
        return unique_suggestions[:10]
