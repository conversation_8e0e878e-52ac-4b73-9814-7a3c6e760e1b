import httpx
import json
from typing import List, Dict, Any, Optional

from ...utils.config import RerankerConfig
from ...utils.logger import get_logger

logger = get_logger(__name__)

class Reranker:
    def __init__(self, config: RerankerConfig):
        self.config = config

    def rerank_sync(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        if not self.config.enabled or not documents:
            if not self.config.enabled:
                logger.info("<PERSON>rank<PERSON> is disabled. Skipping.")
            return documents[:self.config.top_n]

        logger.info(f"Reranking {len(documents)} documents for query: '{query}' using model {self.config.model}")

        try:
            pairs = [[query, doc.get('content', '')] for doc in documents]
            
            with httpx.Client(timeout=60.0) as client:
                scores = self._call_rerank_api_sync(client, pairs)

            if not scores or len(scores) != len(documents):
                logger.warning("<PERSON>rank<PERSON> did not return valid scores. Returning original order.")
                return documents[:self.config.top_n]

            for doc, score in zip(documents, scores):
                doc['rerank_score'] = score

            reranked_docs = sorted(documents, key=lambda x: x['rerank_score'], reverse=True)

            logger.info(f"Reranking complete. Top score: {reranked_docs[0]['rerank_score']:.4f}")
            return reranked_docs[:self.config.top_n]

        except Exception as e:
            logger.error(f"Error during reranking: {e}", exc_info=True)
            return documents[:self.config.top_n]

    def _call_rerank_api_sync(self, client: httpx.Client, pairs: List[List[str]]) -> Optional[List[float]]:
        payload = {
            "model": self.config.model,
            "prompt": json.dumps({"pairs": pairs}),
            "stream": False,
            "raw": True
        }
        try:
            response = client.post(self.config.api_base, json=payload)
            response.raise_for_status()
            response_data = response.json()
            content = response_data.get('response', '[]')
            scores = json.loads(content)
            
            if isinstance(scores, list) and all(isinstance(s, (int, float)) for s in scores):
                return scores
            else:
                logger.error(f"Unexpected format from reranker API: {scores}")
                return None
        except httpx.RequestError as e:
            logger.error(f"HTTP request to reranker failed: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from reranker: {e} - Response: {response.text}")
        except Exception as e:
            logger.error(f"An unexpected error occurred when calling reranker API: {e}")
        return None

    async def rerank(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        client = httpx.AsyncClient(timeout=60.0)
        if not self.config.enabled or not documents:
            if not self.config.enabled:
                logger.info("Reranker is disabled. Skipping.")
            return documents[:self.config.top_n]

        logger.info(f"Reranking {len(documents)} documents for query: '{query}' using model {self.config.model}")

        try:
            # Prepare pairs of [query, document_content]
            pairs = [[query, doc.get('content', '')] for doc in documents]

            # Call the rerank API
            async with self.client as client:
                scores = await self._call_rerank_api(client, pairs)

            if not scores or len(scores) != len(documents):
                logger.warning("Reranker did not return valid scores. Returning original order.")
                return documents[:self.config.top_n]

            for doc, score in zip(documents, scores):
                doc['rerank_score'] = score

            reranked_docs = sorted(documents, key=lambda x: x['rerank_score'], reverse=True)

            logger.info(f"Reranking complete. Top score: {reranked_docs[0]['rerank_score']:.4f}")
            return reranked_docs[:self.config.top_n]

        except Exception as e:
            logger.error(f"Error during reranking: {e}", exc_info=True)
            return documents[:self.config.top_n]
        finally:
            if 'client' in locals() and not client.is_closed():
                await client.aclose()

    async def _call_rerank_api(self, pairs: List[List[str]]) -> Optional[List[float]]:
        """
        Calls the Ollama API to get reranking scores for query-document pairs.
        The Qwen-Reranker model expects a list of [query, passage] pairs and returns scores.
        """
        payload = {
            "model": self.config.model,
            "prompt": json.dumps({"pairs": pairs}), # The prompt format might need adjustment
            "stream": False,
            "raw": True # Use raw mode to control the prompt for the reranker
        }

        try:
            response = await self.client.post(self.config.api_base, json=payload)
            response.raise_for_status()
            
            response_data = response.json()
            # The actual output format from Ollama for a reranker needs to be parsed correctly.
            # Assuming the 'response' field contains a JSON string of a list of scores.
            content = response_data.get('response', '[]')
            scores = json.loads(content)
            
            if isinstance(scores, list) and all(isinstance(s, (int, float)) for s in scores):
                return scores
            else:
                logger.error(f"Unexpected format from reranker API: {scores}")
                return None

        except httpx.RequestError as e:
            logger.error(f"HTTP request to reranker failed: {e}")
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON response from reranker: {e} - Response: {response.text}")
        except Exception as e:
            logger.error(f"An unexpected error occurred when calling reranker API: {e}")
        
        return None

    async def close(self):
        await self.client.aclose()
