"""
SQLite Database Manager

코드베이스 메타데이터를 SQLite에 저장하고 관리하는 클래스
"""

import logging
import sqlite3
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from contextlib import contextmanager
from datetime import datetime

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError

from .models import Base, CodeFile, CodeFunction, CodeClass, GitCommit, AnalysisStats

logger = logging.getLogger(__name__)


class SQLiteManager:
    """SQLite 데이터베이스 관리자"""
    
    def __init__(self, db_path: str):
        """
        Args:
            db_path: SQLite 데이터베이스 파일 경로
        """
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # SQLAlchemy 엔진 생성
        self.engine = create_engine(
            f"sqlite:///{self.db_path}",
            echo=False,  # SQL 로그 출력 여부
            pool_pre_ping=True,
            connect_args={"check_same_thread": False}
        )
        
        # 세션 팩토리 생성
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # 테이블 생성
        self._create_tables()
        logger.info(f"SQLite database initialized: {self.db_path}")
    
    def _create_tables(self):
        """데이터베이스 테이블 생성"""
        try:
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
        except Exception as e:
            logger.error(f"Failed to create database tables: {e}")
            raise
    
    @contextmanager
    def get_session(self):
        """데이터베이스 세션 컨텍스트 매니저"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    # Code File Operations
    def create_code_file(self, file_data: Dict[str, Any]) -> Optional[CodeFile]:
        """코드 파일 생성"""
        try:
            with self.get_session() as session:
                # 기존 파일 확인
                existing_file = session.query(CodeFile).filter(
                    CodeFile.path == file_data["path"]
                ).first()
                
                if existing_file:
                    # 기존 파일 업데이트
                    for key, value in file_data.items():
                        if hasattr(existing_file, key):
                            setattr(existing_file, key, value)
                    existing_file.updated_at = datetime.utcnow()
                    session.flush()
                    logger.debug(f"Updated existing file: {file_data['path']}")
                    return existing_file
                else:
                    # 새 파일 생성
                    code_file = CodeFile(**file_data)
                    session.add(code_file)
                    session.flush()
                    logger.debug(f"Created new file: {file_data['path']}")
                    return code_file
                    
        except SQLAlchemyError as e:
            logger.error(f"Failed to create/update code file: {e}")
            return None
    
    def get_code_file(self, file_path: str) -> Optional[CodeFile]:
        """파일 경로로 코드 파일 조회"""
        try:
            with self.get_session() as session:
                return session.query(CodeFile).filter(
                    CodeFile.path == file_path
                ).first()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get code file: {e}")
            return None
    
    def get_code_files(self, language: Optional[str] = None, limit: int = 100) -> List[CodeFile]:
        """코드 파일 목록 조회"""
        try:
            with self.get_session() as session:
                query = session.query(CodeFile)
                
                if language:
                    query = query.filter(CodeFile.language == language)
                
                return query.limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get code files: {e}")
            return []
    
    def delete_code_file(self, file_path: str) -> bool:
        """코드 파일 삭제"""
        try:
            with self.get_session() as session:
                file_obj = session.query(CodeFile).filter(
                    CodeFile.path == file_path
                ).first()
                
                if file_obj:
                    session.delete(file_obj)
                    logger.debug(f"Deleted file: {file_path}")
                    return True
                return False
        except SQLAlchemyError as e:
            logger.error(f"Failed to delete code file: {e}")
            return False
    
    # Code Function Operations
    def create_code_function(self, function_data: Dict[str, Any]) -> Optional[CodeFunction]:
        """함수 정보 생성"""
        try:
            with self.get_session() as session:
                code_function = CodeFunction(**function_data)
                session.add(code_function)
                session.flush()
                logger.debug(f"Created function: {function_data['name']}")
                return code_function
        except SQLAlchemyError as e:
            logger.error(f"Failed to create code function: {e}")
            return None
    
    def get_functions_by_file(self, file_id: int) -> List[CodeFunction]:
        """파일별 함수 목록 조회"""
        try:
            with self.get_session() as session:
                return session.query(CodeFunction).filter(
                    CodeFunction.file_id == file_id
                ).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get functions by file: {e}")
            return []
    
    def search_functions(self, name_pattern: str, limit: int = 50) -> List[CodeFunction]:
        """함수명으로 검색"""
        try:
            with self.get_session() as session:
                return session.query(CodeFunction).filter(
                    CodeFunction.name.like(f"%{name_pattern}%")
                ).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to search functions: {e}")
            return []
    
    # Code Class Operations
    def create_code_class(self, class_data: Dict[str, Any]) -> Optional[CodeClass]:
        """클래스 정보 생성"""
        try:
            with self.get_session() as session:
                code_class = CodeClass(**class_data)
                session.add(code_class)
                session.flush()
                logger.debug(f"Created class: {class_data['name']}")
                return code_class
        except SQLAlchemyError as e:
            logger.error(f"Failed to create code class: {e}")
            return None
    
    def get_classes_by_file(self, file_id: int) -> List[CodeClass]:
        """파일별 클래스 목록 조회"""
        try:
            with self.get_session() as session:
                return session.query(CodeClass).filter(
                    CodeClass.file_id == file_id
                ).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get classes by file: {e}")
            return []
    
    def search_classes(self, name_pattern: str, limit: int = 50) -> List[CodeClass]:
        """클래스명으로 검색"""
        try:
            with self.get_session() as session:
                return session.query(CodeClass).filter(
                    CodeClass.name.like(f"%{name_pattern}%")
                ).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to search classes: {e}")
            return []
    
    # Git Commit Operations
    def create_git_commit(self, commit_data: Dict[str, Any]) -> Optional[GitCommit]:
        """Git 커밋 정보 생성"""
        try:
            with self.get_session() as session:
                # 기존 커밋 확인
                existing_commit = session.query(GitCommit).filter(
                    GitCommit.hash == commit_data["hash"]
                ).first()
                
                if existing_commit:
                    logger.debug(f"Commit already exists: {commit_data['hash']}")
                    return existing_commit
                
                git_commit = GitCommit(**commit_data)
                session.add(git_commit)
                session.flush()
                logger.debug(f"Created commit: {commit_data['hash']}")
                return git_commit
        except SQLAlchemyError as e:
            logger.error(f"Failed to create git commit: {e}")
            return None
    
    def get_recent_commits(self, limit: int = 20) -> List[GitCommit]:
        """최근 커밋 목록 조회"""
        try:
            with self.get_session() as session:
                return session.query(GitCommit).order_by(
                    GitCommit.timestamp.desc()
                ).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get recent commits: {e}")
            return []
    
    def get_bug_fix_commits(self, limit: int = 50) -> List[GitCommit]:
        """버그 수정 커밋 목록 조회"""
        try:
            with self.get_session() as session:
                return session.query(GitCommit).filter(
                    GitCommit.is_bug_fix == True
                ).order_by(GitCommit.timestamp.desc()).limit(limit).all()
        except SQLAlchemyError as e:
            logger.error(f"Failed to get bug fix commits: {e}")
            return []
    
    # Statistics and Analysis
    def get_analysis_stats(self) -> AnalysisStats:
        """분석 통계 조회"""
        try:
            with self.get_session() as session:
                total_files = session.query(CodeFile).count()
                total_functions = session.query(CodeFunction).count()
                total_classes = session.query(CodeClass).count()
                total_commits = session.query(GitCommit).count()
                
                # 언어별 파일 수
                language_stats = session.execute(
                    text("SELECT language, COUNT(*) FROM code_files GROUP BY language")
                ).fetchall()
                languages = {row[0]: row[1] for row in language_stats}
                
                # 파일 크기 통계
                size_stats = session.execute(
                    text("""
                        SELECT 
                            CASE 
                                WHEN size_bytes < 1024 THEN 'small'
                                WHEN size_bytes < 10240 THEN 'medium'
                                ELSE 'large'
                            END as size_category,
                            COUNT(*) 
                        FROM code_files 
                        GROUP BY size_category
                    """)
                ).fetchall()
                file_sizes = {row[0]: row[1] for row in size_stats}
                
                # 마지막 분석 시간
                last_file = session.query(CodeFile).order_by(
                    CodeFile.updated_at.desc()
                ).first()
                last_analysis = last_file.updated_at if last_file else None
                
                return AnalysisStats(
                    total_files=total_files,
                    total_functions=total_functions,
                    total_classes=total_classes,
                    total_commits=total_commits,
                    languages=languages,
                    file_sizes=file_sizes,
                    last_analysis=last_analysis
                )
        except SQLAlchemyError as e:
            logger.error(f"Failed to get analysis stats: {e}")
            return AnalysisStats(
                total_files=0,
                total_functions=0,
                total_classes=0,
                total_commits=0,
                languages={},
                file_sizes={},
                last_analysis=None
            )
    
    def clear_all_data(self):
        """모든 데이터 삭제 (개발/테스트용)"""
        try:
            with self.get_session() as session:
                session.query(GitCommit).delete()
                session.query(CodeFunction).delete()
                session.query(CodeClass).delete()
                session.query(CodeFile).delete()
                logger.info("All data cleared from database")
        except SQLAlchemyError as e:
            logger.error(f"Failed to clear all data: {e}")
            raise
    
    def close(self):
        """데이터베이스 연결 종료"""
        if hasattr(self, 'engine'):
            self.engine.dispose()
            logger.info("Database connection closed")
