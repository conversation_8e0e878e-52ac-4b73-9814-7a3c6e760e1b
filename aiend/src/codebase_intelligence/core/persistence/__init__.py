"""
Data Layer - 데이터 관리 및 분석

이 모듈은 다음 기능들을 제공합니다:
- SQLite 데이터베이스 관리
- Vector Store (Qdrant) 연동
- Git 히스토리 분석
- 코드베이스 전체 분석
"""

from .models import (
    CodeFile,
    CodeFunction,
    CodeClass,
    GitCommit,
    SearchResult,
    CodeFileCreate,
    CodeFunctionCreate,
    CodeClassCreate,
    GitCommitCreate,
    SearchRequest,
    AnalysisStats
)

from .sqlite_manager import SQLiteManager
from .vector_store import VectorStore
from .git_analyzer import GitAnalyzer
from .tree_sitter_parser import TreeSitterParser
from .codebase_analyzer import CodebaseAnalyzer

__all__ = [
    # Models
    "CodeFile",
    "CodeFunction", 
    "CodeClass",
    "GitCommit",
    "SearchResult",
    "CodeFileCreate",
    "CodeFunctionCreate",
    "CodeClassCreate", 
    "GitCommitCreate",
    "SearchRequest",
    "AnalysisStats",
    
    # Core Classes
    "SQLiteManager",
    "VectorStore",
    "GitAnalyzer",
    "TreeSitterParser",
    "CodebaseAnalyzer",
]
