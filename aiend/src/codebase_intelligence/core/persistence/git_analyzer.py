"""
Git Repository Analyzer

Git 히스토리 분석 및 커밋 정보 추출
"""

import logging
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

try:
    import git
    from git import Repo, Commit
    GIT_AVAILABLE = True
except ImportError:
    GIT_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("GitPython not available. Install with: pip install GitPython")

from .models import GitCommit

logger = logging.getLogger(__name__)


@dataclass
class CommitInfo:
    """커밋 정보"""
    hash: str
    message: str
    author_name: str
    author_email: str
    timestamp: datetime
    changed_files: List[str]
    insertions: int
    deletions: int
    is_merge: bool
    is_bug_fix: bool


class GitAnalyzer:
    """Git 히스토리 분석기"""
    
    def __init__(self, repo_path: str):
        """
        Args:
            repo_path: Git 저장소 경로
        """
        self.repo_path = Path(repo_path)
        self.repo = None
        
        if not GIT_AVAILABLE:
            logger.warning("GitPython not available")
            return
        
        try:
            if self._is_git_repository():
                self.repo = Repo(self.repo_path)
                logger.info(f"Git repository initialized: {self.repo_path}")
            else:
                logger.warning(f"Not a git repository: {self.repo_path}")
        except Exception as e:
            logger.error(f"Failed to initialize git repository: {e}")
            self.repo = None
    
    def _is_git_repository(self) -> bool:
        """Git 저장소 여부 확인"""
        git_dir = self.repo_path / ".git"
        return git_dir.exists() and (git_dir.is_dir() or git_dir.is_file())
    
    def is_available(self) -> bool:
        """Git 분석기 사용 가능 여부"""
        return GIT_AVAILABLE and self.repo is not None
    
    def get_recent_commits(self, max_count: int = 50, since_days: int = 30) -> List[CommitInfo]:
        """최근 커밋 목록 조회"""
        if not self.is_available():
            logger.warning("Git analyzer not available")
            return []
        
        try:
            since_date = datetime.now() - timedelta(days=since_days)
            commits = []
            
            for commit in self.repo.iter_commits(max_count=max_count, since=since_date):
                commit_info = self._extract_commit_info(commit)
                if commit_info:
                    commits.append(commit_info)
            
            logger.debug(f"Retrieved {len(commits)} recent commits")
            return commits
            
        except Exception as e:
            logger.error(f"Failed to get recent commits: {e}")
            return []
    
    def get_all_commits(self, max_count: int = 1000) -> List[CommitInfo]:
        """모든 커밋 목록 조회"""
        if not self.is_available():
            logger.warning("Git analyzer not available")
            return []
        
        try:
            commits = []
            
            for commit in self.repo.iter_commits(max_count=max_count):
                commit_info = self._extract_commit_info(commit)
                if commit_info:
                    commits.append(commit_info)
            
            logger.debug(f"Retrieved {len(commits)} total commits")
            return commits
            
        except Exception as e:
            logger.error(f"Failed to get all commits: {e}")
            return []
    
    def find_bug_fix_commits(self, max_count: int = 200) -> List[CommitInfo]:
        """버그 수정 커밋 찾기"""
        if not self.is_available():
            logger.warning("Git analyzer not available")
            return []
        
        bug_fix_patterns = [
            r'\bfix\b', r'\bbug\b', r'\berror\b', r'\bissue\b',
            r'\bpatch\b', r'\bhotfix\b', r'\brepair\b', r'\bcorrect\b',
            r'\bresolve\b', r'\bsolve\b', r'\bdebug\b'
        ]
        
        try:
            bug_fix_commits = []
            
            for commit in self.repo.iter_commits(max_count=max_count):
                message_lower = commit.message.lower()
                
                # 버그 수정 패턴 매칭
                is_bug_fix = any(
                    re.search(pattern, message_lower) 
                    for pattern in bug_fix_patterns
                )
                
                if is_bug_fix:
                    commit_info = self._extract_commit_info(commit)
                    if commit_info:
                        commit_info.is_bug_fix = True
                        bug_fix_commits.append(commit_info)
            
            logger.debug(f"Found {len(bug_fix_commits)} bug fix commits")
            return bug_fix_commits
            
        except Exception as e:
            logger.error(f"Failed to find bug fix commits: {e}")
            return []
    
    def get_file_history(self, file_path: str, max_count: int = 50) -> List[CommitInfo]:
        """특정 파일의 변경 이력 조회"""
        if not self.is_available():
            logger.warning("Git analyzer not available")
            return []
        
        try:
            commits = []
            
            for commit in self.repo.iter_commits(paths=file_path, max_count=max_count):
                commit_info = self._extract_commit_info(commit)
                if commit_info:
                    commits.append(commit_info)
            
            logger.debug(f"Retrieved {len(commits)} commits for file: {file_path}")
            return commits
            
        except Exception as e:
            logger.error(f"Failed to get file history: {e}")
            return []
    
    def get_changed_files_since(self, since_commit: str) -> Set[str]:
        """특정 커밋 이후 변경된 파일 목록"""
        if not self.is_available():
            logger.warning("Git analyzer not available")
            return set()
        
        try:
            changed_files = set()
            
            # HEAD와 since_commit 사이의 차이점 조회
            diff = self.repo.git.diff('--name-only', since_commit, 'HEAD')
            
            if diff:
                changed_files.update(diff.strip().split('\n'))
            
            logger.debug(f"Found {len(changed_files)} changed files since {since_commit}")
            return changed_files
            
        except Exception as e:
            logger.error(f"Failed to get changed files: {e}")
            return set()
    
    def get_current_branch(self) -> Optional[str]:
        """현재 브랜치 이름 조회"""
        if not self.is_available():
            return None
        
        try:
            return self.repo.active_branch.name
        except Exception as e:
            logger.error(f"Failed to get current branch: {e}")
            return None
    
    def get_latest_commit_hash(self) -> Optional[str]:
        """최신 커밋 해시 조회"""
        if not self.is_available():
            return None
        
        try:
            return self.repo.head.commit.hexsha
        except Exception as e:
            logger.error(f"Failed to get latest commit hash: {e}")
            return None
    
    def _extract_commit_info(self, commit: Commit) -> Optional[CommitInfo]:
        """커밋에서 정보 추출"""
        try:
            # 변경된 파일 목록 추출
            changed_files = []
            insertions = 0
            deletions = 0
            
            try:
                # 커밋의 통계 정보 조회
                stats = commit.stats
                changed_files = list(stats.files.keys())
                insertions = stats.total['insertions']
                deletions = stats.total['deletions']
            except Exception as e:
                logger.debug(f"Failed to get commit stats: {e}")
            
            # 머지 커밋 여부 확인
            is_merge = len(commit.parents) > 1
            
            # 버그 수정 커밋 여부 확인 (기본값은 False, 별도 메서드에서 설정)
            is_bug_fix = False
            
            return CommitInfo(
                hash=commit.hexsha,
                message=commit.message.strip(),
                author_name=commit.author.name,
                author_email=commit.author.email,
                timestamp=datetime.fromtimestamp(commit.committed_date),
                changed_files=changed_files,
                insertions=insertions,
                deletions=deletions,
                is_merge=is_merge,
                is_bug_fix=is_bug_fix
            )
            
        except Exception as e:
            logger.error(f"Failed to extract commit info: {e}")
            return None
    
    def get_author_statistics(self, max_count: int = 500) -> Dict[str, Dict[str, Any]]:
        """작성자별 통계"""
        if not self.is_available():
            logger.warning("Git analyzer not available")
            return {}
        
        try:
            author_stats = {}
            
            for commit in self.repo.iter_commits(max_count=max_count):
                author = commit.author.name
                
                if author not in author_stats:
                    author_stats[author] = {
                        'commit_count': 0,
                        'insertions': 0,
                        'deletions': 0,
                        'files_changed': set(),
                        'first_commit': commit.committed_date,
                        'last_commit': commit.committed_date
                    }
                
                stats = author_stats[author]
                stats['commit_count'] += 1
                
                try:
                    commit_stats = commit.stats
                    stats['insertions'] += commit_stats.total['insertions']
                    stats['deletions'] += commit_stats.total['deletions']
                    stats['files_changed'].update(commit_stats.files.keys())
                except Exception:
                    pass
                
                # 날짜 업데이트
                commit_date = commit.committed_date
                if commit_date < stats['first_commit']:
                    stats['first_commit'] = commit_date
                if commit_date > stats['last_commit']:
                    stats['last_commit'] = commit_date
            
            # Set을 리스트로 변환하고 날짜를 datetime으로 변환
            for author, stats in author_stats.items():
                stats['files_changed'] = len(stats['files_changed'])
                stats['first_commit'] = datetime.fromtimestamp(stats['first_commit'])
                stats['last_commit'] = datetime.fromtimestamp(stats['last_commit'])
            
            logger.debug(f"Generated statistics for {len(author_stats)} authors")
            return author_stats
            
        except Exception as e:
            logger.error(f"Failed to get author statistics: {e}")
            return {}
    
    def get_repository_info(self) -> Dict[str, Any]:
        """저장소 정보 조회"""
        if not self.is_available():
            return {}
        
        try:
            info = {
                'path': str(self.repo_path),
                'current_branch': self.get_current_branch(),
                'latest_commit': self.get_latest_commit_hash(),
                'is_dirty': self.repo.is_dirty(),
                'remote_urls': []
            }
            
            # 원격 저장소 URL 조회
            try:
                for remote in self.repo.remotes:
                    info['remote_urls'].append({
                        'name': remote.name,
                        'url': list(remote.urls)[0] if remote.urls else None
                    })
            except Exception as e:
                logger.debug(f"Failed to get remote URLs: {e}")
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get repository info: {e}")
            return {}
