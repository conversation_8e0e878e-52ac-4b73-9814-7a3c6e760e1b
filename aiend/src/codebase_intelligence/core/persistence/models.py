"""
Data Models for CodeBase Intelligence System

SQLAlchemy ORM 모델과 Pydantic 스키마 정의
"""

import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from pydantic import BaseModel, Field

Base = declarative_base()


# SQLAlchemy ORM Models
class CodeFile(Base):
    """코드 파일 정보"""
    __tablename__ = "code_files"

    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(String, nullable=False, index=True)
    branch = Column(String, nullable=False, index=True, default="main")
    path = Column(String, nullable=False, index=True)
    name = Column(String, nullable=False)
    language = Column(String, nullable=False)
    content = Column(Text)
    size_bytes = Column(Integer, default=0)
    lines_count = Column(Integer, default=0)
    encoding = Column(String, default="utf-8")
    sha256_hash = Column(String, index=True)
    last_modified_at = Column(DateTime, default=datetime.datetime.utcnow)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    functions = relationship("CodeFunction", back_populates="file", cascade="all, delete-orphan")
    classes = relationship("CodeClass", back_populates="file", cascade="all, delete-orphan")


class CodeFunction(Base):
    """함수 정보"""
    __tablename__ = "code_functions"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("code_files.id"), nullable=False)
    name = Column(String, nullable=False, index=True)
    signature = Column(Text)
    docstring = Column(Text)
    start_line = Column(Integer, nullable=False)
    end_line = Column(Integer, nullable=False)
    complexity = Column(Integer, default=1)
    parameters = Column(JSON)  # List of parameter info
    return_type = Column(String)
    is_async = Column(Boolean, default=False)
    is_method = Column(Boolean, default=False)
    class_name = Column(String)  # If it's a method
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    file = relationship("CodeFile", back_populates="functions")


class CodeClass(Base):
    """클래스 정보"""
    __tablename__ = "code_classes"
    
    id = Column(Integer, primary_key=True, index=True)
    file_id = Column(Integer, ForeignKey("code_files.id"), nullable=False)
    name = Column(String, nullable=False, index=True)
    docstring = Column(Text)
    start_line = Column(Integer, nullable=False)
    end_line = Column(Integer, nullable=False)
    base_classes = Column(JSON)  # List of base class names
    methods = Column(JSON)  # List of method names
    attributes = Column(JSON)  # List of attribute names
    is_abstract = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    file = relationship("CodeFile", back_populates="classes")


class GitCommit(Base):
    """Git 커밋 정보"""
    __tablename__ = "git_commits"

    id = Column(Integer, primary_key=True, index=True)
    repository_id = Column(String, nullable=False, index=True)
    branch = Column(String, nullable=False, index=True, default="main")
    hash = Column(String, index=True, nullable=False)
    message = Column(Text, nullable=False)
    author_name = Column(String, nullable=False)
    author_email = Column(String, nullable=False)
    timestamp = Column(DateTime, nullable=False)
    changed_files = Column(JSON)  # List of changed file paths
    insertions = Column(Integer, default=0)
    deletions = Column(Integer, default=0)
    is_merge = Column(Boolean, default=False)
    is_bug_fix = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.datetime.utcnow)


# Pydantic Schemas for API
class CodeFileCreate(BaseModel):
    """코드 파일 생성 스키마"""
    path: str = Field(..., description="파일 경로")
    name: str = Field(..., description="파일명")
    language: str = Field(..., description="프로그래밍 언어")
    content: Optional[str] = Field(None, description="파일 내용")
    size_bytes: int = Field(0, description="파일 크기")
    lines_count: int = Field(0, description="라인 수")
    encoding: str = Field("utf-8", description="인코딩")
    sha256_hash: Optional[str] = Field(None, description="SHA256 해시")


class CodeFunctionCreate(BaseModel):
    """함수 생성 스키마"""
    file_id: int = Field(..., description="파일 ID")
    name: str = Field(..., description="함수명")
    signature: Optional[str] = Field(None, description="함수 시그니처")
    docstring: Optional[str] = Field(None, description="독스트링")
    start_line: int = Field(..., description="시작 라인")
    end_line: int = Field(..., description="끝 라인")
    complexity: int = Field(1, description="복잡도")
    parameters: Optional[List[Dict[str, Any]]] = Field(None, description="매개변수 정보")
    return_type: Optional[str] = Field(None, description="반환 타입")
    is_async: bool = Field(False, description="비동기 함수 여부")
    is_method: bool = Field(False, description="메서드 여부")
    class_name: Optional[str] = Field(None, description="클래스명 (메서드인 경우)")


class CodeClassCreate(BaseModel):
    """클래스 생성 스키마"""
    file_id: int = Field(..., description="파일 ID")
    name: str = Field(..., description="클래스명")
    docstring: Optional[str] = Field(None, description="독스트링")
    start_line: int = Field(..., description="시작 라인")
    end_line: int = Field(..., description="끝 라인")
    base_classes: Optional[List[str]] = Field(None, description="상속 클래스 목록")
    methods: Optional[List[str]] = Field(None, description="메서드 목록")
    attributes: Optional[List[str]] = Field(None, description="속성 목록")
    is_abstract: bool = Field(False, description="추상 클래스 여부")


class GitCommitCreate(BaseModel):
    """Git 커밋 생성 스키마"""
    hash: str = Field(..., description="커밋 해시")
    message: str = Field(..., description="커밋 메시지")
    author_name: str = Field(..., description="작성자명")
    author_email: str = Field(..., description="작성자 이메일")
    timestamp: datetime.datetime = Field(..., description="커밋 시간")
    changed_files: Optional[List[str]] = Field(None, description="변경된 파일 목록")
    insertions: int = Field(0, description="추가된 라인 수")
    deletions: int = Field(0, description="삭제된 라인 수")
    is_merge: bool = Field(False, description="머지 커밋 여부")
    is_bug_fix: bool = Field(False, description="버그 수정 커밋 여부")


# Search and Analysis Models
class SearchMode(str, Enum):
    """검색 모드"""
    SEMANTIC = "semantic"
    STRUCTURAL = "structural"
    TEMPORAL = "temporal"
    HYBRID = "hybrid"
    AUTO = "auto"


class ContextType(str, Enum):
    """컨텍스트 타입"""
    FILE = "file"
    FUNCTION = "function"
    CLASS = "class"
    CHUNK = "chunk"
    COMMIT = "commit"


class SearchRequest(BaseModel):
    """검색 요청 스키마"""
    query: str = Field(..., description="검색 쿼리")
    mode: SearchMode = Field(SearchMode.AUTO, description="검색 모드")
    max_results: int = Field(10, description="최대 결과 수")
    context_types: Optional[List[ContextType]] = Field(None, description="컨텍스트 타입 필터")
    file_extensions: Optional[List[str]] = Field(None, description="파일 확장자 필터")


@dataclass
class SearchResult:
    """검색 결과"""
    file_path: str
    content: str
    score: float
    start_line: int
    end_line: int
    context_type: str
    explanation: str
    metadata: Dict[str, Any]
    related_changes: Optional[List[Dict[str, Any]]] = None


@dataclass
class AnalysisStats:
    """분석 통계"""
    total_files: int
    total_functions: int
    total_classes: int
    total_commits: int
    languages: Dict[str, int]
    file_sizes: Dict[str, int]
    last_analysis: Optional[datetime.datetime]
