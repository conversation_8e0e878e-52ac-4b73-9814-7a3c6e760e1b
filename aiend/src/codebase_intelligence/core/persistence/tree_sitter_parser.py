"""
Tree-sitter Code Parser

Tree-sitter를 사용한 다양한 언어의 코드 구조 분석
"""

import logging
from typing import List, Dict, Any, Optional, Tuple, Set
from pathlib import Path
from dataclasses import dataclass

try:
    import tree_sitter
    from tree_sitter import Language, Parser, Node
    TREE_SITTER_AVAILABLE = True
except ImportError:
    TREE_SITTER_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Tree-sitter not available. Install with: pip install tree-sitter")

logger = logging.getLogger(__name__)


@dataclass
class ParsedFunction:
    """파싱된 함수 정보"""
    name: str
    start_line: int
    end_line: int
    start_byte: int
    end_byte: int
    parameters: List[Dict[str, Any]]
    return_type: Optional[str]
    docstring: Optional[str]
    is_async: bool
    is_method: bool
    class_name: Optional[str]
    visibility: str  # public, private, protected
    decorators: List[str]
    complexity: int


@dataclass
class ParsedClass:
    """파싱된 클래스 정보"""
    name: str
    start_line: int
    end_line: int
    start_byte: int
    end_byte: int
    base_classes: List[str]
    methods: List[str]
    attributes: List[str]
    docstring: Optional[str]
    is_abstract: bool
    visibility: str
    decorators: List[str]


@dataclass
class ParsedImport:
    """파싱된 임포트 정보"""
    module: str
    names: List[str]
    alias: Optional[str]
    is_from_import: bool
    line_number: int


class TreeSitterParser:
    """Tree-sitter 기반 코드 파서"""
    
    def __init__(self):
        """Tree-sitter 파서 초기화"""
        self.parsers = {}
        self.languages = {}
        
        if not TREE_SITTER_AVAILABLE:
            logger.warning("Tree-sitter not available")
            return
        
        # 지원하는 언어들 초기화
        self._initialize_languages()
    
    def _initialize_languages(self):
        """지원하는 언어들 초기화"""
        language_configs = {
            'python': {
                'library_name': 'tree-sitter-python',
                'so_file': 'python.so'
            },
            'javascript': {
                'library_name': 'tree-sitter-javascript', 
                'so_file': 'javascript.so'
            },
            'typescript': {
                'library_name': 'tree-sitter-typescript',
                'so_file': 'typescript.so'
            },
            'ruby': {
                'library_name': 'tree-sitter-ruby',
                'so_file': 'ruby.so'
            },
            'java': {
                'library_name': 'tree-sitter-java',
                'so_file': 'java.so'
            },
            'cpp': {
                'library_name': 'tree-sitter-cpp',
                'so_file': 'cpp.so'
            },
            'c': {
                'library_name': 'tree-sitter-c',
                'so_file': 'c.so'
            },
            'go': {
                'library_name': 'tree-sitter-go',
                'so_file': 'go.so'
            },
            'rust': {
                'library_name': 'tree-sitter-rust',
                'so_file': 'rust.so'
            }
        }
        
        for lang_name, config in language_configs.items():
            try:
                # 언어 라이브러리 로드 시도
                # 실제 환경에서는 언어별 라이브러리가 설치되어 있어야 함
                language = Language(f"build/{config['so_file']}")
                parser = Parser()
                parser.set_language(language)
                
                self.languages[lang_name] = language
                self.parsers[lang_name] = parser
                
                logger.debug(f"Initialized Tree-sitter parser for {lang_name}")
                
            except Exception as e:
                logger.debug(f"Failed to initialize {lang_name} parser: {e}")
                # 언어별 파서가 없어도 계속 진행
                continue
    
    def is_available(self, language: str = None) -> bool:
        """Tree-sitter 파서 사용 가능 여부"""
        if not TREE_SITTER_AVAILABLE:
            return False
        
        if language:
            return language in self.parsers
        
        return len(self.parsers) > 0
    
    def parse_file(self, content: str, language: str) -> Optional[Node]:
        """파일 파싱"""
        if not self.is_available(language):
            logger.warning(f"Parser not available for language: {language}")
            return None
        
        try:
            parser = self.parsers[language]
            tree = parser.parse(bytes(content, 'utf-8'))
            return tree.root_node
        except Exception as e:
            logger.error(f"Failed to parse {language} code: {e}")
            return None
    
    def extract_functions(self, content: str, language: str) -> List[ParsedFunction]:
        """함수 정보 추출"""
        root_node = self.parse_file(content, language)
        if not root_node:
            return []
        
        functions = []
        
        if language == 'python':
            functions = self._extract_python_functions(root_node, content)
        elif language in ['javascript', 'typescript']:
            functions = self._extract_js_functions(root_node, content)
        elif language == 'ruby':
            functions = self._extract_ruby_functions(root_node, content)
        elif language == 'java':
            functions = self._extract_java_functions(root_node, content)
        # 다른 언어들도 필요에 따라 추가
        
        return functions
    
    def extract_classes(self, content: str, language: str) -> List[ParsedClass]:
        """클래스 정보 추출"""
        root_node = self.parse_file(content, language)
        if not root_node:
            return []
        
        classes = []
        
        if language == 'python':
            classes = self._extract_python_classes(root_node, content)
        elif language in ['javascript', 'typescript']:
            classes = self._extract_js_classes(root_node, content)
        elif language == 'ruby':
            classes = self._extract_ruby_classes(root_node, content)
        elif language == 'java':
            classes = self._extract_java_classes(root_node, content)
        
        return classes
    
    def extract_imports(self, content: str, language: str) -> List[ParsedImport]:
        """임포트 정보 추출"""
        root_node = self.parse_file(content, language)
        if not root_node:
            return []
        
        imports = []
        
        if language == 'python':
            imports = self._extract_python_imports(root_node, content)
        elif language in ['javascript', 'typescript']:
            imports = self._extract_js_imports(root_node, content)
        elif language == 'ruby':
            imports = self._extract_ruby_imports(root_node, content)
        elif language == 'java':
            imports = self._extract_java_imports(root_node, content)
        
        return imports
    
    def _extract_python_functions(self, root_node: Node, content: str) -> List[ParsedFunction]:
        """Python 함수 추출"""
        functions = []
        lines = content.splitlines()
        
        def traverse(node: Node, class_name: Optional[str] = None):
            if node.type == 'function_definition':
                func_info = self._parse_python_function(node, content, lines, class_name)
                if func_info:
                    functions.append(func_info)
            elif node.type == 'class_definition':
                # 클래스 내부의 메서드들 탐색
                class_name_node = node.child_by_field_name('name')
                current_class_name = class_name_node.text.decode('utf-8') if class_name_node else None
                
                for child in node.children:
                    traverse(child, current_class_name)
            else:
                for child in node.children:
                    traverse(child, class_name)
        
        traverse(root_node)
        return functions
    
    def _parse_python_function(self, node: Node, content: str, lines: List[str], class_name: Optional[str]) -> Optional[ParsedFunction]:
        """Python 함수 파싱"""
        try:
            # 함수명 추출
            name_node = node.child_by_field_name('name')
            if not name_node:
                return None
            
            name = name_node.text.decode('utf-8')
            
            # 위치 정보
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1
            start_byte = node.start_byte
            end_byte = node.end_byte
            
            # 매개변수 추출
            parameters = []
            params_node = node.child_by_field_name('parameters')
            if params_node:
                for param in params_node.children:
                    if param.type == 'identifier':
                        parameters.append({
                            'name': param.text.decode('utf-8'),
                            'type': None,
                            'default': None
                        })
                    elif param.type == 'typed_parameter':
                        # 타입 힌트가 있는 매개변수
                        param_name = param.child_by_field_name('pattern')
                        param_type = param.child_by_field_name('type')
                        parameters.append({
                            'name': param_name.text.decode('utf-8') if param_name else '',
                            'type': param_type.text.decode('utf-8') if param_type else None,
                            'default': None
                        })
            
            # 독스트링 추출
            docstring = None
            body_node = node.child_by_field_name('body')
            if body_node and body_node.children:
                first_stmt = body_node.children[0]
                if first_stmt.type == 'expression_statement':
                    expr = first_stmt.children[0]
                    if expr.type == 'string':
                        docstring = expr.text.decode('utf-8').strip('"\'')
            
            # 비동기 함수 여부
            is_async = any(child.type == 'async' for child in node.children)
            
            # 데코레이터 추출
            decorators = []
            prev_sibling = node.prev_sibling
            while prev_sibling and prev_sibling.type == 'decorator':
                decorator_text = prev_sibling.text.decode('utf-8')
                decorators.append(decorator_text)
                prev_sibling = prev_sibling.prev_sibling
            
            # 복잡도 계산 (간단한 버전)
            complexity = self._calculate_complexity(node)
            
            return ParsedFunction(
                name=name,
                start_line=start_line,
                end_line=end_line,
                start_byte=start_byte,
                end_byte=end_byte,
                parameters=parameters,
                return_type=None,  # Python에서는 별도 추출 필요
                docstring=docstring,
                is_async=is_async,
                is_method=class_name is not None,
                class_name=class_name,
                visibility='public',  # Python은 기본적으로 public
                decorators=decorators,
                complexity=complexity
            )
            
        except Exception as e:
            logger.error(f"Failed to parse Python function: {e}")
            return None
    
    def _extract_python_classes(self, root_node: Node, content: str) -> List[ParsedClass]:
        """Python 클래스 추출"""
        classes = []
        
        def traverse(node: Node):
            if node.type == 'class_definition':
                class_info = self._parse_python_class(node, content)
                if class_info:
                    classes.append(class_info)
            
            for child in node.children:
                traverse(child)
        
        traverse(root_node)
        return classes
    
    def _parse_python_class(self, node: Node, content: str) -> Optional[ParsedClass]:
        """Python 클래스 파싱"""
        try:
            # 클래스명 추출
            name_node = node.child_by_field_name('name')
            if not name_node:
                return None
            
            name = name_node.text.decode('utf-8')
            
            # 위치 정보
            start_line = node.start_point[0] + 1
            end_line = node.end_point[0] + 1
            start_byte = node.start_byte
            end_byte = node.end_byte
            
            # 상속 클래스 추출
            base_classes = []
            superclasses_node = node.child_by_field_name('superclasses')
            if superclasses_node:
                for base in superclasses_node.children:
                    if base.type == 'identifier':
                        base_classes.append(base.text.decode('utf-8'))
            
            # 메서드와 속성 추출
            methods = []
            attributes = []
            
            body_node = node.child_by_field_name('body')
            if body_node:
                for child in body_node.children:
                    if child.type == 'function_definition':
                        method_name_node = child.child_by_field_name('name')
                        if method_name_node:
                            methods.append(method_name_node.text.decode('utf-8'))
                    elif child.type == 'assignment':
                        # 클래스 속성 (간단한 버전)
                        target = child.child_by_field_name('left')
                        if target and target.type == 'identifier':
                            attributes.append(target.text.decode('utf-8'))
            
            # 독스트링 추출
            docstring = None
            if body_node and body_node.children:
                first_stmt = body_node.children[0]
                if first_stmt.type == 'expression_statement':
                    expr = first_stmt.children[0]
                    if expr.type == 'string':
                        docstring = expr.text.decode('utf-8').strip('"\'')
            
            # 데코레이터 추출
            decorators = []
            prev_sibling = node.prev_sibling
            while prev_sibling and prev_sibling.type == 'decorator':
                decorator_text = prev_sibling.text.decode('utf-8')
                decorators.append(decorator_text)
                prev_sibling = prev_sibling.prev_sibling
            
            return ParsedClass(
                name=name,
                start_line=start_line,
                end_line=end_line,
                start_byte=start_byte,
                end_byte=end_byte,
                base_classes=base_classes,
                methods=methods,
                attributes=attributes,
                docstring=docstring,
                is_abstract=False,  # ABC 체크는 별도 로직 필요
                visibility='public',
                decorators=decorators
            )
            
        except Exception as e:
            logger.error(f"Failed to parse Python class: {e}")
            return None
    
    def _extract_python_imports(self, root_node: Node, content: str) -> List[ParsedImport]:
        """Python 임포트 추출"""
        imports = []
        
        def traverse(node: Node):
            if node.type == 'import_statement':
                import_info = self._parse_python_import(node)
                if import_info:
                    imports.append(import_info)
            elif node.type == 'import_from_statement':
                import_info = self._parse_python_from_import(node)
                if import_info:
                    imports.append(import_info)
            
            for child in node.children:
                traverse(child)
        
        traverse(root_node)
        return imports
    
    def _parse_python_import(self, node: Node) -> Optional[ParsedImport]:
        """Python import 문 파싱"""
        try:
            line_number = node.start_point[0] + 1
            names = []
            
            for child in node.children:
                if child.type == 'dotted_name':
                    names.append(child.text.decode('utf-8'))
                elif child.type == 'aliased_import':
                    # import module as alias 형태
                    name_node = child.child_by_field_name('name')
                    alias_node = child.child_by_field_name('alias')
                    if name_node:
                        module_name = name_node.text.decode('utf-8')
                        alias = alias_node.text.decode('utf-8') if alias_node else None
                        return ParsedImport(
                            module=module_name,
                            names=[module_name],
                            alias=alias,
                            is_from_import=False,
                            line_number=line_number
                        )
            
            if names:
                return ParsedImport(
                    module=names[0],
                    names=names,
                    alias=None,
                    is_from_import=False,
                    line_number=line_number
                )
            
        except Exception as e:
            logger.error(f"Failed to parse Python import: {e}")
        
        return None
    
    def _parse_python_from_import(self, node: Node) -> Optional[ParsedImport]:
        """Python from import 문 파싱"""
        try:
            line_number = node.start_point[0] + 1
            module = ""
            names = []
            
            module_node = node.child_by_field_name('module_name')
            if module_node:
                module = module_node.text.decode('utf-8')
            
            # import 대상들 추출
            for child in node.children:
                if child.type == 'import_list':
                    for item in child.children:
                        if item.type == 'dotted_name':
                            names.append(item.text.decode('utf-8'))
                        elif item.type == 'aliased_import':
                            name_node = item.child_by_field_name('name')
                            if name_node:
                                names.append(name_node.text.decode('utf-8'))
                elif child.type == 'wildcard_import':
                    names.append('*')
            
            return ParsedImport(
                module=module,
                names=names,
                alias=None,
                is_from_import=True,
                line_number=line_number
            )
            
        except Exception as e:
            logger.error(f"Failed to parse Python from import: {e}")
            return None
    
    def _calculate_complexity(self, node: Node) -> int:
        """순환 복잡도 계산"""
        complexity = 1
        
        def traverse(n: Node):
            nonlocal complexity
            
            # 복잡도를 증가시키는 노드들
            if n.type in ['if_statement', 'while_statement', 'for_statement', 
                         'try_statement', 'except_clause', 'with_statement']:
                complexity += 1
            elif n.type in ['and', 'or']:
                complexity += 1
            
            for child in n.children:
                traverse(child)
        
        traverse(node)
        return complexity
    
    # JavaScript/TypeScript, Ruby, Java 등의 파싱 메서드들은 비슷한 패턴으로 구현
    # 여기서는 Python 구현만 상세히 작성하고, 다른 언어들은 기본 구조만 제공
    
    def _extract_js_functions(self, root_node: Node, content: str) -> List[ParsedFunction]:
        """JavaScript/TypeScript 함수 추출 (기본 구현)"""
        # Tree-sitter JavaScript 파서를 사용한 구현
        # 실제 구현은 JavaScript AST 구조에 맞게 작성 필요
        return []
    
    def _extract_js_classes(self, root_node: Node, content: str) -> List[ParsedClass]:
        """JavaScript/TypeScript 클래스 추출 (기본 구현)"""
        return []
    
    def _extract_js_imports(self, root_node: Node, content: str) -> List[ParsedImport]:
        """JavaScript/TypeScript 임포트 추출 (기본 구현)"""
        return []
    
    def _extract_ruby_functions(self, root_node: Node, content: str) -> List[ParsedFunction]:
        """Ruby 함수 추출 (기본 구현)"""
        return []
    
    def _extract_ruby_classes(self, root_node: Node, content: str) -> List[ParsedClass]:
        """Ruby 클래스 추출 (기본 구현)"""
        return []
    
    def _extract_ruby_imports(self, root_node: Node, content: str) -> List[ParsedImport]:
        """Ruby 임포트 추출 (기본 구현)"""
        return []
    
    def _extract_java_functions(self, root_node: Node, content: str) -> List[ParsedFunction]:
        """Java 메서드 추출 (기본 구현)"""
        return []
    
    def _extract_java_classes(self, root_node: Node, content: str) -> List[ParsedClass]:
        """Java 클래스 추출 (기본 구현)"""
        return []
    
    def _extract_java_imports(self, root_node: Node, content: str) -> List[ParsedImport]:
        """Java 임포트 추출 (기본 구현)"""
        return []
