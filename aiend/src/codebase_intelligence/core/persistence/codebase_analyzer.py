"""
Codebase Analyzer

코드베이스 전체 분석 및 인덱싱
"""

import logging
import ast
import hashlib
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Set, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor, as_completed

from .sqlite_manager import SQLiteManager
from .vector_store import VectorStore
from .git_analyzer import GitAnalyzer
from .tree_sitter_parser import TreeSitterParser
from .models import AnalysisStats, CodeFile, CodeFunction, CodeClass

logger = logging.getLogger(__name__)


def string_to_int_id(string_id: str) -> int:
    """문자열 ID를 정수 ID로 변환 (Qdrant용)"""
    # SHA256 해시를 생성하고 처음 8바이트를 정수로 변환
    hash_bytes = hashlib.sha256(string_id.encode()).digest()[:8]
    return int.from_bytes(hash_bytes, byteorder='big', signed=False)


@dataclass
class FileAnalysisResult:
    """파일 분석 결과"""
    file_path: str
    language: str
    functions: List[Dict[str, Any]]
    classes: List[Dict[str, Any]]
    imports: List[str]
    complexity: int
    lines_count: int
    size_bytes: int
    encoding: str
    sha256_hash: str


class CodebaseAnalyzer:
    """코드베이스 분석기"""
    
    # 지원하는 파일 확장자
    SUPPORTED_EXTENSIONS = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.jsx': 'javascript',
        '.tsx': 'typescript',
        '.rb': 'ruby',
        '.java': 'java',
        '.cpp': 'cpp',
        '.c': 'c',
        '.h': 'c',
        '.hpp': 'cpp',
        '.cs': 'csharp',
        '.php': 'php',
        '.go': 'go',
        '.rs': 'rust',
        '.kt': 'kotlin',
        '.swift': 'swift',
        '.scala': 'scala',
        '.r': 'r',
        '.sql': 'sql',
        '.sh': 'bash',
        '.yaml': 'yaml',
        '.yml': 'yaml',
        '.json': 'json',
        '.xml': 'xml',
        '.html': 'html',
        '.css': 'css',
        '.scss': 'scss',
        '.less': 'less',
        '.md': 'markdown',
        '.txt': 'text'
    }
    
    # 제외할 디렉토리
    EXCLUDED_DIRS = {
        '.git', '.svn', '.hg', '.bzr',
        'node_modules', '__pycache__', '.pytest_cache',
        'venv', 'env', '.env', 'virtualenv',
        'build', 'dist', 'target', 'bin', 'obj',
        '.idea', '.vscode', '.vs',
        'coverage', '.coverage', '.nyc_output',
        'logs', 'log', 'tmp', 'temp',
        '.DS_Store', 'Thumbs.db'
    }
    
    # 제외할 파일 패턴
    EXCLUDED_FILES = {
        '.gitignore', '.gitkeep', '.gitmodules',
        '.dockerignore', 'Dockerfile', 'docker-compose.yml',
        'package-lock.json', 'yarn.lock', 'Pipfile.lock',
        'requirements.txt', 'setup.py', 'setup.cfg',
        'Makefile', 'CMakeLists.txt',
        'LICENSE', 'README.md', 'CHANGELOG.md'
    }
    
    def __init__(
        self,
        repo_path: str,
        db_manager: SQLiteManager,
        vector_store: VectorStore,
        git_analyzer: GitAnalyzer,
        repository_id: str = "default",
        branch: str = "main",
        max_workers: int = 4,
        use_tree_sitter: bool = True
    ):
        """
        Args:
            repo_path: 저장소 경로
            db_manager: SQLite 관리자
            vector_store: 벡터 스토어
            git_analyzer: Git 분석기
            repository_id: 저장소 ID
            branch: 브랜치명
            max_workers: 병렬 처리 워커 수
            use_tree_sitter: Tree-sitter 사용 여부
        """
        self.repo_path = Path(repo_path)
        self.db_manager = db_manager
        self.vector_store = vector_store
        self.git_analyzer = git_analyzer
        self.repository_id = repository_id
        self.branch = branch
        self.max_workers = max_workers
        self.use_tree_sitter = use_tree_sitter

        # Tree-sitter 파서 초기화
        self.tree_sitter_parser = None
        if use_tree_sitter:
            try:
                self.tree_sitter_parser = TreeSitterParser()
                if self.tree_sitter_parser.is_available():
                    logger.info("Tree-sitter parser initialized")
                else:
                    logger.warning("Tree-sitter parser not available, falling back to AST")
                    self.tree_sitter_parser = None
            except Exception as e:
                logger.warning(f"Failed to initialize Tree-sitter parser: {e}")
                self.tree_sitter_parser = None

        logger.info(f"Codebase analyzer initialized: {self.repo_path}")
    
    def analyze_repository(self, force_reindex: bool = False) -> AnalysisStats:
        """저장소 전체 분석"""
        logger.info("Starting repository analysis...")
        
        try:
            # 기존 데이터 정리 (force_reindex인 경우)
            if force_reindex:
                logger.info("Force reindex: clearing existing data")
                self.db_manager.clear_all_data()
                if self.vector_store.is_available():
                    self.vector_store.clear_collection()
            
            # 파일 스캔
            code_files = self._scan_code_files()
            logger.info(f"Found {len(code_files)} code files")
            
            # 파일 분석 (병렬 처리)
            analysis_results = self._analyze_files_parallel(code_files)
            logger.info(f"Analyzed {len(analysis_results)} files")
            
            # 데이터베이스에 저장
            self._save_analysis_results(analysis_results)
            
            # 벡터 임베딩 생성
            if self.vector_store.is_available():
                self._generate_embeddings(analysis_results)
            
            # Git 히스토리 분석
            if self.git_analyzer.is_available():
                self._analyze_git_history()
            
            # 통계 생성
            stats = self.db_manager.get_analysis_stats()
            logger.info("Repository analysis completed")
            
            return stats
            
        except Exception as e:
            logger.error(f"Repository analysis failed: {e}")
            raise
    
    def _scan_code_files(self) -> List[Path]:
        """코드 파일 스캔"""
        code_files = []
        
        try:
            for file_path in self.repo_path.rglob('*'):
                # 디렉토리 제외
                if file_path.is_dir():
                    continue
                
                # 제외 디렉토리 확인
                if any(excluded in file_path.parts for excluded in self.EXCLUDED_DIRS):
                    continue
                
                # 제외 파일 확인
                if file_path.name in self.EXCLUDED_FILES:
                    continue
                
                # 지원하는 확장자 확인
                if file_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                    code_files.append(file_path)
            
            logger.debug(f"Scanned {len(code_files)} code files")
            return code_files
            
        except Exception as e:
            logger.error(f"Failed to scan code files: {e}")
            return []
    
    def _analyze_files_parallel(self, file_paths: List[Path]) -> List[FileAnalysisResult]:
        """파일들을 병렬로 분석"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 작업 제출
            future_to_file = {
                executor.submit(self._analyze_single_file, file_path): file_path
                for file_path in file_paths
            }
            
            # 결과 수집
            for future in as_completed(future_to_file):
                file_path = future_to_file[future]
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                except Exception as e:
                    logger.error(f"Failed to analyze file {file_path}: {e}")
        
        return results
    
    def _analyze_single_file(self, file_path: Path) -> Optional[FileAnalysisResult]:
        """단일 파일 분석"""
        try:
            # 파일 정보 수집
            relative_path = file_path.relative_to(self.repo_path)
            language = self.SUPPORTED_EXTENSIONS.get(file_path.suffix.lower(), 'unknown')
            
            # 파일 읽기
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                encoding = 'utf-8'
            except UnicodeDecodeError:
                try:
                    with open(file_path, 'r', encoding='latin-1') as f:
                        content = f.read()
                    encoding = 'latin-1'
                except Exception:
                    logger.warning(f"Failed to read file: {file_path}")
                    return None
            
            # 파일 메타데이터
            file_stats = file_path.stat()
            lines_count = len(content.splitlines())
            size_bytes = file_stats.st_size
            sha256_hash = hashlib.sha256(content.encode()).hexdigest()
            
            # 언어별 분석
            functions = []
            classes = []
            imports = []
            complexity = 1

            # Tree-sitter 사용 가능한 경우 우선 사용
            if self.tree_sitter_parser and self.tree_sitter_parser.is_available(language):
                functions, classes, imports, complexity = self._analyze_with_tree_sitter(content, language)
            else:
                # Tree-sitter 사용 불가능한 경우 기존 방식 사용
                if language == 'python':
                    functions, classes, imports, complexity = self._analyze_python_file(content)
                elif language in ['javascript', 'typescript']:
                    # JavaScript/TypeScript 분석 (기본 구현)
                    functions, classes = self._analyze_js_file(content)
                # 다른 언어들은 향후 구현
            
            return FileAnalysisResult(
                file_path=str(relative_path),
                language=language,
                functions=functions,
                classes=classes,
                imports=imports,
                complexity=complexity,
                lines_count=lines_count,
                size_bytes=size_bytes,
                encoding=encoding,
                sha256_hash=sha256_hash
            )
            
        except Exception as e:
            logger.error(f"Failed to analyze file {file_path}: {e}")
            return None

    def _analyze_with_tree_sitter(self, content: str, language: str) -> Tuple[List[Dict], List[Dict], List[str], int]:
        """Tree-sitter를 사용한 파일 분석"""
        try:
            # Tree-sitter로 함수, 클래스, 임포트 추출
            parsed_functions = self.tree_sitter_parser.extract_functions(content, language)
            parsed_classes = self.tree_sitter_parser.extract_classes(content, language)
            parsed_imports = self.tree_sitter_parser.extract_imports(content, language)

            # 함수 정보 변환
            functions = []
            total_complexity = 1

            for func in parsed_functions:
                func_dict = {
                    'name': func.name,
                    'start_line': func.start_line,
                    'end_line': func.end_line,
                    'is_async': func.is_async,
                    'parameters': [p['name'] for p in func.parameters],
                    'docstring': func.docstring,
                    'complexity': func.complexity,
                    'is_method': func.is_method,
                    'class_name': func.class_name,
                    'visibility': func.visibility,
                    'decorators': func.decorators
                }
                functions.append(func_dict)
                total_complexity += func.complexity

            # 클래스 정보 변환
            classes = []
            for cls in parsed_classes:
                class_dict = {
                    'name': cls.name,
                    'start_line': cls.start_line,
                    'end_line': cls.end_line,
                    'base_classes': cls.base_classes,
                    'methods': cls.methods,
                    'docstring': cls.docstring,
                    'is_abstract': cls.is_abstract,
                    'visibility': cls.visibility,
                    'decorators': cls.decorators
                }
                classes.append(class_dict)

            # 임포트 정보 변환
            imports = []
            for imp in parsed_imports:
                if imp.is_from_import:
                    for name in imp.names:
                        imports.append(f"{imp.module}.{name}")
                else:
                    imports.extend(imp.names)

            logger.debug(f"Tree-sitter analysis: {len(functions)} functions, {len(classes)} classes, {len(imports)} imports")
            return functions, classes, imports, total_complexity

        except Exception as e:
            logger.error(f"Tree-sitter analysis failed: {e}")
            # Tree-sitter 실패 시 기존 방식으로 폴백
            if language == 'python':
                return self._analyze_python_file(content)
            else:
                return [], [], [], 1

    def _analyze_python_file(self, content: str) -> Tuple[List[Dict], List[Dict], List[str], int]:
        """Python 파일 분석"""
        functions = []
        classes = []
        imports = []
        complexity = 1

        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # 함수 분석
                    func_info = {
                        'name': node.name,
                        'start_line': node.lineno,
                        'end_line': getattr(node, 'end_lineno', node.lineno),
                        'is_async': isinstance(node, ast.AsyncFunctionDef),
                        'parameters': [arg.arg for arg in node.args.args],
                        'docstring': ast.get_docstring(node),
                        'complexity': self._calculate_complexity(node)
                    }
                    functions.append(func_info)
                    complexity += func_info['complexity']

                elif isinstance(node, ast.ClassDef):
                    # 클래스 분석
                    class_methods = [
                        n.name for n in node.body
                        if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef))
                    ]

                    class_info = {
                        'name': node.name,
                        'start_line': node.lineno,
                        'end_line': getattr(node, 'end_lineno', node.lineno),
                        'base_classes': [self._get_name(base) for base in node.bases],
                        'methods': class_methods,
                        'docstring': ast.get_docstring(node)
                    }
                    classes.append(class_info)

                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    # 임포트 분석
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imports.append(alias.name)
                    else:  # ImportFrom
                        module = node.module or ''
                        for alias in node.names:
                            imports.append(f"{module}.{alias.name}")

        except SyntaxError as e:
            logger.warning(f"Python syntax error: {e}")
        except Exception as e:
            logger.error(f"Failed to analyze Python file: {e}")

        return functions, classes, imports, complexity

    def _analyze_js_file(self, content: str) -> Tuple[List[Dict], List[Dict]]:
        """JavaScript/TypeScript 파일 분석 (기본 구현)"""
        functions = []
        classes = []

        # 간단한 정규식 기반 분석 (향후 Tree-sitter로 개선)
        import re

        # 함수 패턴
        func_patterns = [
            r'function\s+(\w+)\s*\(',
            r'const\s+(\w+)\s*=\s*\(',
            r'let\s+(\w+)\s*=\s*\(',
            r'var\s+(\w+)\s*=\s*\(',
            r'(\w+)\s*:\s*function\s*\(',
            r'(\w+)\s*\([^)]*\)\s*=>\s*{'
        ]

        lines = content.splitlines()
        for i, line in enumerate(lines, 1):
            for pattern in func_patterns:
                match = re.search(pattern, line)
                if match:
                    functions.append({
                        'name': match.group(1),
                        'start_line': i,
                        'end_line': i,  # 정확한 끝 라인은 Tree-sitter로 구현 필요
                        'parameters': [],
                        'complexity': 1
                    })
                    break

        # 클래스 패턴
        class_pattern = r'class\s+(\w+)'
        for i, line in enumerate(lines, 1):
            match = re.search(class_pattern, line)
            if match:
                classes.append({
                    'name': match.group(1),
                    'start_line': i,
                    'end_line': i,  # 정확한 끝 라인은 Tree-sitter로 구현 필요
                    'methods': [],
                    'base_classes': []
                })

        return functions, classes

    def _calculate_complexity(self, node: ast.AST) -> int:
        """순환 복잡도 계산 (간단한 버전)"""
        complexity = 1

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1

        return complexity

    def _get_name(self, node: ast.AST) -> str:
        """AST 노드에서 이름 추출"""
        if isinstance(node, ast.Name):
            return node.id
        elif isinstance(node, ast.Attribute):
            return f"{self._get_name(node.value)}.{node.attr}"
        else:
            return str(node)

    def _save_analysis_results(self, results: List[FileAnalysisResult]):
        """분석 결과를 데이터베이스에 저장"""
        logger.info("Saving analysis results to database...")

        # 단일 세션으로 모든 작업 수행
        with self.db_manager.get_session() as session:
            for result in results:
                try:
                    # 파일 정보 저장
                    file_data = {
                        'repository_id': self.repository_id,
                        'branch': self.branch,
                        'path': result.file_path,
                        'name': Path(result.file_path).name,
                        'language': result.language,
                        'size_bytes': result.size_bytes,
                        'lines_count': result.lines_count,
                        'encoding': result.encoding,
                        'sha256_hash': result.sha256_hash
                    }

                    # 기존 파일 확인 (repository_id, branch, path로 고유 식별)
                    existing_file = session.query(CodeFile).filter(
                        CodeFile.repository_id == file_data["repository_id"],
                        CodeFile.branch == file_data["branch"],
                        CodeFile.path == file_data["path"]
                    ).first()

                    if existing_file:
                        # 기존 파일 업데이트
                        for key, value in file_data.items():
                            if hasattr(existing_file, key):
                                setattr(existing_file, key, value)
                        existing_file.updated_at = datetime.utcnow()
                        code_file = existing_file
                    else:
                        # 새 파일 생성
                        code_file = CodeFile(**file_data)
                        session.add(code_file)

                    session.flush()  # ID 생성을 위해 flush

                    # 함수 정보 저장
                    for func in result.functions:
                        func_data = {
                            'file_id': code_file.id,
                            'name': func['name'],
                            'start_line': func['start_line'],
                            'end_line': func['end_line'],
                            'complexity': func.get('complexity', 1),
                            'parameters': func.get('parameters', []),
                            'docstring': func.get('docstring'),
                            'is_async': func.get('is_async', False)
                        }
                        code_function = CodeFunction(**func_data)
                        session.add(code_function)

                    # 클래스 정보 저장
                    for cls in result.classes:
                        class_data = {
                            'file_id': code_file.id,
                            'name': cls['name'],
                            'start_line': cls['start_line'],
                            'end_line': cls['end_line'],
                            'base_classes': cls.get('base_classes', []),
                            'methods': cls.get('methods', []),
                            'docstring': cls.get('docstring')
                        }
                        code_class = CodeClass(**class_data)
                        session.add(code_class)

                except Exception as e:
                    logger.error(f"Failed to save analysis result for {result.file_path}: {e}")

        logger.info("Analysis results saved to database")

    def _generate_embeddings(self, results: List[FileAnalysisResult]):
        """벡터 임베딩 생성"""
        logger.info("Generating vector embeddings...")

        chunks = []

        for result in results:
            try:
                # 파일 전체 내용 청크
                file_id = string_to_int_id(f"file_{result.sha256_hash}")
                file_chunk = {
                    'id': file_id,
                    'content': f"File: {result.file_path}\nLanguage: {result.language}",
                    'metadata': {
                        'chunk_type': 'file',
                        'file_path': result.file_path,
                        'language': result.language,
                        'lines_count': result.lines_count
                    }
                }
                chunks.append(file_chunk)

                # 함수별 청크
                for func in result.functions:
                    func_id = string_to_int_id(f"func_{result.sha256_hash}_{func['name']}")
                    func_chunk = {
                        'id': func_id,
                        'content': f"Function: {func['name']}\nFile: {result.file_path}\nParameters: {func.get('parameters', [])}",
                        'metadata': {
                            'chunk_type': 'function',
                            'file_path': result.file_path,
                            'language': result.language,
                            'function_name': func['name'],
                            'start_line': func['start_line'],
                            'end_line': func['end_line']
                        }
                    }
                    chunks.append(func_chunk)

                # 클래스별 청크
                for cls in result.classes:
                    class_id = string_to_int_id(f"class_{result.sha256_hash}_{cls['name']}")
                    class_chunk = {
                        'id': class_id,
                        'content': f"Class: {cls['name']}\nFile: {result.file_path}\nMethods: {cls.get('methods', [])}",
                        'metadata': {
                            'chunk_type': 'class',
                            'file_path': result.file_path,
                            'language': result.language,
                            'class_name': cls['name'],
                            'start_line': cls['start_line'],
                            'end_line': cls['end_line']
                        }
                    }
                    chunks.append(class_chunk)

            except Exception as e:
                logger.error(f"Failed to create chunks for {result.file_path}: {e}")

        # 벡터 스토어에 추가
        if chunks:
            success_count = self.vector_store.add_code_chunks(chunks)
            logger.info(f"Generated {success_count} vector embeddings")

    def _analyze_git_history(self):
        """Git 히스토리 분석"""
        logger.info("Analyzing git history...")

        try:
            # 모든 커밋 조회
            commits = self.git_analyzer.get_all_commits(max_count=1000)

            for commit_info in commits:
                commit_data = {
                    'hash': commit_info.hash,
                    'message': commit_info.message,
                    'author_name': commit_info.author_name,
                    'author_email': commit_info.author_email,
                    'timestamp': commit_info.timestamp,
                    'changed_files': commit_info.changed_files,
                    'insertions': commit_info.insertions,
                    'deletions': commit_info.deletions,
                    'is_merge': commit_info.is_merge,
                    'is_bug_fix': commit_info.is_bug_fix
                }

                self.db_manager.create_git_commit(commit_data)

            logger.info(f"Analyzed {len(commits)} git commits")

        except Exception as e:
            logger.error(f"Failed to analyze git history: {e}")

    def analyze_file_changes(self, changed_files: List[str]) -> bool:
        """변경된 파일들만 재분석"""
        logger.info(f"Analyzing {len(changed_files)} changed files...")

        try:
            file_paths = []
            for file_path in changed_files:
                full_path = self.repo_path / file_path
                if full_path.exists() and full_path.suffix.lower() in self.SUPPORTED_EXTENSIONS:
                    file_paths.append(full_path)

            if not file_paths:
                logger.info("No supported files to analyze")
                return True

            # 기존 데이터 삭제
            for file_path in file_paths:
                relative_path = file_path.relative_to(self.repo_path)
                self.db_manager.delete_code_file(str(relative_path))
                if self.vector_store.is_available():
                    self.vector_store.delete_by_file_path(str(relative_path))

            # 파일 재분석
            analysis_results = self._analyze_files_parallel(file_paths)

            # 결과 저장
            self._save_analysis_results(analysis_results)

            # 벡터 임베딩 생성
            if self.vector_store.is_available():
                self._generate_embeddings(analysis_results)

            logger.info(f"Successfully analyzed {len(analysis_results)} changed files")
            return True

        except Exception as e:
            logger.error(f"Failed to analyze changed files: {e}")
            return False
