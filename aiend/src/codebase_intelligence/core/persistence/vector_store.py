"""
Vector Store Manager

Qdrant를 사용한 벡터 임베딩 저장 및 검색 관리
"""

import logging
import uuid
import requests
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

try:
    from qdrant_client import QdrantClient
    from qdrant_client.http import models
    from qdrant_client.http.models import Distance, VectorParams, PointStruct
    QDRANT_AVAILABLE = True
except ImportError:
    QDRANT_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("Qdrant client not available. Install with: pip install qdrant-client")

from .models import SearchResult

logger = logging.getLogger(__name__)


@dataclass
class VectorSearchResult:
    """벡터 검색 결과"""
    id: str
    score: float
    payload: Dict[str, Any]


class VectorStore:
    """Qdrant 벡터 스토어 관리자"""

    def __init__(
        self,
        host: str = "qdrant",
        port: int = 6333,
        collection_name: str = "codebase",
        model_name: str = "dengcao/Qwen3-Embedding-8B:Q5_K_M",
        vector_size: int = 4096,
        ollama_url: str = "http://**********:11434"
    ):
        """
        Args:
            host: Qdrant 서버 호스트
            port: Qdrant 서버 포트
            collection_name: 컬렉션 이름
            model_name: 임베딩 모델 이름 (Ollama 모델명)
            vector_size: 벡터 차원 수
            ollama_url: Ollama 서버 URL
        """
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.model_name = model_name
        self.vector_size = vector_size
        self.ollama_url = ollama_url.rstrip('/')

        # Qdrant 클라이언트 초기화 (긴 타임아웃 설정)
        if QDRANT_AVAILABLE:
            try:
                self.client = QdrantClient(host=host, port=port, timeout=300)  # 5분 타임아웃
                self._ensure_collection()
                logger.info(f"Qdrant client initialized: {host}:{port}")
            except Exception as e:
                logger.error(f"Failed to connect to Qdrant: {e}")
                self.client = None
        else:
            self.client = None
            logger.warning("Qdrant client not available")

        # Ollama 연결 테스트
        self.ollama_available = self._test_ollama_connection()
        if self.ollama_available:
            logger.info(f"Ollama embedding model ready: {model_name}")
        else:
            logger.warning(f"Ollama not available at {ollama_url}")

    def _test_ollama_connection(self) -> bool:
        """Ollama 연결 테스트"""
        try:
            response = requests.get(f"{self.ollama_url}/api/tags", timeout=30)  # 30초로 증가
            if response.status_code == 200:
                models_data = response.json()
                model_names = [model['name'] for model in models_data.get('models', [])]
                if self.model_name in model_names:
                    return True
                else:
                    logger.warning(f"Model {self.model_name} not found in Ollama. Available models: {model_names}")
                    return False
            return False
        except Exception as e:
            logger.error(f"Failed to connect to Ollama: {e}")
            return False
    
    def _ensure_collection(self):
        """컬렉션 존재 확인 및 생성"""
        if not self.client:
            return
        
        try:
            # 컬렉션 존재 확인
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                # 컬렉션 생성
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.vector_size,
                        distance=Distance.COSINE
                    )
                )
                logger.info(f"Created collection: {self.collection_name}")
            else:
                logger.info(f"Collection already exists: {self.collection_name}")
                
        except Exception as e:
            logger.error(f"Failed to ensure collection: {e}")
            raise
    
    def is_available(self) -> bool:
        """벡터 스토어 사용 가능 여부 확인"""
        return (
            self.client is not None and
            self.ollama_available and
            QDRANT_AVAILABLE
        )

    def generate_embedding(self, text: str) -> Optional[List[float]]:
        """Ollama를 사용한 텍스트 임베딩 생성"""
        if not self.ollama_available:
            logger.warning("Ollama embedding model not available")
            return None

        try:
            # 텍스트 전처리
            text = text.strip()
            if not text:
                return None

            # Ollama API를 통한 임베딩 생성 (백그라운드 작업이므로 긴 타임아웃 설정)
            response = requests.post(
                f"{self.ollama_url}/api/embeddings",
                json={
                    "model": self.model_name,
                    "prompt": text
                },
                timeout=300  # 5분으로 증가
            )

            if response.status_code == 200:
                result = response.json()
                embedding = result.get('embedding')
                if embedding:
                    return embedding
                else:
                    logger.error(f"No embedding in response: {result}")
                    return None
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.error(f"Failed to generate embedding: {e}")
            return None
    
    def add_code_chunk(
        self,
        chunk_id: str,
        content: str,
        metadata: Dict[str, Any]
    ) -> bool:
        """코드 청크 추가"""
        if not self.is_available():
            logger.warning("Vector store not available")
            return False
        
        try:
            # 임베딩 생성
            embedding = self.generate_embedding(content)
            if not embedding:
                logger.warning(f"Failed to generate embedding for chunk: {chunk_id}")
                return False
            
            # 포인트 생성
            point = PointStruct(
                id=chunk_id,
                vector=embedding,
                payload={
                    "content": content,
                    "chunk_type": metadata.get("chunk_type", "code"),
                    "file_path": metadata.get("file_path", ""),
                    "language": metadata.get("language", ""),
                    "start_line": metadata.get("start_line", 0),
                    "end_line": metadata.get("end_line", 0),
                    "function_name": metadata.get("function_name", ""),
                    "class_name": metadata.get("class_name", ""),
                    **metadata
                }
            )
            
            # Qdrant에 추가
            self.client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.debug(f"Added code chunk: {chunk_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add code chunk: {e}")
            return False
    
    def add_code_chunks(self, chunks: List[Dict[str, Any]]) -> int:
        """여러 코드 청크 실시간 추가 (메모리 효율적)"""
        if not self.is_available():
            logger.warning("Vector store not available")
            return 0

        total_chunks = len(chunks)
        logger.info(f"Starting to process {total_chunks} code chunks with real-time upsert")

        success_count = 0
        batch_size = 10  # 더 작은 배치 크기
        current_batch = []

        for i, chunk in enumerate(chunks, 1):
            try:
                chunk_id = chunk.get("id", str(uuid.uuid4()))
                content = chunk.get("content", "")
                metadata = chunk.get("metadata", {})

                # 진행 상황 로깅 (10개마다 또는 마지막)
                if i % 10 == 0 or i == total_chunks:
                    logger.info(f"Processing chunk {i}/{total_chunks} ({(i/total_chunks)*100:.1f}%)")

                # 임베딩 생성
                embedding = self.generate_embedding(content)
                if not embedding:
                    logger.warning(f"Failed to generate embedding for chunk {i}")
                    continue

                # 포인트 생성
                point = PointStruct(
                    id=chunk_id,
                    vector=embedding,
                    payload={
                        "content": content,
                        **metadata
                    }
                )
                current_batch.append(point)

                # 배치가 가득 찼거나 마지막 청크인 경우 즉시 업서트
                if len(current_batch) >= batch_size or i == total_chunks:
                    try:
                        logger.info(f"Upserting batch of {len(current_batch)} points (chunk {i-len(current_batch)+1}-{i})")

                        self.client.upsert(
                            collection_name=self.collection_name,
                            points=current_batch
                        )

                        success_count += len(current_batch)
                        logger.info(f"Successfully upserted {len(current_batch)} points")
                        current_batch = []  # 배치 초기화

                    except Exception as e:
                        logger.error(f"Failed to upsert batch: {e}")
                        current_batch = []  # 실패해도 배치 초기화

            except Exception as e:
                logger.error(f"Failed to process chunk {i}: {e}")
                continue
        
        logger.info(f"Completed processing {total_chunks} chunks. Successfully added {success_count} to vector store")
        
        return success_count
    
    def search_similar(
        self,
        query: str,
        limit: int = 10,
        score_threshold: float = 0.7,
        filter_conditions: Optional[Dict[str, Any]] = None,
        collection_name: Optional[str] = None
    ) -> List[VectorSearchResult]:
        """유사한 코드 검색"""
        if not self.is_available():
            logger.warning("Vector store not available")
            return []
        
        try:
            # 쿼리 임베딩 생성
            query_embedding = self.generate_embedding(query)
            if not query_embedding:
                logger.warning("Failed to generate query embedding")
                return []
            
            # 필터 조건 구성
            search_filter = None
            if filter_conditions:
                search_filter = models.Filter(
                    must=[
                        models.FieldCondition(
                            key=key,
                            match=models.MatchValue(value=value)
                        )
                        for key, value in filter_conditions.items()
                    ]
                )
            
            target_collection = collection_name if collection_name else self.collection_name

            # 벡터 검색 수행
            search_results = self.client.search(
                collection_name=target_collection,
                query_vector=query_embedding,
                query_filter=search_filter,
                limit=limit,
                score_threshold=score_threshold
            )
            
            # 결과 변환
            results = []
            for result in search_results:
                results.append(VectorSearchResult(
                    id=str(result.id),
                    score=result.score,
                    payload=result.payload
                ))
            
            logger.debug(f"Found {len(results)} similar code chunks")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar code: {e}")
            return []
    
    def delete_by_file_path(self, file_path: str) -> bool:
        """파일 경로로 벡터 삭제"""
        if not self.is_available():
            logger.warning("Vector store not available")
            return False
        
        try:
            # 파일 경로로 필터링하여 삭제
            self.client.delete(
                collection_name=self.collection_name,
                points_selector=models.FilterSelector(
                    filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="file_path",
                                match=models.MatchValue(value=file_path)
                            )
                        ]
                    )
                )
            )
            
            logger.debug(f"Deleted vectors for file: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete vectors by file path: {e}")
            return False
    
    def get_collection_info(self) -> Optional[Dict[str, Any]]:
        """컬렉션 정보 조회"""
        if not self.client:
            return None
        
        try:
            info = self.client.get_collection(self.collection_name)
            return {
                "name": info.config.params.vectors.size,
                "vectors_count": info.vectors_count,
                "indexed_vectors_count": info.indexed_vectors_count,
                "points_count": info.points_count,
                "segments_count": info.segments_count,
                "status": info.status
            }
        except Exception as e:
            logger.error(f"Failed to get collection info: {e}")
            return None
    
    def clear_collection(self) -> bool:
        """컬렉션 내 모든 벡터 삭제"""
        if not self.client:
            return False
        
        try:
            # 컬렉션 삭제 후 재생성
            self.client.delete_collection(self.collection_name)
            self._ensure_collection()
            logger.info(f"Cleared collection: {self.collection_name}")
            return True
        except Exception as e:
            logger.error(f"Failed to clear collection: {e}")
            return False
    
    def close(self):
        """연결 종료"""
        if self.client:
            try:
                self.client.close()
                logger.info("Vector store connection closed")
            except Exception as e:
                logger.error(f"Error closing vector store: {e}")
