"""
Main CLI Application

CodeBase Intelligence System의 메인 CLI 애플리케이션
"""

import logging
from pathlib import Path
from typing import Optional

import typer
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax

from ..core.persistence.sqlite_manager import SQLiteManager
from ..core.persistence.vector_store import VectorStore
from ..core.persistence.git_analyzer import GitAnalyzer
from ..core.persistence.codebase_analyzer import CodebaseAnalyzer
from ..core.intelligence.smart_search import SmartSearch, SearchQuery, SearchMode
from ..core.intelligence.reranker import Reranker
from ..core.context.context_builder import ContextBuilder
from ..core.llm.orchestrator import LLMOrchestrator
from ..utils.config import Config
from ..utils.logger import setup_logging

# CLI 앱 초기화
app = typer.Typer(
    name="codebase-intel",
    help="CodeBase Intelligence System - LLM 페어프로그래밍 최적화 도구",
    add_completion=False
)

# Rich 콘솔
console = Console()

# 전역 상태
config: Optional[Config] = None
codebase_analyzer: Optional[CodebaseAnalyzer] = None


@app.callback()
def main(
    repo_path: str = typer.Option(
        ".", 
        "--repo", "-r",
        help="Git 저장소 경로"
    ),
    config_file: Optional[str] = typer.Option(
        None,
        "--config", "-c", 
        help="설정 파일 경로"
    ),
    verbose: bool = typer.Option(
        False,
        "--verbose", "-v",
        help="상세 로그 출력"
    ),
    debug: bool = typer.Option(
        False,
        "--debug", "-d",
        help="디버그 모드"
    )
):
    """CodeBase Intelligence System 초기화"""
    global config, codebase_analyzer
    
    # 로깅 설정
    log_level = "DEBUG" if debug else ("INFO" if verbose else "WARNING")
    setup_logging(log_level)
    
    # 설정 로드
    config = Config.load(config_file)
    
    # 저장소 경로 검증
    repo_path = Path(repo_path).resolve()
    if not repo_path.exists():
        console.print(f"[red]Error: Repository path does not exist: {repo_path}[/red]")
        raise typer.Exit(1)
    
    console.print(f"[green]Initialized CodeBase Intelligence for: {repo_path}[/green]")


@app.command()
def index(
    force: bool = typer.Option(
        False,
        "--force", "-f",
        help="강제 재인덱싱"
    ),
    languages: Optional[str] = typer.Option(
        None,
        "--languages", "-l",
        help="인덱싱할 언어 (쉼표로 구분)"
    )
):
    """코드베이스 인덱싱"""
    console.print("[blue]Starting codebase indexing...[/blue]")
    
    try:
        # 컴포넌트 초기화
        db_manager = SQLiteManager(config.database.path)
        vector_store = VectorStore(
            persist_directory=config.vector_store.path,
            model_name=config.vector_store.model
        )
        git_analyzer = GitAnalyzer(config.repository.path)
        
        analyzer = CodebaseAnalyzer(
            repo_path=config.repository.path,
            db_manager=db_manager,
            vector_store=vector_store,
            git_analyzer=git_analyzer
        )
        
        # 진행률 표시와 함께 분석 실행
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Analyzing codebase...", total=None)
            
            stats = analyzer.analyze_repository(force_reindex=force)
            
            progress.update(task, completed=True)
        
        # 결과 표시
        _display_indexing_stats(stats)
        
    except Exception as e:
        console.print(f"[red]Indexing failed: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def search(
    query: str = typer.Argument(..., help="검색 쿼리"),
    mode: str = typer.Option(
        "auto",
        "--mode", "-m",
        help="검색 모드 (auto, semantic, structural, temporal, hybrid)"
    ),
    max_results: int = typer.Option(
        10,
        "--max-results", "-n",
        help="최대 결과 수"
    ),
    context_type: Optional[str] = typer.Option(
        None,
        "--type", "-t",
        help="컨텍스트 타입 (function, class, file)"
    ),
    output_format: str = typer.Option(
        "table",
        "--format", "-f",
        help="출력 형식 (table, json, detailed)"
    )
):
    """코드베이스 검색"""
    console.print(f"[blue]Searching for: '{query}'[/blue]")
    
    try:
        # 검색 시스템 초기화
        search_system = _init_search_system()
        
        # 검색 쿼리 구성
        search_query = SearchQuery(
            text=query,
            mode=mode,
            context_types=[context_type] if context_type else None,
            max_results=max_results
        )
        
        # 검색 실행
        results = search_system.search(search_query)
        
        # 결과 출력
        if output_format == "json":
            _display_results_json(results)
        elif output_format == "detailed":
            _display_results_detailed(results)
        else:
            _display_results_table(results)
            
    except Exception as e:
        console.print(f"[red]Search failed: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def ask(
    question: str = typer.Argument(..., help="질문"),
    max_tokens: int = typer.Option(
        8000,
        "--max-tokens",
        help="최대 토큰 수"
    ),
    provider: Optional[str] = typer.Option(
        None,
        "--provider", "-p",
        help="LLM 제공자 (openai, anthropic)"
    ),
    model: Optional[str] = typer.Option(
        None,
        "--model", "-m",
        help="사용할 모델"
    ),
    save_context: bool = typer.Option(
        False,
        "--save-context",
        help="컨텍스트를 파일로 저장"
    )
):
    """LLM에게 질문하기"""
    console.print(f"[blue]Processing question: '{question}'[/blue]")
    
    try:
        # LLM 오케스트레이터 초기화
        orchestrator = _init_llm_orchestrator()
        
        # 질문 처리
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("Generating response...", total=None)
            
            # 비동기 함수를 동기적으로 실행
            import asyncio
            response = asyncio.run(orchestrator.process_query(
                query=question,
                max_tokens=max_tokens
            ))
            
            progress.update(task, completed=True)
        
        # 응답 출력
        _display_llm_response(response, save_context)
        
    except Exception as e:
        console.print(f"[red]Question processing failed: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def stats():
    """시스템 통계 정보"""
    try:
        # 컴포넌트 초기화
        db_manager = SQLiteManager(config.database.path)
        vector_store = VectorStore(config.vector_store.path)
        
        # 통계 수집
        db_stats = db_manager.get_database_stats()
        vector_stats = vector_store.get_collection_stats()
        
        # 통계 표시
        _display_system_stats(db_stats, vector_stats)
        
    except Exception as e:
        console.print(f"[red]Failed to get stats: {e}[/red]")
        raise typer.Exit(1)


@app.command()
def config_show():
    """현재 설정 표시"""
    if config:
        console.print(Panel(
            config.to_yaml(),
            title="Current Configuration",
            border_style="blue"
        ))
    else:
        console.print("[red]No configuration loaded[/red]")


def _init_search_system() -> SmartSearch:
    """검색 시스템 초기화"""
    db_manager = SQLiteManager(config.database.path)
    vector_store = VectorStore(config.vector_store.path)
    reranker = Reranker(config.reranker)
    git_analyzer = GitAnalyzer(config.repository.path)
    
    return SmartSearch(db_manager, vector_store, reranker, git_analyzer)


def _init_llm_orchestrator() -> LLMOrchestrator:
    """LLM 오케스트레이터 초기화"""
    # TODO: 실제 구현에서는 설정에서 LLM 제공자들을 로드
    raise NotImplementedError("LLM orchestrator initialization not implemented yet")


def _display_indexing_stats(stats: dict):
    """인덱싱 통계 표시"""
    table = Table(title="Indexing Results")
    table.add_column("Metric", style="cyan")
    table.add_column("Value", style="green")
    
    table.add_row("Files Processed", str(stats.get("files_processed", 0)))
    table.add_row("Functions Found", str(stats.get("functions_found", 0)))
    table.add_row("Classes Found", str(stats.get("classes_found", 0)))
    table.add_row("Errors", str(stats.get("errors", 0)))
    table.add_row("Duration", f"{stats.get('duration', 0):.2f}s")
    
    console.print(table)


def _display_results_table(results):
    """검색 결과를 테이블로 표시"""
    table = Table(title="Search Results")
    table.add_column("Score", style="cyan", width=8)
    table.add_column("Type", style="green", width=10)
    table.add_column("File", style="blue")
    table.add_column("Lines", style="yellow", width=10)
    table.add_column("Content Preview", style="white")
    
    for result in results:
        content_preview = result.search_result.content[:100] + "..." if len(result.search_result.content) > 100 else result.search_result.content
        table.add_row(
            f"{result.combined_score:.3f}",
            result.search_result.context_type,
            result.search_result.file_path,
            f"{result.search_result.start_line}-{result.search_result.end_line}",
            content_preview.replace('\n', ' ')
        )
    
    console.print(table)


def _display_results_detailed(results):
    """검색 결과를 상세히 표시"""
    for i, result in enumerate(results, 1):
        console.print(f"\n[bold blue]Result {i}[/bold blue]")
        console.print(f"Score: {result.combined_score:.3f}")
        console.print(f"File: {result.search_result.file_path}")
        console.print(f"Lines: {result.search_result.start_line}-{result.search_result.end_line}")
        console.print(f"Type: {result.search_result.context_type}")
        
        # 코드 구문 강조
        syntax = Syntax(
            result.search_result.content,
            "python",  # TODO: 파일 확장자에 따라 동적 결정
            theme="monokai",
            line_numbers=True,
            start_line=result.search_result.start_line
        )
        console.print(syntax)


def _display_results_json(results):
    """검색 결과를 JSON으로 표시"""
    import json
    
    json_results = []
    for result in results:
        json_results.append({
            "score": result.combined_score,
            "file_path": result.search_result.file_path,
            "start_line": result.search_result.start_line,
            "end_line": result.search_result.end_line,
            "context_type": result.search_result.context_type,
            "content": result.search_result.content,
            "explanation": result.explanation
        })
    
    console.print(json.dumps(json_results, indent=2, ensure_ascii=False))


def _display_llm_response(response, save_context: bool):
    """LLM 응답 표시"""
    # 응답 내용
    console.print(Panel(
        response.content,
        title=f"Response (Confidence: {response.confidence:.2f})",
        border_style="green"
    ))
    
    # 메타데이터
    console.print(f"\nStrategy: {response.strategy_used}")
    console.print(f"Execution Time: {response.execution_time:.2f}s")
    console.print(f"Context Tokens: {response.context_used.total_tokens}")
    
    # 컨텍스트 저장
    if save_context:
        context_file = f"context_{int(response.execution_time)}.md"
        with open(context_file, 'w', encoding='utf-8') as f:
            f.write(response.context_used.format_context_for_llm())
        console.print(f"Context saved to: {context_file}")


def _display_system_stats(db_stats: dict, vector_stats: dict):
    """시스템 통계 표시"""
    table = Table(title="System Statistics")
    table.add_column("Component", style="cyan")
    table.add_column("Metric", style="blue")
    table.add_column("Value", style="green")
    
    # 데이터베이스 통계
    for key, value in db_stats.items():
        table.add_row("Database", key.title(), str(value))
    
    # 벡터 스토어 통계
    for key, value in vector_stats.items():
        table.add_row("Vector Store", key.title(), str(value))
    
    console.print(table)


if __name__ == "__main__":
    app()
