services:
  aiend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: codebase-intelligence-aiend
    ports:
      - "7102:8000"   # 호스트:컨테이너 = 7102:8000
    environment:
      - PYTHONPATH=/app
      - CODEBASE_DB_PATH=/app/data/codebase.db
      - CODEBASE_VECTOR_PATH=/app/data/chroma_db
      - CODEBASE_LOG_LEVEL=INFO
      - OLLAMA_HOST=http://host.docker.internal:11434
      - QDRANT_URL=http://host.docker.internal:7105
    volumes:
      # 소스코드 마운트 (개발용 - 코드 변경 시 즉시 반영)
      - ./src:/app/src
      - ./config:/app/config

      # 데이터 마운트 (영구 저장용)
      - /mnt/hdd500/data/codebase-intelligence/aiend/data:/app/data
      - /mnt/hdd500/data/codebase-intelligence/aiend/logs:/app/logs
      - /mnt/hdd500/data/codebase-intelligence/repos:/app/repositories
      - ./.env:/app/.env:ro
    restart: unless-stopped
    user: "1000:1000"  # aiend 사용자 UID:GID
    networks:
      - codebase-intelligence
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s


networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence
