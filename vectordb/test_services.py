#!/usr/bin/env python3
"""
VectorDB 서비스 테스트 스크립트
Qdrant와 FAISS-GPU 서비스의 작동 상태를 확인합니다.
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# 서비스 엔드포인트
QDRANT_URL = "http://localhost:7100"

def test_qdrant() -> bool:
    """Qdrant 서비스 테스트"""
    print("🔍 Qdrant 서비스 테스트 중...")
    
    try:
        # Health check
        response = requests.get(f"{QDRANT_URL}/livez", timeout=5)
        if response.status_code == 200:
            print("✅ Qdrant health check 성공")
        else:
            print(f"❌ Qdrant health check 실패: {response.status_code}")
            return False
            
        # 컬렉션 목록 조회
        response = requests.get(f"{QDRANT_URL}/collections", timeout=5)
        if response.status_code == 200:
            collections = response.json()
            print(f"✅ Qdrant 컬렉션 조회 성공: {len(collections.get('result', {}).get('collections', []))}개 컬렉션")
        else:
            print(f"❌ Qdrant 컬렉션 조회 실패: {response.status_code}")
            return False
            
        # 간단한 컬렉션 생성 테스트
        test_collection = "test_collection"
        collection_config = {
            "vectors": {
                "size": 128,
                "distance": "Cosine"
            }
        }
        
        # 기존 테스트 컬렉션 삭제 (있다면)
        requests.delete(f"{QDRANT_URL}/collections/{test_collection}")
        
        # 새 컬렉션 생성
        response = requests.put(
            f"{QDRANT_URL}/collections/{test_collection}",
            json=collection_config,
            timeout=10
        )
        
        if response.status_code in [200, 201]:
            print("✅ Qdrant 컬렉션 생성 테스트 성공")
            
            # 테스트 컬렉션 삭제
            requests.delete(f"{QDRANT_URL}/collections/{test_collection}")
            print("✅ Qdrant 테스트 컬렉션 정리 완료")
        else:
            print(f"❌ Qdrant 컬렉션 생성 실패: {response.status_code}")
            return False
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Qdrant 연결 실패: {e}")
        return False

def test_qdrant_advanced() -> bool:
    """Qdrant 고급 기능 테스트"""
    print("\n🔍 Qdrant 고급 기능 테스트 중...")

    try:
        # 벡터 검색 테스트를 위한 컬렉션 생성
        test_collection = "test_vector_search"
        collection_config = {
            "vectors": {
                "size": 384,  # 일반적인 임베딩 차원
                "distance": "Cosine"
            }
        }

        # 기존 테스트 컬렉션 삭제 (있다면)
        requests.delete(f"{QDRANT_URL}/collections/{test_collection}")

        # 새 컬렉션 생성
        response = requests.put(
            f"{QDRANT_URL}/collections/{test_collection}",
            json=collection_config,
            timeout=10
        )

        if response.status_code not in [200, 201]:
            print(f"❌ 테스트 컬렉션 생성 실패: {response.status_code}")
            return False

        # 테스트 벡터 데이터 삽입
        test_points = {
            "points": [
                {
                    "id": 1,
                    "vector": [0.1] * 384,
                    "payload": {"text": "테스트 문서 1", "category": "test"}
                },
                {
                    "id": 2,
                    "vector": [0.2] * 384,
                    "payload": {"text": "테스트 문서 2", "category": "test"}
                }
            ]
        }

        response = requests.put(
            f"{QDRANT_URL}/collections/{test_collection}/points",
            json=test_points,
            timeout=10
        )

        if response.status_code not in [200, 201]:
            print(f"❌ 테스트 데이터 삽입 실패: {response.status_code}")
            return False

        print("✅ 테스트 데이터 삽입 성공")

        # 벡터 검색 테스트
        search_query = {
            "vector": [0.15] * 384,
            "limit": 2,
            "with_payload": True
        }

        response = requests.post(
            f"{QDRANT_URL}/collections/{test_collection}/points/search",
            json=search_query,
            timeout=10
        )

        if response.status_code == 200:
            results = response.json()
            if "result" in results and len(results["result"]) > 0:
                print(f"✅ 벡터 검색 성공: {len(results['result'])}개 결과 반환")
            else:
                print("❌ 벡터 검색 결과 없음")
                return False
        else:
            print(f"❌ 벡터 검색 실패: {response.status_code}")
            return False

        # 테스트 컬렉션 정리
        requests.delete(f"{QDRANT_URL}/collections/{test_collection}")
        print("✅ 테스트 컬렉션 정리 완료")

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Qdrant 고급 기능 테스트 실패: {e}")
        return False

def test_qdrant_performance() -> bool:
    """Qdrant 성능 테스트"""
    print("\n🔍 Qdrant 성능 테스트 중...")

    try:
        # 클러스터 정보 확인
        response = requests.get(f"{QDRANT_URL}/cluster", timeout=5)
        if response.status_code == 200:
            cluster_info = response.json()
            print(f"✅ 클러스터 정보: {cluster_info}")

        # 메트릭 확인
        response = requests.get(f"{QDRANT_URL}/metrics", timeout=5)
        if response.status_code == 200:
            print("✅ 메트릭 수집 가능")

        # 텔레메트리 정보 확인
        response = requests.get(f"{QDRANT_URL}/telemetry", timeout=5)
        if response.status_code == 200:
            telemetry = response.json()
            print(f"✅ 텔레메트리 정보 수집 성공")

        return True

    except requests.exceptions.RequestException as e:
        print(f"❌ Qdrant 성능 테스트 실패: {e}")
        return False

def main():
    """메인 테스트 함수"""
    print("🚀 Qdrant VectorDB 서비스 테스트 시작\n")

    # 서비스 시작 대기
    print("⏳ 서비스 시작 대기 중...")
    time.sleep(5)

    results = []

    # Qdrant 테스트 실행
    results.append(("Qdrant 기본", test_qdrant()))
    results.append(("Qdrant 고급", test_qdrant_advanced()))
    results.append(("Qdrant 성능", test_qdrant_performance()))

    # 결과 요약
    print("\n" + "="*50)
    print("📊 테스트 결과 요약")
    print("="*50)

    all_passed = True
    for service, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{service:20} : {status}")
        if not passed:
            all_passed = False

    print("="*50)
    if all_passed:
        print("🎉 모든 테스트 통과!")
        print("✨ Qdrant VectorDB가 정상적으로 작동하고 있습니다!")
        sys.exit(0)
    else:
        print("💥 일부 테스트 실패")
        sys.exit(1)

if __name__ == "__main__":
    main()
