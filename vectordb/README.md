# Qdrant VectorDB 서비스

Qdrant를 활용한 고성능 벡터 데이터베이스 서비스입니다.

## 구성 요소

### Qdrant
- **포트**: 7100 (REST API), 7101 (gRPC API)
- **역할**: 벡터 저장, 인덱싱, 검색 및 메타데이터 관리
- **웹 UI**: http://localhost:7100/dashboard
- **특징**: 
  - 고성능 벡터 검색 엔진
  - 실시간 인덱싱
  - 다양한 거리 메트릭 지원 (Cosine, Euclidean, Dot Product)
  - 필터링 및 페이로드 검색
  - 클러스터링 지원

## 시작하기

### 전제 조건
- Docker 및 Docker Compose

### 실행
```bash
# 서비스 시작
docker compose up -d

# 로그 확인
docker compose logs -f

# 서비스 중지
docker compose down

# 테스트 실행
python3 test_services.py
```

## API 사용법

### 기본 API
```bash
# 헬스 체크
curl http://localhost:7100/livez

# 컬렉션 목록 조회
curl http://localhost:7100/collections

# 컬렉션 생성
curl -X PUT http://localhost:7100/collections/my_collection \
  -H "Content-Type: application/json" \
  -d '{
    "vectors": {
      "size": 384,
      "distance": "Cosine"
    }
  }'
```

### 벡터 작업
```bash
# 벡터 삽입
curl -X PUT http://localhost:7100/collections/my_collection/points \
  -H "Content-Type: application/json" \
  -d '{
    "points": [
      {
        "id": 1,
        "vector": [0.1, 0.2, 0.3, ...],
        "payload": {"text": "문서 내용", "category": "문서"}
      }
    ]
  }'

# 벡터 검색
curl -X POST http://localhost:7100/collections/my_collection/points/search \
  -H "Content-Type: application/json" \
  -d '{
    "vector": [0.1, 0.2, 0.3, ...],
    "limit": 10,
    "with_payload": true
  }'
```

## 데이터 저장

- **Qdrant 데이터**: `./data/qdrant/`
- **스냅샷**: `./data/snapshots/`

## 모니터링

### 헬스 체크
```bash
# 서비스 상태
curl http://localhost:7100/livez

# 클러스터 정보
curl http://localhost:7100/cluster

# 메트릭
curl http://localhost:7100/metrics
```

### 리소스 사용량
```bash
# 컨테이너 상태
docker stats vectordb-qdrant

# 컬렉션 정보
curl http://localhost:7100/collections/my_collection
```

## 성능 최적화

### Qdrant 설정
- 메모리: 최대 8GB
- CPU: 최대 4코어
- 디스크: SSD 권장
- 인덱싱: HNSW 알고리즘 사용

### 벡터 최적화
- 차원 수: 384차원 권장 (BERT 등)
- 거리 메트릭: Cosine 유사도 권장
- 배치 삽입: 대량 데이터 처리 시 배치 사용

## 문제 해결

### 일반적인 문제
1. **메모리 부족**: Docker 메모리 제한 조정
2. **포트 충돌**: 포트 번호 변경 (7100, 7101)
3. **데이터 손실**: 볼륨 마운트 확인

### 로그 확인
```bash
# 전체 로그
docker compose logs

# Qdrant 로그
docker compose logs qdrant

# 실시간 로그
docker compose logs -f qdrant
```

## 개발 환경

### 테스트
```bash
# 서비스 테스트 실행
python3 test_services.py

# 개별 기능 테스트
curl http://localhost:7100/collections
```

### 환경 변수
- `QDRANT__LOG_LEVEL`: 로그 레벨 (DEBUG, INFO, WARNING, ERROR)
- `QDRANT__SERVICE__HOST`: 서비스 호스트 (기본: 0.0.0.0)
- `QDRANT__SERVICE__HTTP_PORT`: HTTP 포트 (기본: 6333)
- `QDRANT__SERVICE__GRPC_PORT`: gRPC 포트 (기본: 6334)

## 사용 예시

### Python 클라이언트
```python
import requests

# 컬렉션 생성
collection_config = {
    "vectors": {
        "size": 384,
        "distance": "Cosine"
    }
}
response = requests.put(
    "http://localhost:7100/collections/documents",
    json=collection_config
)

# 벡터 삽입
points = {
    "points": [
        {
            "id": 1,
            "vector": [0.1] * 384,
            "payload": {"text": "문서 내용", "category": "기술"}
        }
    ]
}
response = requests.put(
    "http://localhost:7100/collections/documents/points",
    json=points
)

# 벡터 검색
search_query = {
    "vector": [0.1] * 384,
    "limit": 5,
    "with_payload": True
}
response = requests.post(
    "http://localhost:7100/collections/documents/points/search",
    json=search_query
)
results = response.json()
```
