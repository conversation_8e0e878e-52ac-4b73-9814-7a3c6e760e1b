services:
  # Qdrant 벡터DB - 모든 벡터 인덱싱과 검색을 처리
  qdrant:
    image: qdrant/qdrant:latest
    container_name: codebase-intelligence-qdrant
    ports:
      - "7105:6333"  # REST API
      - "7106:6334"  # gRPC API
    environment:
      - QDRANT__LOG_LEVEL=INFO
      - QDRANT__SERVICE__HOST=0.0.0.0
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    volumes:
      - /mnt/hdd500/data/codebase-intelligence/vectordb/qdrant:/qdrant/storage
      - /mnt/hdd500/data/codebase-intelligence/vectordb/snapshots:/qdrant/snapshots
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:6333/livez"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 2G
          cpus: '1'

networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence
