# VectorDB Makefile

.PHONY: init start stop restart status logs test benchmark clean help

# 기본 명령어
init:
	@echo "VectorDB 초기화..."
	@chmod +x *.sh
	@./init.sh

start:
	@echo "VectorDB 시작..."
	@docker compose up -d

stop:
	@echo "VectorDB 중지..."
	@./stop.sh

restart: stop start

# 상태 확인
status:
	@./status.sh

logs:
	@./logs.sh all

logs-qdrant:
	@./logs.sh qdrant

logs-faiss:
	@./logs.sh faiss

# 테스트
test:
	@echo "VectorDB 테스트 실행..."
	@python3 test_vectordb.py

benchmark:
	@echo "성능 벤치마크 실행..."
	@python3 benchmark.py

# 정리
clean:
	@echo "VectorDB 완전 정리..."
	@docker compose down -v
	@docker system prune -f
	@sudo rm -rf ./data/*

# 개발용
dev:
	@echo "개발 모드로 시작..."
	@docker compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 도움말
help:
	@echo "VectorDB 명령어:"
	@echo "  make init       - 초기 설정 및 시작"
	@echo "  make start      - 서비스 시작"
	@echo "  make stop       - 서비스 중지"
	@echo "  make restart    - 서비스 재시작"
	@echo "  make status     - 상태 확인"
	@echo "  make logs       - 전체 로그 확인"
	@echo "  make test       - 기능 테스트"
	@echo "  make benchmark  - 성능 벤치마크"
	@echo "  make clean      - 완전 정리"
	@echo "  make help       - 이 도움말"