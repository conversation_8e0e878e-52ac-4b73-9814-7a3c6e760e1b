#!/bin/bash

echo "=== VectorDB 로그 확인 ==="

if [ "$1" = "qdrant" ]; then
    echo "Qdrant 로그:"
    docker compose logs -f qdrant
elif [ "$1" = "faiss" ]; then
    echo "FAISS GPU 로그:"
    docker compose logs -f faiss-gpu
elif [ "$1" = "all" ]; then
    echo "전체 로그:"
    docker compose logs -f
else
    echo "사용법:"
    echo "./logs.sh qdrant   # Qdrant 로그만"
    echo "./logs.sh faiss    # FAISS GPU 로그만" 
    echo "./logs.sh all      # 전체 로그"
    echo ""
    echo "최근 로그 미리보기:"
    echo "--- Qdrant ---"
    docker compose logs --tail=10 qdrant
    echo ""
    echo "--- FAISS GPU ---"
    docker compose logs --tail=10 faiss-gpu
fi