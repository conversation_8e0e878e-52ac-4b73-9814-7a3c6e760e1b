#!/usr/bin/env python3
"""
VectorDB 성능 벤치마크 (Qdrant vs FAISS GPU)
"""

import requests
import numpy as np
import time
import statistics
from concurrent.futures import ThreadPoolExecutor

class VectorDBBenchmark:
    def __init__(self):
        self.qdrant_url = "http://localhost:7105"
        self.faiss_url = "http://localhost:7107"
        self.collection_name = "benchmark"
    
    def setup_benchmark_data(self, num_vectors=1000):
        """벤치마크용 데이터 생성"""
        print(f"벤치마크 데이터 생성 중... ({num_vectors}개 벡터)")
        
        # 벤치마크 컬렉션 생성
        requests.put(
            f"{self.qdrant_url}/collections/{self.collection_name}",
            json={
                "vectors": {"size": 768, "distance": "Cosine"}
            }
        )
        
        # 벡터 데이터 생성 및 삽입
        batch_size = 100
        for i in range(0, num_vectors, batch_size):
            vectors = np.random.rand(min(batch_size, num_vectors - i), 768)
            
            points = []
            for j, vector in enumerate(vectors):
                points.append({
                    "id": f"vec_{i+j}",
                    "vector": vector.tolist(),
                    "payload": {"index": i+j}
                })
            
            response = requests.put(
                f"{self.qdrant_url}/collections/{self.collection_name}/points",
                json={"points": points}
            )
            
            if response.status_code != 200:
                raise Exception(f"데이터 삽입 실패: {response.status_code}")
        
        # FAISS와 동기화
        print("FAISS와 동기화 중...")
        response = requests.post(f"{self.faiss_url}/sync")
        if response.status_code != 200:
            raise Exception("FAISS 동기화 실패")
        
        print("✓ 벤치마크 데이터 준비 완료")
    
    def benchmark_search_latency(self, num_queries=100):
        """검색 지연시간 벤치마크"""
        print(f"\n검색 지연시간 벤치마크 ({num_queries}회 쿼리)")
        
        # 쿼리 벡터 생성
        query_vectors = np.random.rand(num_queries, 768)
        
        # Qdrant 벤치마크
        qdrant_times = []
        for vector in query_vectors:
            start_time = time.time()
            
            response = requests.post(
                f"{self.qdrant_url}/collections/{self.collection_name}/points/search",
                json={"vector": vector.tolist(), "limit": 10}
            )
            
            if response.status_code == 200:
                qdrant_times.append(time.time() - start_time)
        
        # FAISS 벤치마크
        faiss_times = []
        for vector in query_vectors:
            start_time = time.time()
            
            response = requests.post(
                f"{self.faiss_url}/search",
                json={"vector": vector.tolist(), "k": 10}
            )
            
            if response.status_code == 200:
                faiss_times.append(time.time() - start_time)
        
        # 결과 출력
        print(f"Qdrant 평균 지연시간: {statistics.mean(qdrant_times)*1000:.2f}ms")
        print(f"FAISS GPU 평균 지연시간: {statistics.mean(faiss_times)*1000:.2f}ms")
        print(f"속도 향상: {statistics.mean(qdrant_times)/statistics.mean(faiss_times):.2f}배")
    
    def benchmark_throughput(self, duration=30):
        """처리량 벤치마크"""
        print(f"\n처리량 벤치마크 ({duration}초간)")
        
        query_vector = np.random.rand(768).tolist()
        
        # Qdrant 처리량 테스트
        print("Qdrant 처리량 측정 중...")
        qdrant_count = 0
        start_time = time.time()
        
        while time.time() - start_time < duration:
            response = requests.post(
                f"{self.qdrant_url}/collections/{self.collection_name}/points/search",
                json={"vector": query_vector, "limit": 10}
            )
            if response.status_code == 200:
                qdrant_count += 1
        
        qdrant_qps = qdrant_count / duration
        
        # FAISS 처리량 테스트
        print("FAISS GPU 처리량 측정 중...")
        faiss_count = 0
        start_time = time.time()
        
        while time.time() - start_time < duration:
            response = requests.post(
                f"{self.faiss_url}/search",
                json={"vector": query_vector, "k": 10}
            )
            if response.status_code == 200:
                faiss_count += 1
        
        faiss_qps = faiss_count / duration
        
        # 결과 출력
        print(f"Qdrant QPS: {qdrant_qps:.2f}")
        print(f"FAISS GPU QPS: {faiss_qps:.2f}")
        print(f"처리량 향상: {faiss_qps/qdrant_qps:.2f}배")
    
    def benchmark_concurrent(self, num_threads=10, queries_per_thread=20):
        """동시 처리 벤치마크"""
        print(f"\n동시 처리 벤치마크 ({num_threads}개 스레드, 스레드당 {queries_per_thread}회)")
        
        query_vector = np.random.rand(768).tolist()
        
        def qdrant_worker():
            times = []
            for _ in range(queries_per_thread):
                start_time = time.time()
                response = requests.post(
                    f"{self.qdrant_url}/collections/{self.collection_name}/points/search",
                    json={"vector": query_vector, "limit": 10}
                )
                if response.status_code == 200:
                    times.append(time.time() - start_time)
            return times
        
        def faiss_worker():
            times = []
            for _ in range(queries_per_thread):
                start_time = time.time()
                response = requests.post(
                    f"{self.faiss_url}/search",
                    json={"vector": query_vector, "k": 10}
                )
                if response.status_code == 200:
                    times.append(time.time() - start_time)
            return times
        
        # Qdrant 동시 처리
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            qdrant_results = list(executor.map(lambda _: qdrant_worker(), range(num_threads)))
        qdrant_total_time = time.time() - start_time
        
        # FAISS 동시 처리
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=num_threads) as executor:
            faiss_results = list(executor.map(lambda _: faiss_worker(), range(num_threads)))
        faiss_total_time = time.time() - start_time
        
        # 결과 계산
        qdrant_times = [t for times in qdrant_results for t in times]
        faiss_times = [t for times in faiss_results for t in times]
        
        print(f"Qdrant 총 처리시간: {qdrant_total_time:.2f}초")
        print(f"FAISS GPU 총 처리시간: {faiss_total_time:.2f}초")
        print(f"Qdrant 평균 응답시간: {statistics.mean(qdrant_times)*1000:.2f}ms")
        print(f"FAISS GPU 평균 응답시간: {statistics.mean(fais