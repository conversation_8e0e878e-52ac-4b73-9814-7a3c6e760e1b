import faiss
import numpy as np

# 벡터 차원
dim = 128

# 샘플 데이터 생성 (10000개의 128차원 벡터)
nb = 10000
np.random.seed(123)
xb = np.random.random((nb, dim)).astype('float32')

# 쿼리 벡터 (5개)
nq = 5
xq = np.random.random((nq, dim)).astype('float32')

# GPU 리소스 초기화
res = faiss.StandardGpuResources()

# CPU index 생성 후 GPU로 전송
cpu_index = faiss.IndexFlatL2(dim)
gpu_index = faiss.index_cpu_to_gpu(res, 0, cpu_index)

# 인덱스에 벡터 추가
gpu_index.add(xb)

# 검색 (쿼리 벡터에 대해 상위 5개 검색)
k = 5
D, I = gpu_index.search(xq, k)

# 결과 출력
print("쿼리 결과 (Top 5)")
for i in range(nq):
    print(f"Query {i+1}:")
    print(f"  Top {k} indices: {I[i]}")
    print(f"  Distances: {D[i]}")
