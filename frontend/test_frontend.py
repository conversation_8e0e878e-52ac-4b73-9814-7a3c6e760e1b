#!/usr/bin/env python3
"""
Frontend 서비스 테스트 스크립트
React + Rsbuild 프론트엔드와 백엔드 연동을 확인합니다.
"""

import requests
import json
import time
import sys
from typing import Dict, Any

# 서비스 엔드포인트
FRONTEND_URL = "http://localhost:7103"

def test_frontend_health() -> bool:
    """Frontend 서비스 기본 테스트"""
    print("🔍 Frontend 서비스 기본 테스트 중...")
    
    try:
        # HTML 페이지 로드 테스트
        response = requests.get(f"{FRONTEND_URL}/", timeout=10)
        if response.status_code == 200:
            if "CodeBase Intelligence" in response.text:
                print("✅ Frontend HTML 페이지 로드 성공")
            else:
                print("❌ Frontend HTML 내용 확인 실패")
                return False
        else:
            print(f"❌ Frontend 페이지 로드 실패: {response.status_code}")
            return False
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Frontend 연결 실패: {e}")
        return False

def test_api_proxy() -> bool:
    """API 프록시 테스트"""
    print("\n🔍 API 프록시 테스트 중...")
    
    try:
        # Backend API 프록시 테스트
        response = requests.get(f"{FRONTEND_URL}/api/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            if health_data.get("success") and health_data.get("data", {}).get("status") == "ok":
                print("✅ Backend API 프록시 성공")
            else:
                print(f"❌ Backend API 프록시 응답 이상: {health_data}")
                return False
        else:
            print(f"❌ Backend API 프록시 실패: {response.status_code}")
            return False
            
        # Repositories API 프록시 테스트
        response = requests.get(f"{FRONTEND_URL}/api/repositories", timeout=10)
        if response.status_code == 200:
            repos_data = response.json()
            if repos_data.get("success") and "pagination" in repos_data.get("data", {}):
                print("✅ Repositories API 프록시 성공")
            else:
                print(f"❌ Repositories API 프록시 응답 이상: {repos_data}")
                return False
        else:
            print(f"❌ Repositories API 프록시 실패: {response.status_code}")
            return False
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API 프록시 연결 실패: {e}")
        return False

def test_static_assets() -> bool:
    """정적 자산 로딩 테스트"""
    print("\n🔍 정적 자산 로딩 테스트 중...")
    
    try:
        # CSS 파일 테스트
        response = requests.get(f"{FRONTEND_URL}/", timeout=5)
        if response.status_code == 200:
            html_content = response.text
            if "/static/css/" in html_content:
                print("✅ CSS 파일 링크 확인")
            else:
                print("ℹ️ CSS 파일 링크 없음 (정상일 수 있음)")
                
            if "/static/js/" in html_content:
                print("✅ JavaScript 파일 링크 확인")
            else:
                print("ℹ️ JavaScript 파일 링크 없음 (정상일 수 있음)")
                
        # Favicon 테스트
        response = requests.get(f"{FRONTEND_URL}/favicon.ico", timeout=5)
        if response.status_code == 200:
            print("✅ Favicon 로드 성공")
        else:
            print("ℹ️ Favicon 로드 실패 (정상일 수 있음)")
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 정적 자산 테스트 실패: {e}")
        return False

def test_cors_headers() -> bool:
    """CORS 헤더 테스트"""
    print("\n🔍 CORS 헤더 테스트 중...")
    
    try:
        response = requests.get(f"{FRONTEND_URL}/api/health", timeout=5)
        if response.status_code == 200:
            headers = response.headers
            print("✅ API 프록시 응답 헤더 확인:")
            print(f"   Content-Type: {headers.get('Content-Type', 'N/A')}")
            print(f"   Server: {headers.get('Server', 'N/A')}")
            return True
        else:
            print(f"❌ CORS 헤더 테스트 실패: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ CORS 헤더 테스트 실패: {e}")
        return False

def test_development_features() -> bool:
    """개발 환경 기능 테스트"""
    print("\n🔍 개발 환경 기능 테스트 중...")
    
    try:
        # 개발 서버 응답 확인
        response = requests.get(f"{FRONTEND_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ 개발 서버 정상 응답")
            
            # Hot reload 관련 스크립트 확인
            if "rsbuild" in response.text.lower() or "webpack" in response.text.lower():
                print("✅ 개발 환경 스크립트 확인")
            else:
                print("ℹ️ 개발 환경 스크립트 미확인 (정상일 수 있음)")
                
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 개발 환경 기능 테스트 실패: {e}")
        return False

def test_network_connectivity() -> bool:
    """네트워크 연결성 테스트"""
    print("\n🔍 네트워크 연결성 테스트 중...")
    
    endpoints_to_test = [
        ("/", "Frontend root"),
        ("/api/health", "Backend health via proxy"),
        ("/api/repositories", "Backend repositories via proxy"),
    ]
    
    success_count = 0
    
    for endpoint, description in endpoints_to_test:
        try:
            response = requests.get(f"{FRONTEND_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {description}: 200 OK")
                success_count += 1
            else:
                print(f"❌ {description}: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {description}: 연결 실패 - {e}")
    
    if success_count == len(endpoints_to_test):
        print(f"✅ 모든 네트워크 연결 테스트 성공 ({success_count}/{len(endpoints_to_test)})")
        return True
    else:
        print(f"❌ 일부 네트워크 연결 테스트 실패 ({success_count}/{len(endpoints_to_test)})")
        return False

def main():
    """메인 테스트 함수"""
    print("🚀 Frontend 서비스 테스트 시작\n")
    
    # 서비스 준비 대기
    print("⏳ 서비스 준비 대기 중...")
    time.sleep(2)
    
    results = []
    
    # 테스트 실행
    results.append(("Frontend Health", test_frontend_health()))
    results.append(("API Proxy", test_api_proxy()))
    results.append(("Static Assets", test_static_assets()))
    results.append(("CORS Headers", test_cors_headers()))
    results.append(("Development Features", test_development_features()))
    results.append(("Network Connectivity", test_network_connectivity()))
    
    # 결과 요약
    print("\n" + "="*50)
    print("📊 테스트 결과 요약")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:20} : {status}")
        if not passed:
            all_passed = False
    
    print("="*50)
    if all_passed:
        print("🎉 모든 테스트 통과!")
        print("✨ Frontend 서비스가 정상적으로 작동하고 있습니다!")
        print(f"🌐 Frontend URL: {FRONTEND_URL}")
        print(f"🔗 API Proxy: {FRONTEND_URL}/api/*")
        print("📱 리모트 접근: https://gserver.parrot-mine.ts.net:7103")
        sys.exit(0)
    else:
        print("💥 일부 테스트 실패")
        sys.exit(1)

if __name__ == "__main__":
    main()
