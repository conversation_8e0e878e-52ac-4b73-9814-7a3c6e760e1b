{"name": "codebase-intelligence-frontend", "version": "0.1.0", "description": "CodeBase Intelligence System Frontend - React + RSBuild + TanStack", "private": true, "type": "module", "scripts": {"dev": "rsbuild dev", "build": "rsbuild build", "preview": "rsbuild preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "clean": "rm -rf dist"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.81.5", "@tanstack/react-router": "^1.58.3", "@tanstack/router-plugin": "^1.58.4", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highlight.js": "^11.11.1", "lucide-react": "^0.446.0", "monaco-editor": "^0.52.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.30.1", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "yup": "^1.6.1", "zod": "^3.23.8", "zustand": "^4.5.5"}, "devDependencies": {"@rsbuild/core": "^1.0.11", "@rsbuild/plugin-react": "^1.0.3", "@rsbuild/plugin-sass": "^1.0.1", "@tailwindcss/postcss": "^4.0.0-alpha.25", "@tanstack/router-devtools": "^1.125.1", "@types/node": "^22.7.4", "@types/react": "^18.3.8", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-plugin-react": "^7.36.1", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.4.47", "prettier": "^3.3.3", "sass": "^1.79.3", "tailwindcss": "^3.4.12", "typescript": "^5.6.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}