# VectorDB 자연어 검색 기능

## 개요

VectorDB Dashboard에 AI 기반 자연어 검색 기능을 추가했습니다. 이 기능을 통해 사용자는 자연어로 코드베이스를 검색하고 질문할 수 있습니다.

## 주요 기능

### 1. 자연어 질문 (Natural Language Query)
- **기능**: 사용자가 자연어로 질문하면 AI가 답변을 생성
- **API**: `POST /aiend/api/v1/search/query`
- **예시**: "사용자 인증은 어떻게 구현되어 있나요?"
- **특징**: 
  - RAG(Retrieval-Augmented Generation) 파이프라인 사용
  - 관련 코드 스니펫과 함께 답변 제공
  - Repository context 지원

### 2. 고급 코드 검색 (Advanced Code Search)
- **기능**: 다양한 검색 모드를 사용한 코드베이스 검색
- **API**: `POST /aiend/api/v1/search/`
- **검색 모드**: 
  - `auto`: 자동 모드 선택
  - `semantic`: 의미론적 검색
  - `structural`: 구조적 검색
  - `temporal`: 시간적 검색
  - `hybrid`: 하이브리드 검색

### 3. 검색 결과 개선 기능
- **정렬 옵션**: Score, Relevance, File Path
- **필터링**: Context Type별 필터링
- **리랭킹**: 다중 요소 기반 관련성 점수 계산
- **상호작용**: 결과 확장/축소, 클립보드 복사

## 구현된 컴포넌트

### 1. `NaturalLanguageSearch.tsx`
- 메인 자연어 검색 인터페이스
- 두 가지 검색 모드 (자연어 질문 / 고급 검색) 제공
- 검색 제안 기능
- Repository context 통합

### 2. `SearchResultsDisplay.tsx`
- 검색 결과 표시 전용 컴포넌트
- 고급 정렬 및 필터링 기능
- 리랭킹 알고리즘 적용
- 사용자 친화적 UI

### 3. `aiendService.ts`
- aiend API와의 통신을 담당하는 서비스 클래스
- 모든 aiend API 엔드포인트 지원
- TypeScript 타입 정의 포함

## 리랭킹 알고리즘

검색 결과의 관련성을 높이기 위해 다음 요소들을 고려한 리랭킹을 적용합니다:

1. **Context Type 가중치**:
   - function: 1.2x
   - method: 1.15x
   - class: 1.1x
   - interface: 1.05x

2. **파일 타입 가중치**:
   - TypeScript (.ts, .tsx): 1.1x
   - JavaScript (.js, .jsx): 1.05x
   - Python (.py): 1.1x
   - Ruby (.rb): 1.05x

3. **콘텐츠 길이 최적화**:
   - 100-1000자: 1.1x 가중치
   - 1000자 이상: 1.05x 가중치

## 프록시 설정

### 개발 환경 (rsbuild.config.ts)
```typescript
'/aiend': {
  target: 'http://gserver.parrot-mine.ts.net:7102',
  changeOrigin: true,
  pathRewrite: {
    '^/aiend': '',
  },
}
```

### 프로덕션 환경 (nginx.conf)
```nginx
location /aiend/ {
    proxy_pass http://aiend:8000/;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_cache_bypass $http_upgrade;
}
```

## 사용 방법

1. **VectorDB 메뉴 접근**: 사이드바에서 VectorDB 클릭
2. **자연어 검색 탭**: 기본적으로 "자연어 검색" 탭이 활성화됨
3. **질문 입력**: 텍스트 영역에 자연어로 질문 입력
4. **검색 실행**: 
   - "AI에게 질문하기" 버튼 클릭 (자연어 질문)
   - "코드 검색" 버튼 클릭 (고급 검색)
   - Ctrl+Enter 단축키 사용
5. **결과 확인**: AI 답변 및 관련 코드 스니펫 확인
6. **결과 조작**: 정렬, 필터링, 확장/축소, 복사 등

## 테스트

`test_aiend_integration.js` 파일을 사용하여 aiend API 통합을 테스트할 수 있습니다:

```javascript
// 브라우저 콘솔에서 실행
window.aiendTests.runAllTests()
```

## 향후 개선 사항

1. **검색 히스토리**: 이전 검색 기록 저장 및 재사용
2. **북마크 기능**: 유용한 검색 결과 북마크
3. **고급 필터**: 파일 타입, 날짜 범위 등 추가 필터
4. **검색 분석**: 검색 패턴 분석 및 개선 제안
5. **실시간 제안**: 타이핑하는 동안 실시간 검색 제안

## 기술 스택

- **Frontend**: React, TypeScript, Tailwind CSS
- **UI Components**: Radix UI
- **API Communication**: Axios
- **State Management**: Zustand
- **Backend**: aiend API (FastAPI)
- **Vector Database**: Qdrant
- **AI Models**: Ollama (configurable)
