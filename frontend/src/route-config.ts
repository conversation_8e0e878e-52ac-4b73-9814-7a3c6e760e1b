import { createRouter } from '@tanstack/react-router';

import { type AuthContextType } from '@/contexts/AuthContext';

import { routeTree } from './routeTree.gen';

// Create the router instance
export const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  context: {
    auth: undefined! as AuthContextType,
  },
});

// Register the router instance for type safety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}
