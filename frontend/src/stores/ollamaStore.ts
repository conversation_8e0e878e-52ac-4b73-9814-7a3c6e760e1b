import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { OLLAMA_CONFIG } from '@/constants';
import { ollamaApi } from '@/services/api';
import { OllamaModel } from '@/types/ollama';

interface OllamaState {
  // 현재 선택된 모델
  selectedModel: string;

  // 사용 가능한 모델 목록
  availableModels: OllamaModel[];
  modelsLoading: boolean;
  modelsError: string | null;

  // Ollama 서비스 상태
  serviceStatus: 'available' | 'unavailable' | 'error' | 'unknown';

  // Actions
  setSelectedModel: (model: string) => void;
  setAvailableModels: (models: OllamaModel[]) => void;
  setModelsLoading: (loading: boolean) => void;
  setModelsError: (error: string | null) => void;
  setServiceStatus: (
    status: 'available' | 'unavailable' | 'error' | 'unknown'
  ) => void;

  // New action to fetch models
  fetchAvailableModels: () => Promise<void>;

  // Getters
  getSelectedModel: () => string;
  isModelAvailable: (modelName: string) => boolean;
}

export const useOllamaStore = create<OllamaState>()(
  persist(
    (set, get) => ({
      // Initial state
      selectedModel: OLLAMA_CONFIG.DEFAULT_MODEL,
      availableModels: [],
      modelsLoading: false,
      modelsError: null,
      serviceStatus: 'unknown',

      // Actions
      setSelectedModel: (model: string) => {
        set({ selectedModel: model });
      },

      setAvailableModels: (models: OllamaModel[]) => {
        set({
          availableModels: models,
          modelsError: null,
          serviceStatus: 'available',
        });
      },

      setModelsLoading: (loading: boolean) => {
        set({ modelsLoading: loading });
      },

      setModelsError: (error: string | null) => {
        set({
          modelsError: error,
          modelsLoading: false,
          serviceStatus: error ? 'error' : 'unknown',
        });
      },

      setServiceStatus: (
        status: 'available' | 'unavailable' | 'error' | 'unknown'
      ) => {
        set({ serviceStatus: status });
      },

      // New action implementation
      fetchAvailableModels: async () => {
        set({ modelsLoading: true, modelsError: null });
        try {
          const response = await ollamaApi.getTags();
          const models = response.models;
          set({
            availableModels: models,
            modelsLoading: false,
            serviceStatus: 'available',
          });

          // Check if the currently selected model is still available
          const { selectedModel } = get();
          const isSelectedModelAvailable = models.some(
            m => m.name === selectedModel
          );

          // If not available, or if no model is selected, set to the first available model
          if (!isSelectedModelAvailable && models.length > 0) {
            set({ selectedModel: models[0].name });
          } else if (models.length === 0) {
            set({ selectedModel: '' }); // No models available
          }
        } catch (error) {
          console.error('Failed to fetch Ollama models:', error);
          const errorMessage =
            error instanceof Error ? error.message : 'Unknown error';
          set({
            modelsError: `Failed to fetch models: ${errorMessage}`,
            modelsLoading: false,
            serviceStatus: 'error',
            availableModels: [],
            selectedModel: '',
          });
        }
      },

      // Getters
      getSelectedModel: () => {
        return get().selectedModel;
      },

      isModelAvailable: (modelName: string) => {
        const { availableModels } = get();
        return availableModels.some(model => model.name === modelName);
      },
    }),
    {
      name: 'ollama-settings', // localStorage key
      partialize: state => ({
        selectedModel: state.selectedModel, // localStorage에 저장할 필드만 선택
      }),
    }
  )
);
