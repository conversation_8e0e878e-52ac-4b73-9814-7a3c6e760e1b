import { create } from 'zustand';
import {
  Repository,
  RepositoryBranchAnalysis,
  RepositoryContext,
} from '@/types';

interface RepositoryState {
  // Repositories list
  repositories: Repository[];
  repositoriesLoading: boolean;
  repositoriesError: string | null;

  // Branch analyses - 브랜치별 분석 상태
  branchAnalyses: RepositoryBranchAnalysis[];
  branchAnalysesLoading: boolean;

  // Current selection context
  context: RepositoryContext;

  // Actions for repositories
  setRepositories: (repositories: Repository[]) => void;
  setRepositoriesLoading: (loading: boolean) => void;
  setRepositoriesError: (error: string | null) => void;

  // Actions for branch analyses
  setBranchAnalyses: (analyses: RepositoryBranchAnalysis[]) => void;
  setBranchAnalysesLoading: (loading: boolean) => void;
  updateBranchAnalysis: (analysis: RepositoryBranchAnalysis) => void;
  addBranchAnalysis: (analysis: RepositoryBranchAnalysis) => void;

  // Actions for current context
  setRepositoryContext: (
    repositoryId: number,
    repositoryName: string,
    branch: string
  ) => void;
  clearContext: () => void;

  // Convenience getters
  hasValidContext: () => boolean;
  getCurrentRepository: () => Repository | null;
  getCurrentBranchAnalysis: () => RepositoryBranchAnalysis | null;
  getReadyRepositories: () => Repository[];
  getBranchAnalysesForRepository: (
    repositoryId: number
  ) => RepositoryBranchAnalysis[];
  getAnalyzedBranches: (repositoryId: number) => string[];

  // Helper functions
  generateAnalysisId: (repositoryId: number, branch: string) => string;
  generateCollectionName: (repositoryId: number, branch: string) => string;
}

export const useRepositoryStore = create<RepositoryState>((set, get) => ({
  // Initial state
  repositories: [],
  repositoriesLoading: false,
  repositoriesError: null,
  branchAnalyses: [],
  branchAnalysesLoading: false,
  context: {
    repository_id: null,
    repository_name: null,
    branch: null,
    analysis_id: null,
    collection_name: null,
  },

  // Helper functions
  generateAnalysisId: (repositoryId: number, branch: string) => {
    return `${repositoryId}_${branch}`;
  },

  generateCollectionName: (repositoryId: number, branch: string) => {
    // 저장소 이름을 가져오기 위해 현재 상태에서 저장소 검색
    const state = get();
    const repository = state.repositories.find(
      repo => repo.id === repositoryId
    );
    const repoName = repository?.name || `unknown_repo_${repositoryId}`;

    // 브랜치 이름에서 특수문자 제거
    const safeBranch = branch.replace(/[^a-zA-Z0-9]/g, '_');

    // {repository_name}_{branch}_{repository_id} 형식으로 반환
    return `${repoName}_${safeBranch}_${repositoryId}`;
  },

  // Actions for repositories
  setRepositories: repositories =>
    set({ repositories, repositoriesError: null }),
  setRepositoriesLoading: loading => set({ repositoriesLoading: loading }),
  setRepositoriesError: error =>
    set({ repositoriesError: error, repositoriesLoading: false }),

  // Actions for branch analyses
  setBranchAnalyses: analyses => set({ branchAnalyses: analyses }),
  setBranchAnalysesLoading: loading => set({ branchAnalysesLoading: loading }),

  updateBranchAnalysis: analysis =>
    set(state => ({
      branchAnalyses: state.branchAnalyses.map(existing =>
        existing.id === analysis.id ? analysis : existing
      ),
    })),

  addBranchAnalysis: analysis =>
    set(state => ({
      branchAnalyses: [
        ...state.branchAnalyses.filter(existing => existing.id !== analysis.id),
        analysis,
      ],
    })),

  // Actions for current context
  setRepositoryContext: (
    repositoryId: number,
    repositoryName: string,
    branch: string
  ) => {
    const { generateAnalysisId, generateCollectionName } = get();
    const analysisId = generateAnalysisId(repositoryId, branch);
    const collectionName = generateCollectionName(repositoryId, branch);

    set({
      context: {
        repository_id: repositoryId,
        repository_name: repositoryName,
        branch: branch,
        analysis_id: analysisId,
        collection_name: collectionName,
      },
    });
  },

  clearContext: () =>
    set({
      context: {
        repository_id: null,
        repository_name: null,
        branch: null,
        analysis_id: null,
        collection_name: null,
      },
    }),

  // Convenience getters
  hasValidContext: () => {
    const state = get();
    return !!(
      state.context.repository_id &&
      state.context.repository_name &&
      state.context.branch
    );
  },

  getCurrentRepository: () => {
    const state = get();
    return (
      state.repositories.find(
        repo => repo.id === state.context.repository_id
      ) || null
    );
  },

  getCurrentBranchAnalysis: () => {
    const state = get();
    if (!state.context.analysis_id) return null;
    return (
      state.branchAnalyses.find(
        analysis => analysis.id === state.context.analysis_id
      ) || null
    );
  },

  getReadyRepositories: () => {
    const state = get();
    return state.repositories.filter(repo => repo.status === 'ready');
  },

  getBranchAnalysesForRepository: (repositoryId: number) => {
    const state = get();
    return state.branchAnalyses.filter(
      analysis => analysis.repository_id === repositoryId
    );
  },

  getAnalyzedBranches: (repositoryId: number) => {
    const state = get();
    return state.branchAnalyses
      .filter(
        analysis =>
          analysis.repository_id === repositoryId &&
          analysis.status === 'completed'
      )
      .map(analysis => analysis.branch);
  },
}));
