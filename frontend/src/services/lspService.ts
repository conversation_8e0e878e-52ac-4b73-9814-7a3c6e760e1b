import { apiService } from './api';

// LSP API 타입 정의
export interface Position {
  line: number;
  character: number;
}

export interface Range {
  start: Position;
  end: Position;
}

export interface SymbolDefinition {
  name: string;
  kind: string;
  filePath: string;
  position: Range;
}

export interface SymbolReference {
  filePath: string;
  position: Range;
}

export interface FileSymbol {
  name: string;
  kind: string;
  position: Range;
}

export interface AnalysisRequest {
  path: string;
  repositoryFolderName: string;
  recursive?: boolean;
}

export interface RepositoryAnalysisRequest {
  repositoryFolderName: string;
  recursive?: boolean;
}

export interface AnalysisResponse {
  status: 'ok' | 'error';
  message: string;
  symbolsFound?: number;
  referencesFound?: number;
  repositoryFolderName?: string;
  repositoryPath?: string;
}

export interface HealthResponse {
  status: string;
  message: string;
}

export interface RepositoryInfo {
  folderName: string;
  displayName: string;
  path: string;
  lastModified: string;
  size: number;
  fileCount?: number;
  symbolCount?: number;
}

export interface Statistics {
  totalSymbols: number;
  totalReferences: number;
  symbolsByKind: Record<string, number>;
  filesCovered: number;
}

export interface LSPDefinitionRequest {
  filePath: string;
  line: number;
  character: number;
  fileContent: string;
}

export interface LSPReferencesRequest {
  filePath: string;
  line: number;
  character: number;
  fileContent: string;
}

export interface LSPCompletionRequest {
  filePath: string;
  line: number;
  character: number;
  fileContent: string;
}

// LSP API 서비스 클래스
class LSPService {
  private baseUrl = '/lsp/api/v1';

  // Health Check
  async healthCheck(): Promise<HealthResponse> {
    return apiService.get(`${this.baseUrl}/health`);
  }

  // Analysis APIs
  async scanDirectory(request: AnalysisRequest): Promise<AnalysisResponse> {
    return apiService.post(`${this.baseUrl}/analysis/scan`, request);
  }

  async scanRepository(
    request: RepositoryAnalysisRequest
  ): Promise<AnalysisResponse> {
    return apiService.post(`${this.baseUrl}/analysis/scan-repository`, request);
  }

  // Symbols APIs
  async getSymbolDefinition(
    name: string,
    repositoryFolderName: string,
    filePath?: string
  ): Promise<SymbolDefinition> {
    const params = new URLSearchParams({
      name,
      repositoryFolderName,
      ...(filePath && { filePath }),
    });
    return apiService.get(
      `${this.baseUrl}/symbols/definition?${params.toString()}`
    );
  }

  async getSymbolReferences(
    repositoryFolderName: string,
    name?: string,
    symbolId?: number
  ): Promise<SymbolReference[]> {
    const params = new URLSearchParams({
      repositoryFolderName,
      ...(name && { name }),
      ...(symbolId && { symbolId: symbolId.toString() }),
    });
    return apiService.get(
      `${this.baseUrl}/symbols/references?${params.toString()}`
    );
  }

  // Files APIs
  async getFileSymbols(
    path: string,
    repositoryFolderName: string
  ): Promise<FileSymbol[]> {
    const params = new URLSearchParams({
      path,
      repositoryFolderName,
    });
    return apiService.get(`${this.baseUrl}/files/symbols?${params.toString()}`);
  }

  async getFileAST(path: string): Promise<{ ast: any }> {
    const encodedPath = encodeURIComponent(path);
    return apiService.get(`${this.baseUrl}/files/${encodedPath}/ast`);
  }

  // Statistics API
  async getStatistics(): Promise<Statistics> {
    return apiService.get(`${this.baseUrl}/stats`);
  }

  // Repositories APIs
  async getRepositories(): Promise<RepositoryInfo[]> {
    return apiService.get(`${this.baseUrl}/repositories`);
  }

  async getRepositoryInfo(folderName: string): Promise<RepositoryInfo> {
    const encodedFolderName = encodeURIComponent(folderName);
    return apiService.get(`${this.baseUrl}/repositories/${encodedFolderName}`);
  }

  async deleteRepository(
    folderName: string
  ): Promise<{
    status: string;
    message: string;
    deletedSymbols: boolean;
    deletedReferences: boolean;
  }> {
    const encodedFolderName = encodeURIComponent(folderName);
    return apiService.delete(
      `${this.baseUrl}/repositories/${encodedFolderName}`
    );
  }

  // LSP Protocol APIs (LSP 클라이언트 모드에서만 사용 가능)
  async getLSPDefinition(
    request: LSPDefinitionRequest
  ): Promise<SymbolDefinition[]> {
    return apiService.post(`${this.baseUrl}/lsp/definition`, request);
  }

  async getLSPReferences(
    request: LSPReferencesRequest
  ): Promise<SymbolReference[]> {
    return apiService.post(`${this.baseUrl}/lsp/references`, request);
  }

  async getLSPCompletion(request: LSPCompletionRequest): Promise<any> {
    return apiService.post(`${this.baseUrl}/lsp/completion`, request);
  }

  // 편의 메서드: 리포지토리 이름과 브랜치로 폴더명 생성
  generateRepositoryFolderName(
    repositoryName: string,
    repositoryId: number
  ): string {
    return `${repositoryName}_${repositoryId}`;
  }

  // 편의 메서드: 리포지토리 스캔 (이름과 ID 기반)
  async scanRepositoryByNameAndId(
    repositoryName: string,
    repositoryId: number,
    recursive: boolean = true
  ): Promise<AnalysisResponse> {
    const repositoryFolderName = this.generateRepositoryFolderName(
      repositoryName,
      repositoryId
    );
    return this.scanRepository({ repositoryFolderName, recursive });
  }
}

export const lspService = new LSPService();
export default lspService;
