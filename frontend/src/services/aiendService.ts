import axios from 'axios';

// aiend API 기본 URL (프록시 사용)
const AIEND_API_BASE_URL = '/ai';

// 기본 API 인스턴스 생성
const api = axios.create({
  baseURL: AIEND_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Search API 관련 타입 정의
export interface QueryRequest {
  query: string;
  repository_id?: string;
  max_results?: number;
}

export interface QueryResponse {
  answer: string;
  search_results: SearchResultResponse[];
  execution_time: number;
}

export interface SearchRequest {
  query: string;
  mode?: 'auto' | 'semantic' | 'structural' | 'temporal' | 'hybrid';
  context_types?: string[];
  file_patterns?: string[];
  max_results?: number;
  include_history?: boolean;
}

export interface SearchResponse {
  results: SearchResultResponse[];
  total_count: number;
  query: string;
  mode: string;
  execution_time: number;
  suggestions: string[];
}

export interface SearchResultResponse {
  id: string;
  score: number;
  content: string;
  file_path: string;
  line_start?: number;
  line_end?: number;
  context_type?: string;
  metadata?: Record<string, any>;
}

export interface EncodeRequest {
  text: string;
  repository_id?: string;
}

export interface EncodeResponse {
  vector: number[];
  model: string;
}

export interface VectorStatusResponse {
  available: boolean;
  collection_info?: any;
  host: string;
  port: number;
  collection_name: string;
  model_name: string;
}

// LLM API 관련 타입 정의
export interface ChatRequest {
  message: string;
  max_tokens?: number;
  provider?: string;
  model?: string;
  strategy?: string;
  save_context?: boolean;
}

export interface ChatResponse {
  content: string;
  confidence: number;
  strategy_used: string;
  execution_time: number;
  context_tokens: number;
  providers_used: string[];
  suggestions: string[];
  context_id?: string;
}

// Analysis API 관련 타입 정의
export interface IndexRequest {
  force_reindex?: boolean;
  languages?: string[];
  file_patterns?: string[];
}

export interface IndexResponse {
  task_id: string;
  status: string;
  message: string;
}

export interface StatsResponse {
  database: {
    files: number;
    functions: number;
    classes: number;
  };
  vector_store: {
    total_documents: number;
    collection_name: string;
  };
  repository: {
    path: string;
    total_files: number;
  };
}

// Repository Scan API 관련 타입 정의
export interface RepositoryScanRequest {
  repository_path: string;
  repository_name: string;
  repository_id: number | string; // 숫자 또는 문자열 모두 허용
  branch: string;
  force_reindex?: boolean;
}

export interface RepositoryScanResponse {
  success: boolean;
  message: string;
  task_id: string;
  processed_files?: number;
}

// aiend API 서비스 클래스
export const aiendService = {
  // === Search API ===

  // 자연어 쿼리 (RAG)
  async naturalLanguageQuery(request: QueryRequest): Promise<QueryResponse> {
    try {
      const response = await api.post('/search/query', request);
      return response.data;
    } catch (error) {
      console.error('Failed to execute natural language query:', error);
      throw error;
    }
  },

  // 코드베이스 검색
  async searchCodebase(request: SearchRequest): Promise<SearchResponse> {
    try {
      const response = await api.post('/search/', request);
      return response.data;
    } catch (error) {
      console.error('Failed to search codebase:', error);
      throw error;
    }
  },

  // 텍스트를 벡터로 임베딩
  async encodeText(request: EncodeRequest): Promise<EncodeResponse> {
    try {
      const response = await api.post('/search/encode', request);
      return response.data;
    } catch (error) {
      console.error('Failed to encode text:', error);
      throw error;
    }
  },

  // 검색 제안 가져오기
  async getSearchSuggestions(
    query: string,
    limit: number = 10
  ): Promise<string[]> {
    try {
      const response = await api.get('/search/suggestions', {
        params: { query, limit },
      });
      return response.data.suggestions || [];
    } catch (error) {
      console.error('Failed to get search suggestions:', error);
      throw error;
    }
  },

  // 검색 모드 목록 가져오기
  async getSearchModes(): Promise<string[]> {
    try {
      const response = await api.get('/search/modes');
      return response.data.modes || [];
    } catch (error) {
      console.error('Failed to get search modes:', error);
      throw error;
    }
  },

  // 컨텍스트 타입 목록 가져오기
  async getContextTypes(): Promise<string[]> {
    try {
      const response = await api.get('/search/context-types');
      return response.data.context_types || [];
    } catch (error) {
      console.error('Failed to get context types:', error);
      throw error;
    }
  },

  // 벡터 스토어 상태 확인
  async getVectorStoreStatus(
    repositoryId?: string
  ): Promise<VectorStatusResponse> {
    try {
      const params = repositoryId ? { repository_id: repositoryId } : {};
      const response = await api.get('/search/vector/status', { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get vector store status:', error);
      throw error;
    }
  },

  // === LLM API ===

  // LLM과 채팅
  async chatWithLLM(request: ChatRequest): Promise<ChatResponse> {
    try {
      const response = await api.post('/llm/chat', request);
      return response.data;
    } catch (error) {
      console.error('Failed to chat with LLM:', error);
      throw error;
    }
  },

  // === Analysis API ===

  // 코드베이스 인덱싱 시작
  async startIndexing(request: IndexRequest): Promise<IndexResponse> {
    try {
      const response = await api.post('/analysis/index', request);
      return response.data;
    } catch (error) {
      console.error('Failed to start indexing:', error);
      throw error;
    }
  },

  // 인덱싱 상태 확인
  async getIndexingStatus(taskId: string): Promise<any> {
    try {
      const response = await api.get(`/analysis/index/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get indexing status:', error);
      throw error;
    }
  },

  // 시스템 통계 가져오기
  async getSystemStats(): Promise<StatsResponse> {
    try {
      const response = await api.get('/analysis/stats');
      return response.data;
    } catch (error) {
      console.error('Failed to get system stats:', error);
      throw error;
    }
  },

  // === Repository Scan API ===

  // 리포지토리 스캔 트리거 (Backend API 사용)
  async triggerRepositoryScan(
    request: RepositoryScanRequest
  ): Promise<RepositoryScanResponse> {
    try {
      // Backend API를 통해 브랜치별 스캔 요청 (프록시 사용)
      console.log('[AiendService] 저장소 스캔 요청 시작:', request);

      // localhost 직접 호출 대신 프록시 사용
      const backendApi = axios.create({
        baseURL: '/api', // rsbuild.config.ts에 설정된 프록시 사용
        timeout: 60000,
      });

      // repository_id가 문자열이면 숫자로 변환
      const repositoryId =
        typeof request.repository_id === 'string'
          ? parseInt(request.repository_id, 10)
          : request.repository_id;

      // 요청 본문 없이 쿼리 파라미터만 사용
      // 백엔드에서 repository_path는 repository_id를 기반으로 결정하므로 전송하지 않음
      console.log(
        '[AiendService] API 호출:',
        `/repositories/${repositoryId}/aiend-branch-scan`
      );
      console.log('[AiendService] 쿼리 파라미터:', {
        branch: request.branch,
        force_reindex: request.force_reindex || false,
      });

      const response = await backendApi.post(
        `/repositories/${repositoryId}/aiend-branch-scan`,
        null,
        {
          params: {
            branch: request.branch,
            force_reindex: request.force_reindex || false,
          },
        }
      );

      console.log('[AiendService] 저장소 스캔 응답 성공:', response.data);

      return {
        success: response.data.success,
        message:
          response.data.message || 'Repository scan triggered successfully',
        task_id: response.data.data?.aiend_task?.task_id || 'unknown',
        processed_files: 0,
      };
    } catch (error) {
      console.error('[AiendService] 저장소 스캔 실패:', error);

      // 자세한 오류 정보 출력
      if (axios.isAxiosError(error)) {
        if (error.response) {
          // 서버가 응답을 보냈지만 오류 상태코드인 경우
          console.error('[AiendService] 서버 오류 응답:', {
            status: error.response.status,
            data: error.response.data,
            headers: error.response.headers,
          });
        } else if (error.request) {
          // 요청이 전송되었지만 응답을 받지 못한 경우
          console.error('[AiendService] 서버 응답 없음:', error.request);
        } else {
          // 요청 설정 중 오류가 발생한 경우
          console.error('[AiendService] 요청 오류:', error.message);
        }
        console.error('[AiendService] 요청 구성:', error.config);
      }

      throw error;
    }
  },

  // === Health API ===

  // 기본 헬스 체크
  async healthCheck(): Promise<{ status: string }> {
    try {
      const response = await api.get('/health/');
      return response.data;
    } catch (error) {
      console.error('Failed to perform health check:', error);
      throw error;
    }
  },

  // 상세 헬스 체크
  async detailedHealthCheck(): Promise<any> {
    try {
      const response = await api.get('/health/detailed');
      return response.data;
    } catch (error) {
      console.error('Failed to perform detailed health check:', error);
      throw error;
    }
  },
};

export default aiendService;
