import axios, {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  AxiosError,
} from 'axios';
import { Repository, ApiResponse, BranchesResponse } from '../types';
import { OllamaGenerateResponse, OllamaTagsResponse } from '../types/ollama';
import { lspService } from './lspService';

const API_BASE_URL = '';
const OLLAMA_API_BASE_URL = 'http://localhost:11434';

class ApiService {
  public api: AxiosInstance;
  public ollamaInstance: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    });

    this.ollamaInstance = axios.create({
      baseURL: OLLAMA_API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    this.api.interceptors.request.use(
      config => {
        // Add JWT token for authenticated requests
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }

        return config;
      },
      error => {
        return Promise.reject(error);
      }
    );

    this.api.interceptors.response.use(
      (response: AxiosResponse) => response,
      (error: AxiosError) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          // Redirect to login page, ensuring it doesn't cause a loop
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.get<T>(url, config);
    return response.data;
  }

  public async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.api.post<T>(url, data, config);
    return response.data;
  }

  public async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<T> {
    const response = await this.api.put<T>(url, data, config);
    return response.data;
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    const response = await this.api.delete<T>(url, config);
    return response.data;
  }
}

export const apiService = new ApiService();

// Auth API
export const authApi = {
  login: (data: any) => apiService.post('/api/login', data),
  logout: () => {
    // Remove stored tokens and call logout endpoint
    return apiService.delete('/api/logout');
  },
  getCurrentUser: () => apiService.get<any>('/api/users/me'),
};

// Helper function to map backend repository data to frontend format
const mapRepositoryData = (repo: any): Repository => ({
  ...repo,
  status: repo.clone_status || repo.status,
  currentBranch: repo.current_branch,
  lastIndexed: repo.last_indexed,
});

// Repository management methods
export const repositoryApi = {
  cloneRepository: (data: {
    url: string;
    username?: string;
    password?: string;
  }) => apiService.post('/api/repositories/clone', data),

  getRepositories: async (params?: { per_page?: number; page?: number }) => {
    const data = await apiService.get<any>('/api/repositories', {
      params: {
        per_page: params?.per_page || 100,
        page: params?.page || 1,
      },
    });
    if (data?.data?.repositories) {
      data.data.repositories = data.data.repositories.map(mapRepositoryData);
    }
    return data;
  },

  getBranches: (repositoryId: string) =>
    apiService.get<ApiResponse<BranchesResponse>>(
      `/api/repositories/${repositoryId}/branches`
    ),

  switchBranch: (repositoryId: string, branch: string) =>
    apiService.post(`/api/repositories/${repositoryId}/switch-branch`, {
      branch,
    }),

  pullRepository: (repositoryId: string) =>
    apiService.post(`/api/repositories/${repositoryId}/pull`),

  scanRepository: (repositoryId: string) =>
    apiService.post(`/api/repositories/${repositoryId}/scan`),

  getAiendStatus: (repositoryId: string) =>
    apiService.get(`/api/repositories/${repositoryId}/aiend-status`),

  triggerAiendScan: (repositoryId: string, forceReindex: boolean = false) =>
    apiService.post(`/api/repositories/${repositoryId}/aiend-scan`, {
      force_reindex: forceReindex,
    }),

  triggerRepositoryBranchIdScan: (
    repositoryBranchId: string,
    forceReindex: boolean = false
  ) => {
    const repoIdParts = repositoryBranchId.split('_');
    const repository_id = parseInt(repoIdParts.pop() || '0', 10);
    const branch = repoIdParts.pop() || '';
    const repository_name = repoIdParts.join('_');
    const path = `/app/repositories/${repository_name}_${repository_id}`;

    const payload = {
      repository_id,
      repository_name,
      branch,
      path,
      force_reindex: forceReindex,
    };

    console.log('[triggerRepositoryBranchIdScan] POST payload:', payload);
    return apiService.post(
      '/api/repositories/repository-branch-id-scan',
      payload
    );
  },

  getRepositoryStatus: async (repositoryId: string) => {
    const data = await apiService.get<any>(
      `/api/repositories/${repositoryId}/status`
    );
    if (data?.repository) {
      data.repository = mapRepositoryData(data.repository);
    }
    return data;
  },

  deleteRepository: (repositoryId: string) =>
    apiService.delete(`/api/repositories/${repositoryId}`),
};

// Git commit management methods
export const gitApi = {
  getCommits: (
    repositoryId: string,
    branch: string,
    params?: {
      page?: number;
      per_page?: number;
      since?: string;
      until?: string;
    }
  ) =>
    apiService.get(`/api/repositories/${repositoryId}/commits`, {
      params: { branch, ...params },
    }),

  getCommitDetail: (repositoryId: string, commitHash: string) =>
    apiService.get(`/api/repositories/${repositoryId}/commits/${commitHash}`),

  getFileContent: (
    repositoryId: string,
    commitHash: string,
    filePath: string
  ) =>
    apiService.get(
      `/api/repositories/${repositoryId}/commits/${commitHash}/files`,
      { params: { path: filePath } }
    ),

  getCommitFilesContent: (repositoryId: string, commitHash: string) =>
    apiService.get(
      `/api/repositories/${repositoryId}/commits/${commitHash}/files-content`
    ),

  getCommitDiff: (
    repositoryId: string,
    commitHash: string,
    options?: { format?: 'unified' | 'name-only' | 'stat'; context?: number }
  ) =>
    apiService.get(
      `/api/repositories/${repositoryId}/commits/${commitHash}/diff`,
      {
        params: options,
      }
    ),
};

// File Explorer API methods
export const fileExplorerApi = {
  getFileTree: (
    repositoryId: string,
    options?: {
      include_files?: boolean;
      max_depth?: number;
      lazy_load?: boolean;
    }
  ) =>
    apiService.get(`/api/explorer/repositories/${repositoryId}/tree`, {
      params: options,
    }),

  getDirectoryContents: (repositoryId: string, path: string) =>
    apiService.get(
      `/api/explorer/repositories/${repositoryId}/directory/${encodeURIComponent(path)}`
    ),

  getFileInfo: (repositoryId: string, fileId: string) =>
    apiService.get(`/api/explorer/repositories/${repositoryId}/file/${fileId}`),

  getFileContent: (repositoryId: string, fileId: string) =>
    apiService.get(
      `/api/explorer/repositories/${repositoryId}/file/${fileId}/content`
    ),

  updateFileContent: (repositoryId: string, fileId: string, content: string) =>
    apiService.put(
      `/api/explorer/repositories/${repositoryId}/file/${fileId}/content`,
      {
        content,
      }
    ),

  searchFiles: (
    repositoryId: string,
    query: string,
    type?: 'content' | 'filename' | 'path' | 'all'
  ) =>
    apiService.get(`/api/explorer/repositories/${repositoryId}/search`, {
      params: { q: query, type: type || 'all' },
    }),
};

// Indexing API response types
interface IndexResponse {
  message: string;
  job_id: string;
}

interface IndexingStatus {
  status: 'completed' | 'working' | 'failed' | 'error';
  progress: number;
  message: string;
}

// Codebase API methods
export const codebaseApi = {
  indexByName: (repositoryName: string, branch: string) =>
    apiService.post<IndexResponse>('/api/codebase/index_by_name', {
      repository_name: repositoryName,
      branch,
    }),

  getIndexingStatus: (jobId: string) =>
    apiService.get<IndexingStatus>(`/api/codebase/indexing_status/${jobId}`),

  sqlQuery: (query: string) =>
    apiService.post('/api/codebase/sql_query', { sql: query }),

  getFiles: (repositoryName: string, branch: string, path?: string) => {
    const params = new URLSearchParams({
      repository_name: repositoryName,
      branch,
      ...(path && { path }),
    });
    return apiService.get(`/api/codebase/files?${params.toString()}`);
  },
};

// Ollama API methods
let ollamaInstance = axios.create();

export const updateOllamaBaseUrl = (url: string) => {
  ollamaInstance = axios.create({
    baseURL: url,
    headers: {
      'Content-Type': 'application/json',
    },
  });
};

// Initialize with a default or stored URL
// This will be properly initialized by the store
updateOllamaBaseUrl('http://localhost:11434');

export const ollamaApi = {
  // 텍스트 생성 (채팅 완료)
  generateText: async (model: string, prompt: string, options?: any): Promise<OllamaGenerateResponse> => {
    try {
      const response = await ollamaInstance.post('/api/generate', {
        model,
        prompt,
        stream: false,
        options: {
          temperature: 0.3,
          top_p: 0.9,
          top_k: 40,
          ...options,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error in generateText:', error);
      throw error;
    }
  },

  // 채팅 완료 (대화형)
  chatCompletion: (
    model: string,
    messages: Array<{ role: string; content: string }>,
    options?: any
  ) =>
    ollamaInstance.post('/api/chat', {
      model,
      messages,
      stream: false,
      options: {
        temperature: 0.3,
        top_p: 0.9,
        top_k: 40,
        ...options,
      },
    }),

  // 사용 가능한 모델 목록 조회
  getModels: () => ollamaInstance.get('/api/tags'),

  // 모델 정보 조회
  getModelInfo: (model: string) =>
    ollamaInstance.post('/api/show', { name: model }),

  getTags: async () => {
    const response = await ollamaInstance.get<OllamaTagsResponse>('/api/tags');
    return response.data;
  },
};

// LSP API export
export { lspService };
