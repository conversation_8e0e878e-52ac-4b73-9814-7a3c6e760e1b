import axios from 'axios';

// Qdrant API 기본 URL (프록시 사용)
const QDRANT_API_BASE_URL = '/qdrant';

// 기본 API 인스턴스 생성
const api = axios.create({
  baseURL: QDRANT_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface Collection {
  name: string;
  vectors_count?: number;
  status: string;
  vector_size: number;
  config?: any;
}

export interface CollectionDetail extends Collection {
  points_count: number;
  segments_count: number;
  payload_schema?: Record<string, any>;
  optimization?: Record<string, any>;
}

export interface SearchParams {
  collection: string;
  vector: number[];
  limit: number;
  filter?: Record<string, any>;
  with_payload?: boolean | string[];
  with_vectors?: boolean;
}

export interface SearchResult {
  id: string | number;
  score: number;
  payload?: Record<string, any>;
  vector?: number[];
}

// VectorDB 서비스 클래스
export const vectorDBService = {
  // 모든 컬렉션 목록 가져오기
  async getCollections(): Promise<Collection[]> {
    try {
      const response = await api.get('/collections');
      return response.data.result.collections || [];
    } catch (error) {
      console.error('Failed to fetch collections:', error);
      throw error;
    }
  },

  // 특정 컬렉션 정보 가져오기
  async getCollectionInfo(name: string): Promise<CollectionDetail> {
    try {
      const response = await api.get(`/collections/${name}`);
      return response.data.result || {};
    } catch (error) {
      console.error(`Failed to fetch collection info for ${name}:`, error);
      throw error;
    }
  },

  // 벡터 검색하기
  async searchVectors({
    collection,
    vector,
    limit,
    filter,
    with_payload = true,
    with_vectors = false,
  }: SearchParams): Promise<SearchResult[]> {
    try {
      const response = await api.post(
        `/collections/${collection}/points/search`,
        {
          vector,
          limit,
          filter,
          with_payload,
          with_vectors,
        }
      );
      return response.data.result || [];
    } catch (error) {
      console.error('Failed to search vectors:', error);
      throw error;
    }
  },

  // 컬렉션 통계 가져오기
  async getCollectionStats(name: string): Promise<any> {
    try {
      const response = await api.get(`/collections/${name}/stats`);
      return response.data.result || {};
    } catch (error) {
      console.error(`Failed to fetch stats for ${name}:`, error);
      throw error;
    }
  },

  // 컬렉션의 클러스터 정보 가져오기
  async getCollectionClusters(name: string): Promise<any> {
    try {
      const response = await api.post(`/collections/${name}/cluster`);
      return response.data.result || {};
    } catch (error) {
      console.error(`Failed to fetch clusters for ${name}:`, error);
      throw error;
    }
  },
};

export default vectorDBService;
