import React from 'react';
import { Card } from '@/components/ui/Card';
import { But<PERSON> } from '@/components/ui/Button';
import { Search, MessageSquare, BarChart3, Brain } from 'lucide-react';
import { Link } from '@tanstack/react-router';

export const HomePage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">
          Welcome to CodeBase Intelligence
        </h1>
        <p className="text-muted-foreground">
          Analyze, search, and understand your codebase with AI-powered tools.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Search className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">Smart Search</h3>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Search through your codebase with semantic understanding.
          </p>
          <Link to="/search">
            <Button className="w-full mt-4" variant="outline">
              Start Searching
            </Button>
          </Link>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">AI Chat</h3>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Ask questions about your code and get intelligent answers.
          </p>
          <Link to="/chat">
            <Button className="w-full mt-4" variant="outline">
              Start Chatting
            </Button>
          </Link>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">Code Analysis</h3>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Get insights and metrics about your codebase.
          </p>
          <Link to="/analysis">
            <Button className="w-full mt-4" variant="outline">
              View Analysis
            </Button>
          </Link>
        </Card>

        <Card className="p-6">
          <div className="flex items-center space-x-2">
            <Brain className="h-5 w-5 text-primary" />
            <h3 className="font-semibold">AI Models</h3>
          </div>
          <p className="text-sm text-muted-foreground mt-2">
            Manage and configure AI models for your project.
          </p>
          <Link to="/models">
            <Button className="w-full mt-4" variant="outline">
              Manage Models
            </Button>
          </Link>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="p-6">
          <h3 className="font-semibold mb-4">Recent Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 bg-green-500 rounded-full" />
              <span className="text-sm">Code analysis completed</span>
              <span className="text-xs text-muted-foreground ml-auto">
                2 min ago
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 bg-blue-500 rounded-full" />
              <span className="text-sm">New search query processed</span>
              <span className="text-xs text-muted-foreground ml-auto">
                5 min ago
              </span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-2 w-2 bg-yellow-500 rounded-full" />
              <span className="text-sm">AI model updated</span>
              <span className="text-xs text-muted-foreground ml-auto">
                1 hour ago
              </span>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="font-semibold mb-4">Quick Stats</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Total Files</span>
              <span className="font-medium">1,247</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">
                Lines of Code
              </span>
              <span className="font-medium">45,892</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Languages</span>
              <span className="font-medium">8</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Last Scan</span>
              <span className="font-medium">Today</span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};
