import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from '@tanstack/react-router';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Import the generated route tree

import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import { router } from './route-config';
import { useOllamaStore } from './stores/ollamaStore';

// Import global styles
import './styles/globals.css';

function App() {
  const auth = useAuth();
  const fetchAvailableModels = useOllamaStore(
    state => state.fetchAvailableModels
  );

  React.useEffect(() => {
    fetchAvailableModels();
  }, [fetchAvailableModels]);

  // Update the router context with the auth context
  router.update({
    context: {
      auth,
    },
  });

  return <RouterProvider router={router} />;
}

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

const rootElement = document.getElementById('root')!;
if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <App />
        </AuthProvider>
      </QueryClientProvider>
    </React.StrictMode>
  );
}
