@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%; /* Pure black for better contrast */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%; /* Pure black for card text */
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%; /* Pure black for popover text */
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 0 0% 0%; /* Pure black for secondary text */
    --muted: 210 40% 96%;
    --muted-foreground: 0 0% 25%; /* Dark gray instead of light gray */
    --accent: 210 40% 96%;
    --accent-foreground: 0 0% 0%; /* Pure black for accent text */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family:
      'Roboto',
      'Open Sans',
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      sans-serif;
    font-weight: 400;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Typography improvements */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Roboto', sans-serif;
    font-weight: 600;
    color: hsl(0 0% 0%); /* Pure black for headings */
    line-height: 1.3;
  }

  h1 {
    font-weight: 700;
  }

  p,
  span,
  div {
    color: hsl(0 0% 0%); /* Pure black for body text */
  }

  /* Button text improvements */
  button {
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
  }

  /* Input text improvements */
  input,
  textarea,
  select {
    font-family: 'Open Sans', sans-serif;
    color: hsl(0 0% 0%); /* Pure black for input text */
  }

  /* Label text improvements */
  label {
    font-family: 'Roboto', sans-serif;
    font-weight: 500;
    color: hsl(0 0% 0%); /* Pure black for labels */
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Code syntax highlighting */
.code-block {
  @apply bg-muted p-4 rounded-lg overflow-x-auto;
}

.code-block pre {
  @apply text-sm;
}

/* Loading animations */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Custom utilities */
.text-gradient {
  @apply bg-gradient-to-r from-primary to-blue-600 bg-clip-text text-transparent;
}
