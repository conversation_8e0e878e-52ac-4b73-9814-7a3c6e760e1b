import { useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { repositoryApi } from '@/services/api';
import { useRepositoryStore } from '@/stores/repositoryStore';

/**
 * Global repositories hook that manages repositories state
 * This should be used at the app level to ensure repositories are loaded globally
 */
export const useRepositories = () => {
  const {
    repositories,
    repositoriesLoading,
    repositoriesError,
    setRepositories,
    setRepositoriesLoading,
    setRepositoriesError,
    context,
    setRepositoryContext,
    getReadyRepositories,
  } = useRepositoryStore();

  // Fetch repositories using React Query
  const {
    data: repositoriesData,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['repositories'],
    queryFn: () => repositoryApi.getRepositories({ per_page: 100 }), // Get up to 100 repositories
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // Consider data stale after 10 seconds
  });

  // Update global state when query data changes
  useEffect(() => {
    setRepositoriesLoading(isLoading);
  }, [isLoading, setRepositoriesLoading]);

  useEffect(() => {
    if (error) {
      setRepositoriesError(
        error instanceof Error ? error.message : 'Failed to fetch repositories'
      );
    } else {
      setRepositoriesError(null);
    }
  }, [error, setRepositoriesError]);

  useEffect(() => {
    if (repositoriesData?.data?.repositories) {
      setRepositories(repositoriesData.data.repositories);
    }
  }, [repositoriesData, setRepositories]);

  // Auto-select first ready repository if none is selected
  useEffect(() => {
    if (repositories.length > 0 && !context.repository_id) {
      const readyRepos = getReadyRepositories();
      if (readyRepos.length > 0) {
        // Find a preferred repository or use the first one
        const preferredRepo =
          readyRepos.find(
            repo =>
              repo.name === 'express' ||
              repo.name === 'rails' ||
              repo.name === 'vscode'
          ) || readyRepos[0];

        if (preferredRepo) {
          console.log('Auto-selecting repository:', preferredRepo);
          // Auto-select with default branch
          setRepositoryContext(
            preferredRepo.id,
            preferredRepo.name,
            preferredRepo.current_branch ?? ''
          );
        }
      }
    }
  }, [
    repositories,
    context.repository_id,
    setRepositoryContext,
    getReadyRepositories,
  ]);

  return {
    repositories,
    loading: repositoriesLoading,
    error: repositoriesError,
    refetch: refetch,
    readyRepositories: getReadyRepositories(),
  };
};

/**
 * Hook for components that need to access repositories without triggering the global fetch
 * Use this in individual components that need repository data
 */
export const useRepositoryData = () => {
  const {
    repositories,
    repositoriesLoading,
    repositoriesError,
    branchAnalyses,
    branchAnalysesLoading,
    context,
    hasValidContext,
    getCurrentRepository,
    getCurrentBranchAnalysis,
    getReadyRepositories,
    getBranchAnalysesForRepository,
    getAnalyzedBranches,
  } = useRepositoryStore();

  return {
    repositories,
    repositoriesLoading,
    repositoriesError,
    branchAnalyses,
    branchAnalysesLoading,
    context,
    hasValidContext: hasValidContext(),
    currentRepository: getCurrentRepository(),
    currentBranchAnalysis: getCurrentBranchAnalysis(),
    readyRepositories: getReadyRepositories(),
    getBranchAnalysesForRepository,
    getAnalyzedBranches,
  };
};

/**
 * Hook for managing repository context (selection)
 */
export const useRepositoryContext = () => {
  const {
    context,
    setRepositoryContext,
    clearContext,
    hasValidContext,
    getCurrentRepository,
    getCurrentBranchAnalysis,
  } = useRepositoryStore();

  return {
    context,
    setRepositoryContext,
    clearContext,
    hasValidContext: hasValidContext(),
    currentRepository: getCurrentRepository(),
    currentBranchAnalysis: getCurrentBranchAnalysis(),
  };
};
