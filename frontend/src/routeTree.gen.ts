/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as ViewRouteImport } from './routes/view'
import { Route as VectordbRouteImport } from './routes/vectordb'
import { Route as SettingsRouteImport } from './routes/settings'
import { Route as SearchRouteImport } from './routes/search'
import { Route as ReposRouteImport } from './routes/repos'
import { Route as LoginRouteImport } from './routes/login'
import { Route as CodeRouteImport } from './routes/code'
import { Route as ChatRouteImport } from './routes/chat'
import { Route as AnalysisRouteImport } from './routes/analysis'
import { Route as IndexRouteImport } from './routes/index'

const ViewRoute = ViewRouteImport.update({
  id: '/view',
  path: '/view',
  getParentRoute: () => rootRouteImport,
} as any)
const VectordbRoute = VectordbRouteImport.update({
  id: '/vectordb',
  path: '/vectordb',
  getParentRoute: () => rootRouteImport,
} as any)
const SettingsRoute = SettingsRouteImport.update({
  id: '/settings',
  path: '/settings',
  getParentRoute: () => rootRouteImport,
} as any)
const SearchRoute = SearchRouteImport.update({
  id: '/search',
  path: '/search',
  getParentRoute: () => rootRouteImport,
} as any)
const ReposRoute = ReposRouteImport.update({
  id: '/repos',
  path: '/repos',
  getParentRoute: () => rootRouteImport,
} as any)
const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const CodeRoute = CodeRouteImport.update({
  id: '/code',
  path: '/code',
  getParentRoute: () => rootRouteImport,
} as any)
const ChatRoute = ChatRouteImport.update({
  id: '/chat',
  path: '/chat',
  getParentRoute: () => rootRouteImport,
} as any)
const AnalysisRoute = AnalysisRouteImport.update({
  id: '/analysis',
  path: '/analysis',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/analysis': typeof AnalysisRoute
  '/chat': typeof ChatRoute
  '/code': typeof CodeRoute
  '/login': typeof LoginRoute
  '/repos': typeof ReposRoute
  '/search': typeof SearchRoute
  '/settings': typeof SettingsRoute
  '/vectordb': typeof VectordbRoute
  '/view': typeof ViewRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/analysis': typeof AnalysisRoute
  '/chat': typeof ChatRoute
  '/code': typeof CodeRoute
  '/login': typeof LoginRoute
  '/repos': typeof ReposRoute
  '/search': typeof SearchRoute
  '/settings': typeof SettingsRoute
  '/vectordb': typeof VectordbRoute
  '/view': typeof ViewRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/analysis': typeof AnalysisRoute
  '/chat': typeof ChatRoute
  '/code': typeof CodeRoute
  '/login': typeof LoginRoute
  '/repos': typeof ReposRoute
  '/search': typeof SearchRoute
  '/settings': typeof SettingsRoute
  '/vectordb': typeof VectordbRoute
  '/view': typeof ViewRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/analysis'
    | '/chat'
    | '/code'
    | '/login'
    | '/repos'
    | '/search'
    | '/settings'
    | '/vectordb'
    | '/view'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/analysis'
    | '/chat'
    | '/code'
    | '/login'
    | '/repos'
    | '/search'
    | '/settings'
    | '/vectordb'
    | '/view'
  id:
    | '__root__'
    | '/'
    | '/analysis'
    | '/chat'
    | '/code'
    | '/login'
    | '/repos'
    | '/search'
    | '/settings'
    | '/vectordb'
    | '/view'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AnalysisRoute: typeof AnalysisRoute
  ChatRoute: typeof ChatRoute
  CodeRoute: typeof CodeRoute
  LoginRoute: typeof LoginRoute
  ReposRoute: typeof ReposRoute
  SearchRoute: typeof SearchRoute
  SettingsRoute: typeof SettingsRoute
  VectordbRoute: typeof VectordbRoute
  ViewRoute: typeof ViewRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/view': {
      id: '/view'
      path: '/view'
      fullPath: '/view'
      preLoaderRoute: typeof ViewRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/vectordb': {
      id: '/vectordb'
      path: '/vectordb'
      fullPath: '/vectordb'
      preLoaderRoute: typeof VectordbRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/settings': {
      id: '/settings'
      path: '/settings'
      fullPath: '/settings'
      preLoaderRoute: typeof SettingsRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/search': {
      id: '/search'
      path: '/search'
      fullPath: '/search'
      preLoaderRoute: typeof SearchRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/repos': {
      id: '/repos'
      path: '/repos'
      fullPath: '/repos'
      preLoaderRoute: typeof ReposRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/code': {
      id: '/code'
      path: '/code'
      fullPath: '/code'
      preLoaderRoute: typeof CodeRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/chat': {
      id: '/chat'
      path: '/chat'
      fullPath: '/chat'
      preLoaderRoute: typeof ChatRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/analysis': {
      id: '/analysis'
      path: '/analysis'
      fullPath: '/analysis'
      preLoaderRoute: typeof AnalysisRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
  }
}

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AnalysisRoute: AnalysisRoute,
  ChatRoute: ChatRoute,
  CodeRoute: CodeRoute,
  LoginRoute: LoginRoute,
  ReposRoute: ReposRoute,
  SearchRoute: SearchRoute,
  SettingsRoute: SettingsRoute,
  VectordbRoute: VectordbRoute,
  ViewRoute: ViewRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
