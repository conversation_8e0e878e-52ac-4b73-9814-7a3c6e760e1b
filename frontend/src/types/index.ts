// Auth types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role?: string;
  createdAt: string;
  updatedAt: string;
}

export interface LoginData {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Search types
export interface SearchResult {
  id: string;
  title: string;
  content: string;
  filePath: string;
  lineNumber?: number;
  score: number;
  highlights?: string[];
}

export interface SearchQuery {
  query: string;
  filters?: {
    fileTypes?: string[];
    directories?: string[];
    dateRange?: {
      from: string;
      to: string;
    };
  };
  limit?: number;
  offset?: number;
}

// Chat types
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant' | 'system';
  timestamp: string;
  metadata?: {
    model?: string;
    tokens?: number;
    references?: string[];
  };
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  createdAt: string;
  updatedAt: string;
}

// Analysis types
export interface CodeMetrics {
  totalFiles: number;
  totalLines: number;
  totalFunctions: number;
  totalClasses: number;
  languages: {
    [key: string]: {
      files: number;
      lines: number;
      percentage: number;
    };
  };
  complexity: {
    average: number;
    max: number;
    distribution: {
      low: number;
      medium: number;
      high: number;
    };
  };
}

export interface FileAnalysis {
  path: string;
  language: string;
  lines: number;
  functions: number;
  classes: number;
  complexity: number;
  dependencies: string[];
  lastModified: string;
}

// Model types
export interface AIModel {
  id: string;
  name: string;
  provider: 'openai' | 'anthropic' | 'ollama' | 'custom';
  type: 'chat' | 'embedding' | 'completion';
  status: 'active' | 'inactive' | 'error';
  config: {
    apiKey?: string;
    baseUrl?: string;
    model: string;
    temperature?: number;
    maxTokens?: number;
  };
  usage?: {
    requests: number;
    tokens: number;
    cost: number;
  };
}

// Repository types
export interface Repository {
  id: number;
  name: string;
  url: string;
  local_path?: string;
  clone_status?: 'cloning' | 'ready' | 'indexing' | 'error' | 'pending';
  status: 'cloning' | 'ready' | 'indexing' | 'error' | 'pending';
  current_branch?: string;
  currentBranch?: string;
  last_indexed?: string;
  lastIndexed?: string;
  branches: string[];
  last_indexed_at?: string;
  created_at: string;
  updated_at: string;
  error?: string;
}

// Repository Branch Analysis - 브랜치별 분석 상태
export interface RepositoryBranchAnalysis {
  id: string; // repository_id + branch 조합으로 생성된 고유 ID
  repository_id: number;
  repository_name: string;
  branch: string;
  status: 'not_analyzed' | 'analyzing' | 'completed' | 'error';
  collection_name: string; // repo_{repository_id}_branch_{safe_branch}
  last_analyzed_at?: string;
  total_files?: number;
  total_vectors?: number;
  error_message?: string;
}

// Repository Context - 현재 선택된 컨텍스트
export interface RepositoryContext {
  repository_id: number | null;
  repository_name: string | null;
  branch: string | null;
  analysis_id: string | null; // repository_id + branch 조합
  collection_name: string | null;
}

export interface RepositoryCloneRequest {
  url: string;
  username?: string;
  password?: string;
}

export interface RepositoryBranch {
  name: string;
  commit: string;
  isDefault: boolean;
  lastCommitDate: string;
  lastCommitMessage: string;
}

export interface BranchesResponse {
  branches: RepositoryBranch[];
}

export interface RepositoryStatus {
  id: string;
  status: Repository['status'];
  progress?: number;
  message?: string;
  error?: string;
}

// Git Commit types
export interface GitCommit {
  hash: string;
  shortHash: string;
  author: string;
  authorEmail: string;
  date: string;
  message: string;
  subject: string;
  body?: string;
  filesChanged: number;
  insertions: number;
  deletions: number;
}

export interface GitCommitFile {
  path: string;
  status: 'added' | 'modified' | 'deleted' | 'renamed' | 'copied';
  additions: number;
  deletions: number;
  changes: number;
  oldPath?: string; // for renamed files
  content?: string; // file content for diff view
  diff?: string; // git diff output
}

export interface GitCommitDetail {
  commit: GitCommit;
  files: GitCommitFile[];
  stats: {
    totalFiles: number;
    totalAdditions: number;
    totalDeletions: number;
  };
}

export interface GitCommitGroup {
  date: string;
  commits: GitCommit[];
}

// UI types
export interface NavigationItem {
  title: string;
  href: string;
  icon?: React.ComponentType<any>;
  badge?: string | number;
  children?: NavigationItem[];
}

export interface TableColumn<T = any> {
  key: keyof T;
  title: string;
  sortable?: boolean;
  render?: (value: any, record: T) => React.ReactNode;
}

export interface TableProps<T = any> {
  data: T[];
  columns: TableColumn<T>[];
  loading?: boolean;
  pagination?: {
    current: number;
    total: number;
    pageSize: number;
    onChange: (page: number, pageSize: number) => void;
  };
  onSort?: (key: keyof T, direction: 'asc' | 'desc') => void;
}

// File Explorer types
export interface FileTreeNode {
  id: string;
  name: string;
  type: 'directory' | 'file';
  path: string;
  depth: number;
  children?: FileTreeNode[];
  expanded?: boolean;
  has_children?: boolean;
  file_count?: number;
  subdirectory_count?: number;

  // File specific properties
  size?: number;
  lines?: number;
  language?: string;
  extension?: string;
  is_binary?: boolean;
  last_modified?: string;
}

export interface FileContent {
  content: string;
  file: {
    id: number;
    name: string;
    path: string;
    language: string;
    encoding: string;
    lines: number;
    size: number;
  };
}

export interface FileSearchResult {
  file: {
    id: number;
    name: string;
    path: string;
    language: string;
    size: number;
    lines: number;
  };
  match_type: 'content' | 'filename' | 'path';
  directory: string;
}

export interface FileSearchResponse {
  results: FileSearchResult[];
  query: string;
  search_type: string;
  total_results: number;
}
