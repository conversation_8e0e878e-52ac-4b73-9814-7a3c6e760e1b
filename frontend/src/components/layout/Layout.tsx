import React from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { useRepositories } from '@/hooks/useRepositories';

interface LayoutProps {
  isAuthenticated: boolean;
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({
  children,
  isAuthenticated,
}) => {
  // Initialize global repositories state
  useRepositories();

  return (
    <div className="h-screen bg-background flex flex-col overflow-hidden">
      <Header isAuthenticated={isAuthenticated} />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />
        <main className="flex-1 overflow-y-auto p-3 pt-4 min-w-0">
          {children}
        </main>
      </div>
    </div>
  );
};
