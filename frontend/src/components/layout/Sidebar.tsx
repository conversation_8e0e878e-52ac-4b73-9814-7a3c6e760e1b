import React from 'react';
import { Link } from '@tanstack/react-router';
import {
  Home,
  Search,
  MessageSquare,
  BarChart3,
  FileText,
  Settings,
  Brain,
  Database,
  Code,
  Boxes,
  GitBranch,
} from 'lucide-react';
import { cn } from '@/utils/cn';

const sidebarItems = [
  {
    title: 'Dashboard',
    href: '/',
    icon: Home,
  },
  {
    title: 'View',
    href: '/view',
    icon: FileText,
  },
  {
    title: 'Code',
    href: '/code',
    icon: Code,
  },
  {
    title: 'Vectors',
    href: '/vectordb',
    icon: Boxes,
  },
  {
    title: 'Search',
    href: '/search',
    icon: Search,
  },
  {
    title: 'AI Chat',
    href: '/chat',
    icon: MessageSquare,
  },
  {
    title: 'Analysis',
    href: '/analysis',
    icon: BarChart3,
  },
  {
    title: 'Models',
    href: '/models',
    icon: Brain,
  },
  {
    title: 'Data',
    href: '/data',
    icon: Database,
  },
  {
    title: 'Docs',
    href: '/docs',
    icon: FileText,
  },
  {
    title: 'Repos',
    href: '/repos',
    icon: GitBranch,
  },
  {
    title: 'Settings',
    href: '/settings',
    icon: Settings,
  },
];

export const Sidebar: React.FC = () => {
  return (
    <div className="pb-12 w-40 xl:w-64 flex-shrink-0">
      <div className="space-y-4 py-4">
        <div className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold font-heading text-black tracking-tight">
            Navigation
          </h2>
          <div className="space-y-1">
            {sidebarItems.map(item => (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  'flex items-center rounded-lg px-3 py-2 text-sm font-medium font-body text-gray-700 hover:bg-accent hover:text-accent-foreground transition-colors'
                )}
                activeProps={{
                  className: 'bg-accent text-black font-medium',
                }}
              >
                <item.icon className="mr-2 h-4 w-4" />
                <span>{item.title}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
