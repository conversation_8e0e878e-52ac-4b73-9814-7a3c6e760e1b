import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate, Link } from '@tanstack/react-router';
import {
  Search,
  MessageSquare,
  BarChart3,
  Settings,
  LogOut,
  Database,
  FileCode,
} from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { lspService } from '@/services/api';

export const Header: React.FC<{ isAuthenticated: boolean }> = ({
  isAuthenticated,
}) => {
  const { logout } = useAuth();
  const navigate = useNavigate();
  const [lspHealthy, setLspHealthy] = useState<boolean | null>(null);

  const handleLogout = async () => {
    await logout();
    navigate({ to: '/login' });
  };

  // LSP 상태 체크
  useEffect(() => {
    const checkLspHealth = async () => {
      try {
        const health = await lspService.healthCheck();
        setLspHealthy(health.status === 'ok');
      } catch (error) {
        console.error('[Header] LSP health check failed:', error);
        setLspHealthy(false);
      }
    };

    if (isAuthenticated) {
      checkLspHealth();
      // 30초마다 상태 체크
      const interval = setInterval(checkLspHealth, 30000);
      return () => clearInterval(interval);
    }
  }, [isAuthenticated]);

  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-14 items-center w-full px-2">
        <div className="mr-4 flex items-center">
          <Link to="/" className="mr-6 flex items-center space-x-2">
            <div className="relative flex items-center justify-center">
              <Database className="h-5 w-5 text-blue-600" />
              <FileCode className="h-4 w-4 text-green-600 absolute -top-1 -right-1" />
            </div>
            <span className="hidden font-bold font-heading text-black sm:inline-block">
              CodeBase Intelligence
            </span>
          </Link>
          {isAuthenticated && (
            <nav className="flex items-center space-x-6 text-sm font-medium">
              <Link
                to="/search"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
                activeProps={{ className: 'text-foreground' }}
              >
                <div className="flex items-center space-x-2">
                  <Search className="h-4 w-4" />
                  <span>Search</span>
                </div>
              </Link>
              <Link
                to="/chat"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
                activeProps={{ className: 'text-foreground' }}
              >
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-4 w-4" />
                  <span>Chat</span>
                </div>
              </Link>
              <Link
                to="/analysis"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
                activeProps={{ className: 'text-foreground' }}
              >
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Analysis</span>
                </div>
              </Link>
            </nav>
          )}
        </div>
        <div className="flex flex-1 items-center justify-end space-x-4 pr-2">
          {isAuthenticated && lspHealthy !== null && (
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium">LSP Status:</span>
              <span
                className={`text-sm font-medium ${
                  lspHealthy ? 'text-green-600' : 'text-red-600'
                }`}
              >
                {lspHealthy ? 'OK' : 'Not OK'}
              </span>
            </div>
          )}
          {isAuthenticated && (
            <>
              <Button variant="ghost" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </Button>
              <Link
                to="/settings"
                className="transition-colors hover:text-foreground/80 text-foreground/60"
                activeProps={{ className: 'text-foreground' }}
              >
                <Settings className="h-5 w-5" />
                <span className="sr-only">Settings</span>
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
};
