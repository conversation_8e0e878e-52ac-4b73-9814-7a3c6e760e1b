import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Loader2 } from 'lucide-react';
// 컴포넌트 임포트
import { CollectionList, CollectionDetail, VectorSearch } from '.';
import NaturalLanguageSearch from './NaturalLanguageSearch';
import { vectorDBService, Collection } from '@/services/vectorDBService';
import { useRepositoryStore } from '@/stores/repositoryStore';

export const VectorDBDashboard: React.FC = () => {
  // Global repository state
  const { context } = useRepositoryStore();

  const [collections, setCollections] = useState<Collection[]>([]);
  const [selectedCollection, setSelectedCollection] = useState<string | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('natural-search');

  // 컬렉션 데이터 로드
  useEffect(() => {
    const fetchCollections = async () => {
      try {
        setLoading(true);
        const data = await vectorDBService.getCollections();
        setCollections(data);

        // Repository가 선택되어 있으면 해당 repository의 컬렉션을 우선 선택
        if (data.length > 0 && !selectedCollection) {
          let preferredCollection = null;

          if (context.repository_id) {
            // repo_{repositoryId} 형태의 컬렉션을 찾기
            preferredCollection = data.find(
              col =>
                col.name === `repo_${context.repository_id}` ||
                col.name.includes(context.repository_id!.toString())
            );
          }

          // 우선 컬렉션이 없으면 첫 번째 컬렉션 선택
          const targetCollection = preferredCollection || data[0];
          setSelectedCollection(targetCollection.name);
        }
        setError(null);
      } catch (err) {
        console.error('Failed to fetch collections:', err);
        setError(
          '벡터 데이터베이스 연결에 실패했습니다. 서버가 실행 중인지 확인하세요.'
        );
      } finally {
        setLoading(false);
      }
    };

    fetchCollections();
  }, [context.repository_id]); // repository_id가 변경될 때마다 다시 로드

  const handleCollectionSelect = (name: string) => {
    setSelectedCollection(name);
  };

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">데이터 로딩 중...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="text-destructive">연결 오류</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
          <p className="mt-2 text-sm text-muted-foreground">
            벡터 데이터베이스(Qdrant) 서버가 실행 중인지 확인하고 다시
            시도해주세요.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="flex h-full flex-col space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">VectorDB 대시보드</h1>
          <p className="text-muted-foreground">
            AI 기반 자연어 검색 및 벡터 데이터베이스 관리
            {context.repository_name && (
              <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
                Repository: {context.repository_name}
              </span>
            )}
          </p>
        </div>
      </div>

      <div className="grid h-full grid-cols-5 gap-4">
        {/* 좌측 컬렉션 목록 */}
        <div className="col-span-1 overflow-y-auto border-r pr-2">
          <CollectionList
            collections={collections}
            selectedCollection={selectedCollection}
            onSelectCollection={handleCollectionSelect}
          />
        </div>

        {/* 우측 메인 콘텐츠 영역 */}
        <div className="col-span-4">
          {selectedCollection ? (
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="w-full justify-start">
                <TabsTrigger value="overview">개요</TabsTrigger>
                <TabsTrigger value="natural-search">자연어 검색</TabsTrigger>
                <TabsTrigger value="search">벡터 검색</TabsTrigger>
                <TabsTrigger value="stats">통계</TabsTrigger>
              </TabsList>
              <TabsContent value="overview" className="mt-4">
                <CollectionDetail collectionName={selectedCollection} />
              </TabsContent>
              <TabsContent value="natural-search" className="mt-4">
                <NaturalLanguageSearch />
              </TabsContent>
              <TabsContent value="search" className="mt-4">
                <VectorSearch collectionName={selectedCollection} />
              </TabsContent>
              <TabsContent value="stats" className="mt-4">
                <Card>
                  <CardHeader>
                    <CardTitle>컬렉션 통계</CardTitle>
                    <CardDescription>
                      {selectedCollection} 컬렉션의 상세 통계 정보
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {/* 추후 통계 컴포넌트로 확장 */}
                    <p className="text-muted-foreground">
                      통계 기능은 개발 중입니다...
                    </p>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <p className="text-center text-muted-foreground">
                  좌측에서 컬렉션을 선택하세요.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};
