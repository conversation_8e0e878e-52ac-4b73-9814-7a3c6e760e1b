import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { CollectionDetail as CollectionDetailType } from '@/services/vectorDBService';
import { vectorDBService } from '@/services/vectorDBService';
import { Loader2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface CollectionDetailProps {
  collectionName: string;
}

const CollectionDetail: React.FC<CollectionDetailProps> = ({
  collectionName,
}) => {
  const [collection, setCollection] = useState<CollectionDetailType | null>(
    null
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCollectionDetails = async () => {
      try {
        setLoading(true);
        const data = await vectorDBService.getCollectionInfo(collectionName);
        setCollection(data);
        setError(null);
      } catch (err) {
        console.error(`Failed to fetch details for ${collectionName}:`, err);
        setError('컬렉션 정보를 불러오는데 실패했습니다.');
      } finally {
        setLoading(false);
      }
    };

    if (collectionName) {
      fetchCollectionDetails();
    }
  }, [collectionName]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-6">
        <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
        <span>로딩 중...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader>
          <CardTitle className="text-destructive">오류 발생</CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (!collection) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground">컬렉션 정보가 없습니다.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{collection.name}</CardTitle>
            <Badge
              variant={collection.status === 'green' ? 'success' : 'secondary'}
            >
              {collection.status === 'green' ? '활성' : collection.status}
            </Badge>
          </div>
          <CardDescription>기본 정보</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-1">
              <div className="text-sm font-medium">벡터 수:</div>
              <div className="text-sm">
                {collection.points_count?.toLocaleString() || 'N/A'}
              </div>
            </div>
            <div className="grid grid-cols-2 gap-1">
              <div className="text-sm font-medium">벡터 차원:</div>
              <div className="text-sm">{collection.vector_size}</div>
            </div>
            <div className="grid grid-cols-2 gap-1">
              <div className="text-sm font-medium">세그먼트 수:</div>
              <div className="text-sm">{collection.segments_count || 0}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>컬렉션 구성</CardTitle>
          <CardDescription>벡터 인덱싱 및 검색 구성</CardDescription>
        </CardHeader>
        <CardContent>
          {collection.config ? (
            <div className="space-y-2 text-sm">
              <div className="grid grid-cols-2 gap-1">
                <div className="font-medium">벡터 거리:</div>
                <div>
                  {collection.config.params?.vector_params?.distance ||
                    '코사인'}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-1">
                <div className="font-medium">인덱싱 타입:</div>
                <div>{collection.config.hnsw_config?.m ? 'HNSW' : '기본'}</div>
              </div>
              {collection.config.hnsw_config && (
                <div className="grid grid-cols-2 gap-1">
                  <div className="font-medium">HNSW 파라미터:</div>
                  <div>
                    M={collection.config.hnsw_config.m}, ef=
                    {collection.config.hnsw_config.ef_construct}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p className="text-muted-foreground">구성 정보가 없습니다.</p>
          )}
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>페이로드 스키마</CardTitle>
          <CardDescription>벡터에 연결된 메타데이터 구조</CardDescription>
        </CardHeader>
        <CardContent>
          {collection.payload_schema &&
          Object.keys(collection.payload_schema).length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-1 font-medium">필드</th>
                    <th className="text-left py-2 px-1 font-medium">타입</th>
                    <th className="text-left py-2 px-1 font-medium">인덱싱</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(collection.payload_schema).map(
                    ([key, schema]) => (
                      <tr key={key} className="border-b">
                        <td className="py-2 px-1">{key}</td>
                        <td className="py-2 px-1">{schema.data_type}</td>
                        <td className="py-2 px-1">
                          {schema.indexed ? (
                            <Badge
                              variant="outline"
                              className="bg-green-50 text-green-700 border-green-200"
                            >
                              인덱싱됨
                            </Badge>
                          ) : (
                            <span className="text-muted-foreground">—</span>
                          )}
                        </td>
                      </tr>
                    )
                  )}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-muted-foreground">
              페이로드 스키마가 정의되지 않았습니다.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default CollectionDetail;
