import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Slider } from '@/components/ui/slider';
import { SearchResult, vectorDBService } from '@/services/vectorDBService';
import { Loader2, Search } from 'lucide-react';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/Label';
import { Switch } from '@/components/ui/switch';

interface VectorSearchProps {
  collectionName: string;
}

const VectorSearch: React.FC<VectorSearchProps> = ({ collectionName }) => {
  const [vectorText, setVectorText] = useState<string>('');
  const [limit, setLimit] = useState<number>(10);
  const [withPayload, setWithPayload] = useState<boolean>(true);
  const [withVectors, setWithVectors] = useState<boolean>(false);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [searched, setSearched] = useState<boolean>(false);

  const handleSearch = async () => {
    try {
      setLoading(true);
      setError(null);
      setSearched(true);

      // 벡터 텍스트를 파싱하여 숫자 배열로 변환
      let vector: number[];

      try {
        // 텍스트가 JSON 배열인 경우
        if (
          vectorText.trim().startsWith('[') &&
          vectorText.trim().endsWith(']')
        ) {
          vector = JSON.parse(vectorText);
        } else {
          // 쉼표나 공백으로 구분된 숫자 문자열인 경우
          vector = vectorText
            .split(/[\s,]+/)
            .filter(v => v.trim())
            .map(v => parseFloat(v));
        }

        if (!Array.isArray(vector) || vector.some(isNaN)) {
          throw new Error('Invalid vector format');
        }
      } catch (err) {
        setError('유효한 벡터 형식이 아닙니다. 숫자 배열을 입력하세요.');
        setLoading(false);
        return;
      }

      const searchResults = await vectorDBService.searchVectors({
        collection: collectionName,
        vector,
        limit,
        with_payload: withPayload,
        with_vectors: withVectors,
      });

      setResults(searchResults);
    } catch (err) {
      console.error('Search failed:', err);
      setError('검색 중 오류가 발생했습니다.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>벡터 검색</CardTitle>
          <CardDescription>
            {collectionName} 컬렉션에서 유사한 벡터를 검색합니다.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="vector-input">벡터 입력</Label>
              <Textarea
                id="vector-input"
                placeholder="예시: [0.1, 0.2, 0.3, ...] 또는 0.1, 0.2, 0.3, ..."
                className="h-24 font-mono"
                value={vectorText}
                onChange={e => setVectorText(e.target.value)}
              />
              <p className="text-sm text-muted-foreground mt-1">
                JSON 배열 또는 쉼표/공백으로 구분된 숫자를 입력하세요.
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>결과 수: {limit}</Label>
              </div>
              <Slider
                value={[limit]}
                min={1}
                max={100}
                step={1}
                onValueChange={value => setLimit(value[0])}
              />
            </div>

            <div className="flex space-x-8">
              <div className="flex items-center space-x-2">
                <Switch
                  id="with-payload"
                  checked={withPayload}
                  onCheckedChange={setWithPayload}
                />
                <Label htmlFor="with-payload">페이로드 포함</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="with-vectors"
                  checked={withVectors}
                  onCheckedChange={setWithVectors}
                />
                <Label htmlFor="with-vectors">벡터 포함</Label>
              </div>
            </div>

            <Button
              onClick={handleSearch}
              disabled={loading || !vectorText.trim()}
              className="w-full"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  검색 중...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  벡터 검색
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive">{error}</p>
          </CardContent>
        </Card>
      )}

      {searched && !error && results.length === 0 && !loading && (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">
              검색 결과가 없습니다.
            </p>
          </CardContent>
        </Card>
      )}

      {results.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>검색 결과</CardTitle>
            <CardDescription>
              가장 유사한 {results.length}개의 벡터
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-1 font-medium">ID</th>
                    <th className="text-left py-2 px-1 font-medium">
                      유사도 점수
                    </th>
                    {withPayload && (
                      <th className="text-left py-2 px-1 font-medium">
                        페이로드
                      </th>
                    )}
                  </tr>
                </thead>
                <tbody>
                  {results.map((result, idx) => (
                    <tr key={idx} className="border-b">
                      <td className="py-2 px-1 font-mono">{result.id}</td>
                      <td className="py-2 px-1">{result.score.toFixed(6)}</td>
                      {withPayload && (
                        <td className="py-2 px-1">
                          <div className="max-h-24 overflow-y-auto">
                            <pre className="text-xs">
                              {JSON.stringify(result.payload, null, 2)}
                            </pre>
                          </div>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default VectorSearch;
