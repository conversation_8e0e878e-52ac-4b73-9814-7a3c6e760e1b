import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { Button } from '@/components/ui/Button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  aiendService,
  QueryRequest,
  QueryResponse,
  SearchRequest,
  SearchResponse,
} from '@/services/aiendService';
import { useRepositoryStore } from '@/stores/repositoryStore';
import SearchResultsDisplay from './SearchResultsDisplay';
import { Loader2, Search, MessageSquare, Lightbulb } from 'lucide-react';

const NaturalLanguageSearch: React.FC = () => {
  // Global repository state
  const { context } = useRepositoryStore();

  // State for natural language query
  const [query, setQuery] = useState<string>('');
  const [queryResponse, setQueryResponse] = useState<QueryResponse | null>(
    null
  );
  const [queryLoading, setQueryLoading] = useState<boolean>(false);
  const [queryError, setQueryError] = useState<string | null>(null);

  // State for advanced search
  const [searchMode, setSearchMode] = useState<string>('auto');
  const [maxResults, setMaxResults] = useState<number>(10);
  const [searchResponse, setSearchResponse] = useState<SearchResponse | null>(
    null
  );
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchError, setSearchError] = useState<string | null>(null);

  // State for suggestions and modes
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [searchModes, setSearchModes] = useState<any[]>([]);

  // UI state
  const [activeTab, setActiveTab] = useState<string>('query');

  // Load search modes on component mount
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        const modes = await aiendService.getSearchModes();
        setSearchModes(modes);
      } catch (error) {
        console.error('Failed to load search modes:', error);
      }
    };

    loadMetadata();
  }, []);

  // Handle natural language query
  const handleNaturalLanguageQuery = async () => {
    if (!query.trim()) return;

    if (!context.repository_id) {
      setQueryError('리포지토리를 먼저 선택해주세요.');
      return;
    }

    try {
      setQueryLoading(true);
      setQueryError(null);

      const request: QueryRequest = {
        query: query.trim(),
        repository_id: context.repository_id || undefined,
        max_results: maxResults,
      };

      const response = await aiendService.naturalLanguageQuery(request);
      setQueryResponse(response);

      // Get suggestions for the query
      try {
        const suggestions = await aiendService.getSearchSuggestions(
          query.trim(),
          5
        );
        setSuggestions(suggestions);
      } catch (err) {
        console.warn('Failed to get suggestions:', err);
      }
    } catch (error) {
      console.error('Natural language query failed:', error);
      setQueryError(
        '자연어 검색 중 오류가 발생했습니다. 서버 연결을 확인해주세요.'
      );
    } finally {
      setQueryLoading(false);
    }
  };

  // Handle advanced search
  const handleAdvancedSearch = async () => {
    if (!query.trim()) return;

    try {
      setSearchLoading(true);
      setSearchError(null);

      const request: SearchRequest = {
        query: query.trim(),
        mode: searchMode as any,
        max_results: maxResults,
        include_history: true,
      };

      const response = await aiendService.searchCodebase(request);
      setSearchResponse(response);
    } catch (error) {
      console.error('Advanced search failed:', error);
      setSearchError(
        '고급 검색 중 오류가 발생했습니다. 서버 연결을 확인해주세요.'
      );
    } finally {
      setSearchLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>자연어 검색</span>
          </CardTitle>
          <CardDescription>
            {context.repository_name ? (
              <>
                <span className="font-medium">{context.repository_name}</span>{' '}
                리포지토리에서 자연어로 코드를 검색하세요.
              </>
            ) : (
              '자연어로 코드베이스를 검색하고 질문하세요.'
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="search-query">검색 질문</Label>
              <Textarea
                id="search-query"
                placeholder="예: 사용자 인증은 어떻게 구현되어 있나요? 또는 데이터베이스 연결 코드를 찾아주세요."
                className="h-20"
                value={query}
                onChange={e => setQuery(e.target.value)}
                onKeyDown={e => {
                  if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    if (activeTab === 'query') {
                      handleNaturalLanguageQuery();
                    } else {
                      handleAdvancedSearch();
                    }
                  }
                }}
              />
              <p className="text-sm text-muted-foreground mt-1">
                Ctrl+Enter로 검색 실행 | 자연어로 질문하거나 키워드로
                검색하세요.
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Label htmlFor="max-results">최대 결과 수:</Label>
                <Input
                  id="max-results"
                  type="number"
                  min="1"
                  max="50"
                  value={maxResults}
                  onChange={e => setMaxResults(parseInt(e.target.value) || 10)}
                  className="w-20"
                />
              </div>

              {searchModes.length > 0 && (
                <div className="flex items-center space-x-2">
                  <Label htmlFor="search-mode">검색 모드:</Label>
                  <Select value={searchMode} onValueChange={setSearchMode}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {searchModes.map(mode => (
                        <SelectItem key={mode.name} value={mode.name}>
                          {mode.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="w-full"
            >
              <TabsList className="w-full">
                <TabsTrigger value="query" className="flex-1">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  자연어 질문
                </TabsTrigger>
                <TabsTrigger value="search" className="flex-1">
                  <Search className="h-4 w-4 mr-2" />
                  고급 검색
                </TabsTrigger>
              </TabsList>

              <TabsContent value="query" className="mt-4">
                <Button
                  onClick={handleNaturalLanguageQuery}
                  disabled={queryLoading || !query.trim()}
                  className="w-full"
                >
                  {queryLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      AI가 답변 생성 중...
                    </>
                  ) : (
                    <>
                      <MessageSquare className="mr-2 h-4 w-4" />
                      AI에게 질문하기
                    </>
                  )}
                </Button>
              </TabsContent>

              <TabsContent value="search" className="mt-4">
                <Button
                  onClick={handleAdvancedSearch}
                  disabled={searchLoading || !query.trim()}
                  className="w-full"
                >
                  {searchLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      검색 중...
                    </>
                  ) : (
                    <>
                      <Search className="mr-2 h-4 w-4" />
                      코드 검색
                    </>
                  )}
                </Button>
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center space-x-2 text-sm">
              <Lightbulb className="h-4 w-4" />
              <span>검색 제안</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {suggestions.map((suggestion, index) => (
                <Badge
                  key={index}
                  variant="outline"
                  className="cursor-pointer hover:bg-muted"
                  onClick={() => setQuery(suggestion)}
                >
                  {suggestion}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Messages */}
      {(queryError || searchError) && (
        <Card className="border-destructive">
          <CardContent className="pt-6">
            <p className="text-destructive">{queryError || searchError}</p>
          </CardContent>
        </Card>
      )}

      {/* Natural Language Query Results */}
      {queryResponse && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="h-5 w-5 text-green-500" />
                <span>AI 답변</span>
                <Badge variant="secondary" className="text-xs">
                  {queryResponse.execution_time.toFixed(2)}s
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-md">
                <p className="whitespace-pre-wrap">{queryResponse.answer}</p>
              </div>
            </CardContent>
          </Card>

          {queryResponse.search_results.length > 0 && (
            <SearchResultsDisplay
              results={queryResponse.search_results}
              title="관련 코드"
              description={`AI 답변과 관련된 ${queryResponse.search_results.length}개의 코드 스니펫`}
              executionTime={queryResponse.execution_time}
              showFilters={true}
            />
          )}
        </div>
      )}

      {/* Advanced Search Results */}
      {searchResponse && (
        <SearchResultsDisplay
          results={searchResponse.results}
          title="검색 결과"
          description={`${searchResponse.mode} 모드로 검색된 결과`}
          executionTime={searchResponse.execution_time}
          totalCount={searchResponse.total_count}
          mode={searchResponse.mode}
          showFilters={true}
        />
      )}
    </div>
  );
};

export default NaturalLanguageSearch;
