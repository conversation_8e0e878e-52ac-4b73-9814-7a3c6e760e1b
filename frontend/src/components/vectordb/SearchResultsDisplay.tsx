import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { SearchResultResponse } from '@/services/aiendService';
import {
  Copy,
  ChevronDown,
  ChevronUp,
  FileText,
  Filter,
  SortAsc,
  SortDesc,
  RefreshCw,
} from 'lucide-react';

interface SearchResultsDisplayProps {
  results: SearchResultResponse[];
  title: string;
  description?: string;
  executionTime?: number;
  totalCount?: number;
  mode?: string;
  showFilters?: boolean;
}

const SearchResultsDisplay: React.FC<SearchResultsDisplayProps> = ({
  results,
  title,
  description,
  executionTime,
  totalCount,
  mode,
  showFilters = true,
}) => {
  const [expandedResults, setExpandedResults] = useState<Set<string>>(
    new Set()
  );
  const [sortBy, setSortBy] = useState<'score' | 'relevance' | 'file'>('score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [filterByType, setFilterByType] = useState<string>('all');

  // Toggle result expansion
  const toggleResultExpansion = (resultId: string) => {
    const newExpanded = new Set(expandedResults);
    if (newExpanded.has(resultId)) {
      newExpanded.delete(resultId);
    } else {
      newExpanded.add(resultId);
    }
    setExpandedResults(newExpanded);
  };

  // Calculate relevance score for reranking
  const calculateRelevance = (result: SearchResultResponse): number => {
    let relevance = result.score;

    // Boost based on context type
    if (result.context_type) {
      const typeBoosts = {
        function: 1.2,
        class: 1.1,
        method: 1.15,
        interface: 1.05,
        snippet: 1.0,
      };
      relevance *=
        typeBoosts[result.context_type as keyof typeof typeBoosts] || 1.0;
    }

    // Boost based on file type
    const fileExt = result.file_path.split('.').pop()?.toLowerCase();
    if (fileExt) {
      const extBoosts = {
        ts: 1.1,
        tsx: 1.1,
        js: 1.05,
        jsx: 1.05,
        py: 1.1,
        rb: 1.05,
        md: 0.9,
      };
      relevance *= extBoosts[fileExt as keyof typeof extBoosts] || 1.0;
    }

    // Boost based on content length (prefer substantial content)
    const contentLength = result.content.length;
    if (contentLength > 100 && contentLength < 1000) {
      relevance *= 1.1;
    } else if (contentLength > 1000) {
      relevance *= 1.05;
    }

    return relevance;
  };

  // Sort and filter results
  const sortAndFilterResults = (results: SearchResultResponse[]) => {
    let filtered = results;

    // Filter by context type
    if (filterByType !== 'all') {
      filtered = results.filter(
        result =>
          result.context_type === filterByType ||
          (filterByType === 'unknown' && !result.context_type)
      );
    }

    // Sort results
    const sorted = [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'score':
          comparison = a.score - b.score;
          break;
        case 'file':
          comparison = a.file_path.localeCompare(b.file_path);
          break;
        case 'relevance':
          const aRelevance = calculateRelevance(a);
          const bRelevance = calculateRelevance(b);
          comparison = aRelevance - bRelevance;
          break;
        default:
          comparison = a.score - b.score;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return sorted;
  };

  // Copy content to clipboard
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error('Failed to copy to clipboard:', err);
    }
  };

  // Get unique context types from results
  const getUniqueContextTypes = (results: SearchResultResponse[]) => {
    const types = new Set<string>();
    results.forEach(result => {
      if (result.context_type) {
        types.add(result.context_type);
      } else {
        types.add('unknown');
      }
    });
    return Array.from(types).sort();
  };

  // Render search result
  const renderSearchResult = (result: SearchResultResponse, index: number) => {
    const resultId = `${result.id}-${index}`;
    const isExpanded = expandedResults.has(resultId);
    const relevanceScore = calculateRelevance(result);
    const fileExt = result.file_path.split('.').pop()?.toLowerCase();

    return (
      <Card className="mb-3 hover:shadow-md transition-shadow">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <FileText className="h-4 w-4 text-blue-500 flex-shrink-0" />
              <span className="font-mono text-sm text-muted-foreground truncate">
                {result.file_path}
              </span>
              {result.line_start && (
                <Badge variant="outline" className="text-xs flex-shrink-0">
                  Line {result.line_start}
                  {result.line_end &&
                    result.line_end !== result.line_start &&
                    `-${result.line_end}`}
                </Badge>
              )}
              {fileExt && (
                <Badge variant="secondary" className="text-xs flex-shrink-0">
                  {fileExt.toUpperCase()}
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2 flex-shrink-0">
              <Badge variant="secondary" className="text-xs">
                Score: {result.score.toFixed(3)}
              </Badge>
              {relevanceScore !== result.score && (
                <Badge variant="outline" className="text-xs">
                  Rel: {relevanceScore.toFixed(3)}
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => copyToClipboard(result.content)}
                title="Copy content"
              >
                <Copy className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => toggleResultExpansion(resultId)}
                title={isExpanded ? 'Collapse' : 'Expand'}
              >
                {isExpanded ? (
                  <ChevronUp className="h-4 w-4" />
                ) : (
                  <ChevronDown className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
          <div className="flex items-center space-x-2 mt-2">
            {result.context_type && (
              <Badge variant="outline" className="text-xs">
                {result.context_type}
              </Badge>
            )}
            {result.metadata && Object.keys(result.metadata).length > 0 && (
              <Badge variant="secondary" className="text-xs">
                +{Object.keys(result.metadata).length} metadata
              </Badge>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div
            className={`transition-all duration-200 ${isExpanded ? 'max-h-none' : 'max-h-24 overflow-hidden'}`}
          >
            <pre className="text-sm bg-muted p-3 rounded-md overflow-x-auto whitespace-pre-wrap border">
              {result.content}
            </pre>
          </div>
          {!isExpanded && result.content.length > 200 && (
            <div className="mt-2 text-xs text-muted-foreground">
              클릭하여 전체 내용 보기... ({result.content.length} characters)
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  const processedResults = sortAndFilterResults(results);
  const uniqueTypes = getUniqueContextTypes(results);

  if (results.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground py-8">
            검색 결과가 없습니다.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="flex items-center space-x-2">
              <span>{title}</span>
              {totalCount !== undefined && (
                <Badge variant="secondary" className="text-xs">
                  {totalCount}개 결과
                </Badge>
              )}
              {mode && (
                <Badge variant="outline" className="text-xs">
                  {mode} 모드
                </Badge>
              )}
              {executionTime !== undefined && (
                <Badge variant="secondary" className="text-xs">
                  {executionTime.toFixed(2)}s
                </Badge>
              )}
            </CardTitle>
          </div>

          {showFilters && (
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setExpandedResults(new Set())}
                title="Collapse all"
              >
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {description && <CardDescription>{description}</CardDescription>}

        {showFilters && (
          <div className="flex items-center space-x-4 pt-2">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4" />
              <Select value={filterByType} onValueChange={setFilterByType}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {uniqueTypes.map(type => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Select
                value={sortBy}
                onValueChange={(value: any) => setSortBy(value)}
              >
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="score">Score</SelectItem>
                  <SelectItem value="relevance">Relevance</SelectItem>
                  <SelectItem value="file">File Path</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
                }
              >
                {sortOrder === 'asc' ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              Showing {processedResults.length} of {results.length} results
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {processedResults.map((result, index) => (
            <React.Fragment key={result.id || index}>
              {renderSearchResult(result, index)}
            </React.Fragment>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default SearchResultsDisplay;
