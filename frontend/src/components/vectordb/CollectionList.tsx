import React from 'react';
import { Collection } from '@/services/vectorDBService';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/Button';
import { Database, CheckCircle } from 'lucide-react';

interface CollectionListProps {
  collections: Collection[];
  selectedCollection: string | null;
  onSelectCollection: (name: string) => void;
}

const CollectionList: React.FC<CollectionListProps> = ({
  collections,
  selectedCollection,
  onSelectCollection,
}) => {
  if (collections.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <Database className="h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-muted-foreground">컬렉션을 찾을 수 없습니다</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      <h3 className="font-medium mb-2">컬렉션 목록</h3>
      <ScrollArea className="flex-1">
        <div className="space-y-1 pr-1">
          {collections.map(collection => (
            <Button
              key={collection.name}
              variant={
                selectedCollection === collection.name ? 'secondary' : 'ghost'
              }
              className="w-full justify-start h-auto py-2 px-2 text-left"
              onClick={() => onSelectCollection(collection.name)}
            >
              <div className="flex items-center w-full">
                <Database className="h-4 w-4 mr-2 shrink-0" />
                <div className="flex flex-col overflow-hidden">
                  <div className="flex items-center gap-1">
                    <span className="truncate font-medium">
                      {collection.name}
                    </span>
                    {collection.status === 'green' && (
                      <CheckCircle className="h-3 w-3 text-green-500 shrink-0" />
                    )}
                  </div>
                  <div className="text-xs text-muted-foreground truncate">
                    {collection.vectors_count?.toLocaleString() ?? 'N/A'} 벡터
                  </div>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default CollectionList;
