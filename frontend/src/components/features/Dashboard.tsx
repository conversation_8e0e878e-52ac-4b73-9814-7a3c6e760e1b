import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  Search,
  MessageSquare,
  BarChart3,
  Brain,
  Loader2,
  Database,
  FileText,
  Code,
  GitBranch,
} from 'lucide-react';
import { Link } from '@tanstack/react-router';
import { lspService } from '@/services/api';
import type { Statistics, RepositoryInfo } from '@/services/lspService';

export const Dashboard: React.FC = () => {
  const [statistics, setStatistics] = useState<Statistics | null>(null);
  const [repositories, setRepositories] = useState<RepositoryInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('[Dashboard] LSP 데이터 로딩 시작');

        // LSP API에서 통계와 리포지토리 정보를 동시에 가져오기
        const [statsResult, reposResult] = await Promise.allSettled([
          lspService.getStatistics(),
          lspService.getRepositories(),
        ]);

        if (statsResult.status === 'fulfilled') {
          console.log('[Dashboard] LSP 통계 데이터:', statsResult.value);
          setStatistics(statsResult.value);
        } else {
          console.error('[Dashboard] LSP 통계 로딩 실패:', statsResult.reason);
        }

        if (reposResult.status === 'fulfilled') {
          console.log('[Dashboard] LSP 리포지토리 데이터:', reposResult.value);
          setRepositories(reposResult.value);
        } else {
          console.error(
            '[Dashboard] LSP 리포지토리 로딩 실패:',
            reposResult.reason
          );
        }

        // 둘 다 실패한 경우에만 에러 표시
        if (
          statsResult.status === 'rejected' &&
          reposResult.status === 'rejected'
        ) {
          setError('Failed to load dashboard data');
        }
      } catch (err) {
        console.error('[Dashboard] 데이터 로딩 오류:', err);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  // 통계 계산
  const totalRepositories = repositories.length;
  const totalSymbols = statistics?.totalSymbols || 0;
  const totalReferences = statistics?.totalReferences || 0;
  const filesCovered = statistics?.filesCovered || 0;
  const functionCount = statistics?.symbolsByKind?.function || 0;
  const classCount = statistics?.symbolsByKind?.class || 0;

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to CodeBase Intelligence System
        </p>
        {loading && (
          <div className="flex items-center gap-2 mt-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span className="text-sm text-muted-foreground">
              Loading LSP data...
            </span>
          </div>
        )}
        {error && <div className="text-sm text-red-600 mt-2">{error}</div>}
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Files Analyzed
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? (
                <Loader2 className="h-6 w-6 animate-spin" />
              ) : (
                filesCovered.toLocaleString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Files covered by LSP analysis
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Symbols</CardTitle>
            <Code className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? (
                <Loader2 className="h-6 w-6 animate-spin" />
              ) : (
                totalSymbols.toLocaleString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Functions: {functionCount.toLocaleString()}, Classes:{' '}
              {classCount.toLocaleString()}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">References</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? (
                <Loader2 className="h-6 w-6 animate-spin" />
              ) : (
                totalReferences.toLocaleString()
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Symbol references found
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Repositories</CardTitle>
            <GitBranch className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? (
                <Loader2 className="h-6 w-6 animate-spin" />
              ) : (
                totalRepositories
              )}
            </div>
            <p className="text-xs text-muted-foreground">
              Analyzed repositories
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Quick Search</CardTitle>
            <CardDescription>
              Search your codebase with AI-powered semantic search
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link to="/search">
                <Search className="mr-2 h-4 w-4" />
                Start Searching
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Assistant</CardTitle>
            <CardDescription>
              Chat with AI about your codebase and get intelligent insights
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link to="/chat">
                <MessageSquare className="mr-2 h-4 w-4" />
                Start Chatting
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Code Analysis</CardTitle>
            <CardDescription>
              Analyze your codebase structure and get detailed insights
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link to="/analysis">
                <BarChart3 className="mr-2 h-4 w-4" />
                View Analysis
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-1 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>LSP Statistics</CardTitle>
            <CardDescription>
              Detailed breakdown of analyzed code symbols
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading statistics...</span>
              </div>
            ) : statistics ? (
              <div className="space-y-3">
                {Object.entries(statistics.symbolsByKind || {}).map(
                  ([kind, count]) => (
                    <div
                      key={kind}
                      className="flex items-center justify-between"
                    >
                      <span className="text-sm font-medium capitalize">
                        {kind}s
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {count.toLocaleString()}
                      </span>
                    </div>
                  )
                )}
                <div className="border-t pt-3 mt-3">
                  <div className="flex items-center justify-between font-medium">
                    <span className="text-sm">Total Symbols</span>
                    <span className="text-sm">
                      {totalSymbols.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Total References</span>
                    <span className="text-sm text-muted-foreground">
                      {totalReferences.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                No statistics available
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Analyzed Repositories</CardTitle>
            <CardDescription>
              Repositories currently indexed by LSP
            </CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading repositories...</span>
              </div>
            ) : repositories.length > 0 ? (
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {repositories.slice(0, 5).map((repo, index) => (
                  <div
                    key={repo.folderName}
                    className="flex items-center space-x-3"
                  >
                    <div
                      className={`w-2 h-2 rounded-full ${
                        index === 0
                          ? 'bg-green-500'
                          : index === 1
                            ? 'bg-blue-500'
                            : index === 2
                              ? 'bg-purple-500'
                              : 'bg-gray-400'
                      }`}
                    ></div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">
                        {repo.displayName || repo.folderName}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {repo.symbolCount
                          ? `${repo.symbolCount} symbols`
                          : 'No symbols'}{' '}
                        •
                        {repo.fileCount
                          ? ` ${repo.fileCount} files`
                          : ' Unknown files'}{' '}
                        •{new Date(repo.lastModified).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
                {repositories.length > 5 && (
                  <div className="text-xs text-muted-foreground text-center pt-2">
                    And {repositories.length - 5} more repositories...
                  </div>
                )}
              </div>
            ) : (
              <div className="text-sm text-muted-foreground">
                No repositories analyzed yet
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
