import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  GitBranch,
  Plus,
  Trash2,
  RefreshCw,
  Download,
  AlertCircle,
} from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Label } from '@/components/ui/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { repositoryApi, lspService } from '@/services/api';
import { Repository, RepositoryCloneRequest, RepositoryBranch } from '@/types';
import { useOllamaStore } from '@/stores/ollamaStore';

export const SettingsPage: React.FC = () => {
  const [repositoryForm, setRepositoryForm] = useState<RepositoryCloneRequest>({
    url: '',
    username: '',
    password: '',
  });
  const [selectedRepository, setSelectedRepository] = useState<string>('');
  const [selectedBranch, setSelectedBranch] = useState<string>('');

  const queryClient = useQueryClient();

  // Ollama store
  const {
    selectedModel,
    availableModels,
    modelsLoading,
    modelsError,
    serviceStatus,
    setSelectedModel,
    fetchAvailableModels,
  } = useOllamaStore();

  // Fetch repositories
  const { data: repositoriesData, isLoading: repositoriesLoading } = useQuery({
    queryKey: ['repositories'],
    queryFn: () => repositoryApi.getRepositories(),
  });

  // Fetch Ollama models when the component mounts
  useEffect(() => {
    fetchAvailableModels();
  }, [fetchAvailableModels]);

  // Fetch branches for selected repository
  const { data: branchesData, isLoading: branchesLoading } = useQuery({
    queryKey: ['branches', selectedRepository],
    queryFn: () => repositoryApi.getBranches(selectedRepository),
    enabled: !!selectedRepository,
  });

  const repositories: Repository[] = repositoriesData?.data?.repositories || [];
  const branches: RepositoryBranch[] = branchesData?.data?.branches || [];

  // Find repositories that are currently cloning
  const cloningRepositories = repositories.filter(
    repo => repo.status === 'cloning' || repo.status === 'pending'
  );

  // Setup polling for cloning repositories
  useQuery({
    queryKey: ['repositoryStatus', cloningRepositories.map(r => r.id)],
    queryFn: async () => {
      const statuses = await Promise.all(
        cloningRepositories.map(repo =>
          repositoryApi.getRepositoryStatus(repo.id.toString())
        )
      );
      // Once all monitored repositories are in a final state, invalidate the main list
      const allDone = statuses.every(
        s =>
          s.repository.status !== 'cloning' && s.repository.status !== 'pending'
      );
      if (allDone) {
        queryClient.invalidateQueries({ queryKey: ['repositories'] });
      }
      return statuses;
    },
    // Refetch every 3 seconds if there are cloning repositories
    refetchInterval: cloningRepositories.length > 0 ? 3000 : false,
    enabled: cloningRepositories.length > 0,
  });

  // Clone repository mutation
  const cloneRepositoryMutation = useMutation({
    mutationFn: repositoryApi.cloneRepository,
    onSuccess: () => {
      toast.success('Repository clone started successfully!');
      setRepositoryForm({ url: '', username: '', password: '' });
      queryClient.invalidateQueries({ queryKey: ['repositories'] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to clone repository: ${error.message}`);
    },
  });

  // Switch branch mutation
  const switchBranchMutation = useMutation({
    mutationFn: ({
      repositoryId,
      branch,
    }: {
      repositoryId: string;
      branch: string;
    }) => repositoryApi.switchBranch(repositoryId, branch),
    onSuccess: () => {
      toast.success('Branch switched and indexing started!');
      queryClient.invalidateQueries({ queryKey: ['repositories'] });
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch branch: ${error.message}`);
    },
  });

  // Delete repository mutation
  const deleteRepositoryMutation = useMutation({
    mutationFn: async (repositoryId: string) => {
      console.log('[SettingsPage] 리포지토리 삭제 시작:', repositoryId);

      // 현재 로드된 리포지토리 목록에서 찾기 (더 안전함)
      const repositoriesResponse = await repositoryApi.getRepositories();
      const repositories = repositoriesResponse?.data?.repositories || [];
      const repository = repositories.find(
        (repo: any) => repo.id.toString() === repositoryId
      );

      if (!repository || !repository.name) {
        throw new Error(`Repository with ID ${repositoryId} not found`);
      }

      const repositoryFolderName = `${repository.name}_${repository.id}`;
      console.log('[SettingsPage] 리포지토리 정보:', {
        name: repository.name,
        id: repository.id,
        folderName: repositoryFolderName,
      });

      // 1. Backend에서 삭제 (Aiend 알림 포함)
      const backendResult = await repositoryApi.deleteRepository(repositoryId);
      console.log('[SettingsPage] Backend 삭제 완료:', backendResult);

      // 2. LSP 서버에서 삭제
      try {
        const lspResult =
          await lspService.deleteRepository(repositoryFolderName);
        console.log('[SettingsPage] LSP 서버 삭제 완료:', lspResult);
        return { backend: backendResult, lsp: lspResult };
      } catch (lspError) {
        console.error('[SettingsPage] LSP 서버 삭제 실패:', lspError);
        // LSP 삭제 실패해도 backend는 이미 삭제되었으므로 성공으로 처리
        return {
          backend: backendResult,
          lsp: { status: 'error', message: 'LSP deletion failed' },
        };
      }
    },
    onSuccess: result => {
      console.log('[SettingsPage] 전체 삭제 결과:', result);

      let message = 'Repository deleted successfully from backend';
      if (result.lsp.status === 'ok') {
        message += ' and LSP server';
      } else {
        message += ' (LSP server deletion failed)';
      }

      toast.success(message);
      setSelectedRepository('');
      setSelectedBranch('');
      queryClient.invalidateQueries({ queryKey: ['repositories'] });
    },
    onError: (error: Error) => {
      console.error('[SettingsPage] 리포지토리 삭제 실패:', error);
      toast.error(`Failed to delete repository: ${error.message}`);
    },
  });

  const handleCloneRepository = () => {
    if (!repositoryForm.url.trim()) {
      toast.error('Repository URL is required');
      return;
    }

    cloneRepositoryMutation.mutate(repositoryForm);
  };

  const handleSwitchBranch = () => {
    if (!selectedRepository || !selectedBranch) {
      toast.error('Please select both repository and branch');
      return;
    }

    switchBranchMutation.mutate({
      repositoryId: selectedRepository,
      branch: selectedBranch,
    });
  };

  const handleDeleteRepository = () => {
    if (!selectedRepository) {
      toast.error('Please select a repository to delete');
      return;
    }

    if (
      confirm(
        'Are you sure you want to delete this repository? This action cannot be undone.'
      )
    ) {
      deleteRepositoryMutation.mutate(selectedRepository);
    }
  };

  const getStatusColor = (status: Repository['status']) => {
    switch (status) {
      case 'ready':
        return 'text-green-600';
      case 'cloning':
      case 'indexing':
        return 'text-blue-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: Repository['status']) => {
    switch (status) {
      case 'ready':
        return <GitBranch className="h-4 w-4" />;
      case 'cloning':
      case 'indexing':
        return <RefreshCw className="h-4 w-4 animate-spin" />;
      case 'error':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <GitBranch className="h-4 w-4" />;
    }
  };

  const handleRefreshModels = () => {
    fetchAvailableModels();
    toast.success('Refreshed model list from Ollama.');
  };

  // Update selected branch when repository changes
  useEffect(() => {
    if (selectedRepository) {
      const repo = repositories.find(r => String(r.id) === selectedRepository);
      if (repo) {
        setSelectedBranch(repo.currentBranch ?? '');
      }
    }
  }, [selectedRepository, repositories]);

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold font-heading text-black tracking-tight">
          Settings
        </h1>
        <p className="font-body text-gray-700">
          Manage your repositories and configure indexing settings.
        </p>
      </div>

      {/* Add Repository Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add Repository
          </CardTitle>
          <CardDescription>
            Clone a new repository to start analyzing your codebase.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="repository-url">Repository URL *</Label>
              <Input
                id="repository-url"
                placeholder="https://github.com/username/repository.git"
                value={repositoryForm.url}
                onChange={e =>
                  setRepositoryForm(prev => ({ ...prev, url: e.target.value }))
                }
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="username">Username (optional)</Label>
              <Input
                id="username"
                placeholder="Git username"
                value={repositoryForm.username}
                onChange={e =>
                  setRepositoryForm(prev => ({
                    ...prev,
                    username: e.target.value,
                  }))
                }
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="password">Password/Token (optional)</Label>
            <Input
              id="password"
              type="password"
              placeholder="Git password or personal access token"
              value={repositoryForm.password}
              onChange={e =>
                setRepositoryForm(prev => ({
                  ...prev,
                  password: e.target.value,
                }))
              }
            />
          </div>
          <Button
            onClick={handleCloneRepository}
            disabled={
              cloneRepositoryMutation.isPending || !repositoryForm.url.trim()
            }
            className="w-full md:w-auto"
          >
            {cloneRepositoryMutation.isPending ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Cloning...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Clone Repository
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Repository List Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Repository List
          </CardTitle>
          <CardDescription>
            View and manage all cloned repositories.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {repositoriesLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading repositories...</span>
            </div>
          ) : repositories.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No repositories found. Clone a repository to get started.
            </div>
          ) : (
            <div className="space-y-3">
              {repositories.map(repo => (
                <div
                  key={repo.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center gap-3">
                    <span className={getStatusColor(repo.status)}>
                      {getStatusIcon(repo.status)}
                    </span>
                    <div>
                      <div className="font-medium text-black">{repo.name}</div>
                      <div className="text-sm text-gray-600">
                        {repo.url} • Branch: {repo.currentBranch}
                      </div>
                      <div className="text-xs text-gray-500">
                        Status: {repo.status}
                        {repo.lastIndexed && (
                          <span>
                            {' '}
                            • Last indexed:{' '}
                            {new Date(repo.lastIndexed).toLocaleDateString()}
                          </span>
                        )}
                      </div>
                      {repo.error && (
                        <div className="text-xs text-red-600 mt-1">
                          Error: {repo.error}
                        </div>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => {
                      if (
                        confirm(
                          `Are you sure you want to delete "${repo.name}"? This will remove all repository data and files. This action cannot be undone.`
                        )
                      ) {
                        deleteRepositoryMutation.mutate(repo.id.toString());
                      }
                    }}
                    disabled={deleteRepositoryMutation.isPending}
                  >
                    {deleteRepositoryMutation.isPending ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Repository Management Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Branch Management
          </CardTitle>
          <CardDescription>
            Switch branches for existing repositories and trigger re-indexing.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {repositoriesLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading repositories...</span>
            </div>
          ) : repositories.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No repositories found. Clone a repository to get started.
            </div>
          ) : (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="repository-select">Select Repository</Label>
                  <Select
                    value={selectedRepository}
                    onValueChange={setSelectedRepository}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a repository" />
                    </SelectTrigger>
                    <SelectContent>
                      {repositories.map(repo => (
                        <SelectItem key={repo.id} value={repo.id.toString()}>
                          <div className="flex items-center gap-2">
                            <span className={getStatusColor(repo.status)}>
                              {getStatusIcon(repo.status)}
                            </span>
                            <span>{repo.name}</span>
                            <span className="text-xs text-muted-foreground">
                              ({repo.currentBranch})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="branch-select">Select Branch</Label>
                  <Select
                    value={selectedBranch}
                    onValueChange={setSelectedBranch}
                    disabled={!selectedRepository || branchesLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map(branch => (
                        <SelectItem key={branch.name} value={branch.name}>
                          <div className="flex items-center gap-2">
                            <span>{branch.name}</span>
                            {branch.isDefault && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                                default
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={handleSwitchBranch}
                  disabled={
                    !selectedRepository ||
                    !selectedBranch ||
                    switchBranchMutation.isPending
                  }
                >
                  {switchBranchMutation.isPending ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Switching...
                    </>
                  ) : (
                    <>
                      <GitBranch className="mr-2 h-4 w-4" />
                      Switch & Index
                    </>
                  )}
                </Button>

                <Button
                  variant="destructive"
                  onClick={handleDeleteRepository}
                  disabled={
                    !selectedRepository || deleteRepositoryMutation.isPending
                  }
                >
                  {deleteRepositoryMutation.isPending ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    <>
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Repository
                    </>
                  )}
                </Button>
              </div>

              {/* Repository Status Display */}
              {selectedRepository && (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  {(() => {
                    const repo = repositories.find(
                      r => String(r.id) === selectedRepository
                    );
                    if (!repo) return null;

                    return (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <span className={getStatusColor(repo.status)}>
                            {getStatusIcon(repo.status)}
                          </span>
                          <span className="font-medium">{repo.name}</span>
                          <span className="text-sm text-muted-foreground">
                            Status: {repo.status}
                          </span>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          <p>URL: {repo.url}</p>
                          <p>Current Branch: {repo.currentBranch}</p>
                          {repo.lastIndexed && (
                            <p>
                              Last Indexed:{' '}
                              {new Date(repo.lastIndexed).toLocaleString()}
                            </p>
                          )}
                          {repo.error && (
                            <p className="text-red-600">Error: {repo.error}</p>
                          )}
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Ollama Model Selection Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Ollama Model Settings
          </CardTitle>
          <CardDescription>
            Select the default Ollama model for AI-powered commit analysis and
            other LLM features.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {modelsLoading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading Ollama models...</span>
            </div>
          ) : modelsError ? (
            <div className="text-center py-8">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-red-600 mb-2">Failed to load Ollama models</p>
              <p className="text-sm text-gray-500 mb-4">{modelsError}</p>
              <Button
                onClick={() =>
                  queryClient.invalidateQueries({ queryKey: ['ollama-models'] })
                }
                variant="outline"
                size="sm"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </div>
          ) : availableModels.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
              <p className="text-gray-600 mb-2">No Ollama models found</p>
              <p className="text-sm text-gray-500">
                Make sure Ollama is running and has models installed.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="ollama-model-select">Default Model</Label>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a model" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels.map(model => (
                        <SelectItem key={model.name} value={model.name}>
                          <div className="flex items-center gap-2">
                            <span>{model.name}</span>
                            {model.size && (
                              <span className="text-xs text-gray-500">
                                (
                                {Math.round(
                                  (model.size / (1024 * 1024 * 1024)) * 10
                                ) / 10}{' '}
                                GB)
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Service Status</Label>
                  <div className="flex items-center gap-2 p-2 border rounded">
                    <div
                      className={`w-2 h-2 rounded-full ${
                        serviceStatus === 'available'
                          ? 'bg-green-500'
                          : serviceStatus === 'error'
                            ? 'bg-red-500'
                            : serviceStatus === 'unavailable'
                              ? 'bg-yellow-500'
                              : 'bg-gray-500'
                      }`}
                    />
                    <span className="text-sm capitalize">{serviceStatus}</span>
                  </div>
                </div>
              </div>

              {selectedModel && (
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium mb-2">
                    Selected Model Information
                  </h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>
                      <strong>Name:</strong> {selectedModel}
                    </p>
                    {(() => {
                      const modelInfo = availableModels.find(
                        m => m.name === selectedModel
                      );
                      if (!modelInfo) return null;

                      return (
                        <>
                          {modelInfo.size && (
                            <p>
                              <strong>Size:</strong>{' '}
                              {Math.round(
                                (modelInfo.size / (1024 * 1024 * 1024)) * 10
                              ) / 10}{' '}
                              GB
                            </p>
                          )}
                          {modelInfo.modified_at && (
                            <p>
                              <strong>Modified:</strong>{' '}
                              {new Date(modelInfo.modified_at).toLocaleString()}
                            </p>
                          )}
                        </>
                      );
                    })()}
                  </div>
                </div>
              )}

              <div className="flex items-center gap-2">
                <Button
                  onClick={() =>
                    queryClient.invalidateQueries({
                      queryKey: ['ollama-models'],
                    })
                  }
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh Models
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
