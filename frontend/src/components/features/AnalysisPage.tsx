import React from 'react';
import {
  Bar<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ircle,
} from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useRepositoryStore } from '@/stores/repositoryStore';

export const AnalysisPage: React.FC = () => {
  // Global repository state
  const { context } = useRepositoryStore();

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Code Analysis</h1>
        <p className="text-muted-foreground">
          Comprehensive analysis of your codebase structure and quality
          {context.repository_name && (
            <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
              Repository: {context.repository_name}
            </span>
          )}
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Code Quality</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">8.5/10</div>
            <p className="text-xs text-muted-foreground">
              +0.3 from last analysis
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Complexity Score
            </CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">6.2</div>
            <p className="text-xs text-muted-foreground">
              Average cyclomatic complexity
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Technical Debt
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12h</div>
            <p className="text-xs text-muted-foreground">
              Estimated time to fix
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Test Coverage</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">78%</div>
            <p className="text-xs text-muted-foreground">+5% from last month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-4 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>File Analysis</CardTitle>
            <CardDescription>
              Breakdown of your codebase by file types and languages
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  <span className="text-sm">Python</span>
                </div>
                <span className="text-sm font-medium">45 files (67%)</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded"></div>
                  <span className="text-sm">JavaScript</span>
                </div>
                <span className="text-sm font-medium">18 files (27%)</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  <span className="text-sm">TypeScript</span>
                </div>
                <span className="text-sm font-medium">4 files (6%)</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Complexity Distribution</CardTitle>
            <CardDescription>
              Functions grouped by cyclomatic complexity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Low (1-5)</span>
                <span className="text-sm font-medium">234 functions (78%)</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Medium (6-10)</span>
                <span className="text-sm font-medium">45 functions (15%)</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">High (11-20)</span>
                <span className="text-sm font-medium">18 functions (6%)</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Very High (20+)</span>
                <span className="text-sm font-medium">3 functions (1%)</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Issues & Recommendations</CardTitle>
          <CardDescription>
            AI-powered analysis of potential improvements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <AlertTriangle className="h-5 w-5 text-yellow-500 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium">
                  High Complexity Function
                </h4>
                <p className="text-sm text-muted-foreground">
                  Function `process_data` in `data_processor.py` has complexity
                  of 23. Consider refactoring.
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  View Function
                </Button>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium">Good Test Coverage</h4>
                <p className="text-sm text-muted-foreground">
                  Module `auth.py` has excellent test coverage at 95%.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3 p-3 border rounded-lg">
              <AlertTriangle className="h-5 w-5 text-red-500 mt-0.5" />
              <div className="flex-1">
                <h4 className="text-sm font-medium">
                  Potential Security Issue
                </h4>
                <p className="text-sm text-muted-foreground">
                  SQL query in `user_service.py` may be vulnerable to injection
                  attacks.
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  Review Code
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-center">
        <Button>
          <BarChart3 className="mr-2 h-4 w-4" />
          Run Full Analysis
        </Button>
      </div>
    </div>
  );
};
