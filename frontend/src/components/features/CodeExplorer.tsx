import React, {
  useState,
  useEffect,
  useRef,
  createContext,
  useContext,
} from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import {
  Code,
  FolderTree,
  Search,
  RefreshCw,
  RotateCcw,
  ChevronDown,
  ChevronUp,
  Palette,
} from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Input } from '@/components/ui/Input';
import { FileTree } from './FileTree';
import { CodeEditor } from './CodeEditor';
import { repositoryApi, fileExplorerApi } from '@/services/api';
import { FileTreeNode, FileContent } from '@/types';
import { useRepositoryStore } from '@/stores/repositoryStore';
import { useRepositories } from '@/hooks/useRepositories';

// Theme types
type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

// Theme context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// Theme hook
const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const CodeExplorer: React.FC = () => {
  // Global repository state
  const { context, setRepositoryContext } = useRepositoryStore();

  // Global repositories data - API 호출이 포함된 훅 사용
  const {
    repositories,
    readyRepositories,
    loading: repositoriesLoading,
    error: repositoriesError,
    refetch,
  } = useRepositories();

  const queryClient = useQueryClient();

  // Local state
  const [selectedFileId, setSelectedFileId] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [fileTree, setFileTree] = useState<FileTreeNode[]>([]);
  const [isControlsExpanded, setIsControlsExpanded] = useState<boolean>(false); // 기본값을 false로 변경
  const [treeWidth, setTreeWidth] = useState<number>(300);
  const [isResizing, setIsResizing] = useState<boolean>(false);
  // 테마 상태 - localStorage에서 초기값 읽기
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('code-explorer-theme') as Theme;
      return savedTheme || 'light';
    }
    return 'light';
  });
  const resizeRef = useRef<HTMLDivElement>(null);

  // 테마 변경 시 localStorage에 저장
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('code-explorer-theme', theme);
    }
  }, [theme]);

  // Repositories are now managed globally via useRepositoryData hook

  // Fetch file tree for selected repository and branch
  const {
    data: fileTreeData,
    isLoading: fileTreeLoading,
    refetch: refetchFileTree,
    error: fileTreeError,
  } = useQuery<{
    data: {
      tree: FileTreeNode[];
      repository: {
        id: number;
        name: string;
        current_branch: string;
      };
      stats: {
        total_files: number;
        total_directories: number;
        total_size: number;
        languages: string[];
      };
    };
  }>({
    queryKey: ['file-tree', context.repository_id, context.branch],
    queryFn: async () => {
      const result = await fileExplorerApi.getFileTree(
        context.repository_id!.toString(),
        {
          include_files: true,
          max_depth: 3,
          lazy_load: false,
        }
      );
      return result as {
        data: {
          tree: FileTreeNode[];
          repository: {
            id: number;
            name: string;
            current_branch: string;
          };
          stats: {
            total_files: number;
            total_directories: number;
            total_size: number;
            languages: string[];
          };
        };
      };
    },
    enabled: !!context.repository_id,
    retry: 1,
  });

  // Fetch file content for selected file
  const { data: fileContentData, isLoading: fileContentLoading } = useQuery<{
    data: FileContent;
  }>({
    queryKey: ['file-content', context.repository_id, selectedFileId],
    queryFn: async (): Promise<{ data: FileContent }> => {
      return (await fileExplorerApi.getFileContent(
        context.repository_id!.toString(),
        selectedFileId
      )) as { data: FileContent };
    },
    enabled: !!context.repository_id && !!selectedFileId,
  });

  // Branch checkout mutation
  const checkoutMutation = useMutation({
    mutationFn: ({
      repositoryId,
      branch,
    }: {
      repositoryId: string;
      branch: string;
    }) => repositoryApi.switchBranch(repositoryId, branch),
    onSuccess: data => {
      const result = data as { message?: string };
      toast.success(result.message || 'Branch switched successfully');
      // Invalidate and refetch file tree for the new branch
      queryClient.invalidateQueries({
        queryKey: ['file-tree', context.repository_id],
      });
      // Clear selected file since it might not exist in the new branch
      setSelectedFileId('');
      // Also invalidate file content queries
      queryClient.invalidateQueries({
        queryKey: ['file-content', context.repository_id],
      });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to switch branch');
    },
  });

  // Use global repositories data instead of local query
  const fileContent: FileContent | undefined = fileContentData?.data;

  // Repository auto-selection is now handled globally in useRepositories hook

  // Update file tree when data is fetched
  useEffect(() => {
    console.log('[CodeExplorer] 파일트리 데이터 업데이트:', {
      fileTreeData,
      hasData: !!fileTreeData,
      hasDataTree: !!fileTreeData?.data,
      hasTree: !!fileTreeData?.data?.tree,
      treeLength: fileTreeData?.data?.tree?.length,
      tree: fileTreeData?.data?.tree,
    });

    if (fileTreeData?.data?.tree) {
      console.log('[CodeExplorer] 파일트리 설정:', fileTreeData.data.tree);
      setFileTree(fileTreeData.data.tree);
    } else {
      console.log('[CodeExplorer] 파일트리 비우기');
      setFileTree([]);
    }
  }, [fileTreeData]);

  // Check if repository needs scanning
  const needsScanning =
    context.repository_id &&
    !fileTreeLoading &&
    (!fileTreeData?.data?.tree || fileTreeData.data.tree.length === 0) &&
    !fileTreeError;

  // 파일트리 에러 로깅
  useEffect(() => {
    if (fileTreeError) {
      console.error('[CodeExplorer] 파일트리 로딩 에러:', fileTreeError);
    }
  }, [fileTreeError]);

  const handleFileSelect = (node: FileTreeNode) => {
    if (node.type === 'file') {
      // Extract numeric ID from "file_123" format
      const fileId = node.id.startsWith('file_')
        ? node.id.substring(5)
        : node.id;
      setSelectedFileId(fileId);
    }
  };

  const handleDirectoryToggle = (node: FileTreeNode) => {
    // Update the file tree to reflect the expanded/collapsed state
    const updateNodeExpansion = (nodes: FileTreeNode[]): FileTreeNode[] => {
      return nodes.map(n => {
        if (n.id === node.id) {
          return { ...n, expanded: node.expanded };
        }
        if (n.children) {
          return { ...n, children: updateNodeExpansion(n.children) };
        }
        return n;
      });
    };

    setFileTree(updateNodeExpansion(fileTree));
  };

  const handleRefresh = () => {
    if (context.repository_id) {
      refetchFileTree();
      toast.success('File tree refreshed');
    }
  };

  const handleScanRepository = async () => {
    if (!context.repository_id) return;

    try {
      toast.loading('Scanning repository...', { id: 'scan-repo' });

      await repositoryApi.scanRepository(context.repository_id.toString());

      // Wait a moment then refresh the file tree
      setTimeout(() => {
        refetchFileTree();
        toast.success('Repository scan completed', { id: 'scan-repo' });
      }, 2000);
    } catch (error) {
      console.error('Failed to scan repository:', error);
      toast.error('Failed to scan repository', { id: 'scan-repo' });
    }
  };

  // Resize handlers
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsResizing(true);
    e.preventDefault();
  };

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;

      const newWidth = e.clientX;
      if (newWidth >= 200 && newWidth <= 600) {
        setTreeWidth(newWidth);
      }
    };

    const handleMouseUp = () => {
      setIsResizing(false);
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const handleSearch = () => {
    if (searchQuery.trim() && context.repository_id) {
      // TODO: Implement search functionality
      toast('Search functionality coming soon');
    }
  };

  if (repositoriesLoading) {
    return (
      <div className="py-4">
        <div className="flex items-center justify-center py-8">
          <RotateCcw className="h-6 w-6 animate-spin mr-2" />
          <span className="font-body text-black">Loading repositories...</span>
        </div>
      </div>
    );
  }

  if (repositoriesError) {
    return (
      <div className="py-4">
        <Card>
          <CardHeader>
            <CardTitle className="font-heading text-black">
              Error Loading Repositories
            </CardTitle>
            <CardDescription className="font-body text-red-600">
              {repositoriesError}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => refetch()} variant="outline">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (repositories.length === 0) {
    return (
      <div className="py-4">
        <Card>
          <CardHeader>
            <CardTitle className="font-heading text-black">
              No Repositories Found
            </CardTitle>
            <CardDescription className="font-body text-gray-700">
              Please clone a repository in Settings first.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <ThemeContext.Provider value={{ theme, setTheme }}>
      <div
        className={`w-full max-w-none h-full flex flex-col space-y-4 ${
          theme === 'dark' ? 'dark bg-black' : 'bg-white'
        }`}
      >
        {/* Header */}
        <div className="flex items-center justify-between flex-shrink-0">
          <div>
            <h1
              className={`text-3xl font-bold font-heading tracking-tight ${
                theme === 'dark' ? 'text-white' : 'text-black'
              }`}
            >
              Code Explorer
            </h1>
          </div>
          <div className="flex items-center gap-2">
            {/* Theme Selector */}
            <Select
              value={theme}
              onValueChange={(value: Theme) => setTheme(value)}
            >
              <SelectTrigger
                className="w-24"
                style={{
                  backgroundColor: theme === 'dark' ? '#0d1117' : '#ffffff',
                  borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                  color: theme === 'dark' ? '#ffffff' : '#000000',
                }}
              >
                <SelectValue
                  style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
                />
              </SelectTrigger>
              <SelectContent
                style={{
                  backgroundColor: theme === 'dark' ? '#0d1117' : '#ffffff',
                  borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                  color: theme === 'dark' ? '#ffffff' : '#000000',
                }}
              >
                <SelectItem
                  value="light"
                  style={{
                    color: theme === 'dark' ? '#ffffff' : '#000000',
                    backgroundColor: 'transparent',
                  }}
                  className={
                    theme === 'dark'
                      ? 'hover:bg-zinc-800 focus:bg-zinc-800'
                      : 'hover:bg-gray-100 focus:bg-gray-100'
                  }
                >
                  <div className="flex items-center gap-2">
                    <Palette
                      className="h-4 w-4"
                      style={{
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    />
                    <span
                      className="font-body"
                      style={{
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    >
                      Light
                    </span>
                  </div>
                </SelectItem>
                <SelectItem
                  value="dark"
                  style={{
                    color: theme === 'dark' ? '#ffffff' : '#000000',
                    backgroundColor: 'transparent',
                  }}
                  className={
                    theme === 'dark'
                      ? 'hover:bg-zinc-800 focus:bg-zinc-800'
                      : 'hover:bg-gray-100 focus:bg-gray-100'
                  }
                >
                  <div className="flex items-center gap-2">
                    <Palette
                      className="h-4 w-4"
                      style={{
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    />
                    <span
                      className="font-body"
                      style={{
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    >
                      Dark
                    </span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
            <Button
              onClick={() => setIsControlsExpanded(!isControlsExpanded)}
              size="sm"
              variant="ghost"
              className="p-2"
              style={{
                color: theme === 'dark' ? '#ffffff' : '#000000',
              }}
            >
              {isControlsExpanded ? (
                <ChevronUp
                  className="h-4 w-4"
                  style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
                />
              ) : (
                <ChevronDown
                  className="h-4 w-4"
                  style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
                />
              )}
            </Button>
          </div>
        </div>

        {/* Controls */}
        {isControlsExpanded && (
          <Card
            className={`flex-shrink-0 ${
              theme === 'dark'
                ? 'bg-zinc-900 border-zinc-700'
                : 'bg-white border-gray-200'
            }`}
          >
            <CardHeader>
              <CardTitle
                className={`flex items-center justify-between font-heading ${
                  theme === 'dark' ? 'text-white' : 'text-black'
                }`}
              >
                <div className="flex items-center gap-2">
                  <Code
                    className="h-5 w-5"
                    style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
                  />
                  Repository Selection
                </div>
                <Button
                  onClick={handleRefresh}
                  disabled={!context.repository_id}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2"
                  style={{
                    backgroundColor: theme === 'dark' ? '#0d1117' : '#ffffff',
                    borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                    color: theme === 'dark' ? '#ffffff' : '#000000',
                  }}
                >
                  <RefreshCw
                    className="h-4 w-4"
                    style={{ color: theme === 'dark' ? '#ffffff' : '#000000' }}
                  />
                  Refresh
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Repository Selection */}
                <div className="space-y-2">
                  <label
                    className={`text-sm font-medium font-heading ${
                      theme === 'dark' ? 'text-white' : 'text-black'
                    }`}
                  >
                    Repository
                  </label>
                  <Select
                    value={context.repository_id?.toString() || ''}
                    onValueChange={value => {
                      const repo = repositories.find(
                        r => r.id.toString() === value
                      );
                      if (repo) {
                        setRepositoryContext(
                          repo.id,
                          repo.name,
                          repo.current_branch ?? ''
                        );
                      }
                    }}
                  >
                    <SelectTrigger
                      style={{
                        backgroundColor:
                          theme === 'dark' ? '#0d1117' : '#ffffff',
                        borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    >
                      <SelectValue
                        placeholder="Select repository"
                        style={{
                          color: theme === 'dark' ? '#ffffff' : '#000000',
                        }}
                      />
                    </SelectTrigger>
                    <SelectContent
                      style={{
                        backgroundColor:
                          theme === 'dark' ? '#0d1117' : '#ffffff',
                        borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    >
                      {readyRepositories.map(repo => (
                        <SelectItem
                          key={repo.id}
                          value={repo.id.toString()}
                          style={{
                            color: theme === 'dark' ? '#ffffff' : '#000000',
                            backgroundColor: 'transparent',
                          }}
                          className={
                            theme === 'dark'
                              ? 'hover:bg-zinc-800 focus:bg-zinc-800'
                              : 'hover:bg-gray-100 focus:bg-gray-100'
                          }
                        >
                          <div className="flex items-center gap-2">
                            <span
                              className="font-body"
                              style={{
                                color: theme === 'dark' ? '#ffffff' : '#000000',
                              }}
                            >
                              {repo.name}
                            </span>
                            <span
                              className="text-xs"
                              style={{
                                color: theme === 'dark' ? '#cccccc' : '#6b7280',
                              }}
                            >
                              ({repo.current_branch})
                            </span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Branch Selection */}
                {context.repository_id && (
                  <div className="space-y-2">
                    <label
                      className={`text-sm font-medium font-heading ${
                        theme === 'dark' ? 'text-white' : 'text-black'
                      }`}
                    >
                      Branch{' '}
                      {context.branch && (
                        <span className="text-xs text-blue-600 font-normal">
                          (Current: {context.branch})
                        </span>
                      )}
                      {checkoutMutation.isPending && (
                        <span className="text-xs text-orange-600 font-normal">
                          (Switching...)
                        </span>
                      )}
                    </label>
                    <div className="flex items-center gap-2">
                      <Select
                        value={context.branch || ''}
                        onValueChange={value => {
                          if (
                            context.repository_id &&
                            context.repository_name
                          ) {
                            // Update context first
                            setRepositoryContext(
                              context.repository_id,
                              context.repository_name,
                              value
                            );
                            // Then trigger checkout
                            checkoutMutation.mutate({
                              repositoryId: context.repository_id.toString(),
                              branch: value,
                            });
                          }
                        }}
                        disabled={
                          !context.repository_id || checkoutMutation.isPending
                        }
                      >
                        <SelectTrigger
                          style={{
                            backgroundColor:
                              theme === 'dark' ? '#0d1117' : '#ffffff',
                            borderColor:
                              theme === 'dark' ? '#30363d' : '#d1d5db',
                            color: theme === 'dark' ? '#ffffff' : '#000000',
                          }}
                        >
                          <SelectValue
                            placeholder="Select branch"
                            style={{
                              color: theme === 'dark' ? '#ffffff' : '#000000',
                            }}
                          />
                        </SelectTrigger>
                        <SelectContent
                          style={{
                            backgroundColor:
                              theme === 'dark' ? '#161b22' : '#ffffff',
                            borderColor:
                              theme === 'dark' ? '#30363d' : '#d1d5db',
                            color: theme === 'dark' ? '#ffffff' : '#000000',
                          }}
                        >
                          {/* Branch options will be populated from repository data */}
                          {context.repository_id &&
                            repositories
                              .find(r => r.id === context.repository_id)
                              ?.branches?.map(branch => (
                                <SelectItem
                                  key={branch}
                                  value={branch}
                                  style={{
                                    color:
                                      theme === 'dark' ? '#ffffff' : '#000000',
                                    backgroundColor: 'transparent',
                                  }}
                                  className={
                                    theme === 'dark'
                                      ? 'hover:bg-zinc-800 focus:bg-zinc-800'
                                      : 'hover:bg-gray-100 focus:bg-gray-100'
                                  }
                                >
                                  <div className="flex items-center gap-2">
                                    <span
                                      className="font-body"
                                      style={{
                                        color:
                                          theme === 'dark'
                                            ? '#ffffff'
                                            : '#000000',
                                      }}
                                    >
                                      {branch}
                                    </span>
                                    {branch ===
                                      repositories.find(
                                        r => r.id === context.repository_id
                                      )?.current_branch && (
                                      <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                                        current
                                      </span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                {/* Search */}
                <div className="space-y-2">
                  <label
                    className={`text-sm font-medium font-heading ${
                      theme === 'dark' ? 'text-white' : 'text-black'
                    }`}
                  >
                    Search Files
                  </label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Search files..."
                      value={searchQuery}
                      onChange={e => setSearchQuery(e.target.value)}
                      onKeyDown={e => e.key === 'Enter' && handleSearch()}
                      style={{
                        backgroundColor:
                          theme === 'dark' ? '#0d1117' : '#ffffff',
                        borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    />
                    <Button
                      onClick={handleSearch}
                      disabled={!searchQuery.trim() || !context.repository_id}
                      size="sm"
                      variant="outline"
                      style={{
                        backgroundColor:
                          theme === 'dark' ? '#0d1117' : '#ffffff',
                        borderColor: theme === 'dark' ? '#30363d' : '#d1d5db',
                        color: theme === 'dark' ? '#ffffff' : '#000000',
                      }}
                    >
                      <Search
                        className="h-4 w-4"
                        style={{
                          color: theme === 'dark' ? '#ffffff' : '#000000',
                        }}
                      />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        {context.repository_id && (
          <div className="flex gap-0 flex-1 min-h-0">
            {/* Left Panel - File Tree */}
            <div
              className={`flex flex-col min-h-0 border-r ${
                theme === 'dark' ? 'border-zinc-700' : 'border-gray-200'
              }`}
              style={{ width: `${treeWidth}px` }}
            >
              <Card
                className={`flex-1 flex flex-col min-h-0 border-0 rounded-none ${
                  theme === 'dark' ? 'bg-zinc-900' : 'bg-white'
                }`}
              >
                <CardHeader
                  className={`flex-shrink-0 border-b ${
                    theme === 'dark' ? 'border-zinc-700' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <CardTitle
                      className={`flex items-center gap-2 font-heading ${
                        theme === 'dark' ? 'text-white' : 'text-black'
                      }`}
                    >
                      <FolderTree className="h-5 w-5" />
                      Files
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      {needsScanning && (
                        <Button
                          onClick={handleScanRepository}
                          size="sm"
                          variant="outline"
                          className={`${
                            theme === 'dark'
                              ? 'bg-zinc-800 border-zinc-600 text-zinc-200 hover:bg-zinc-700'
                              : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          <Search className="h-4 w-4 mr-1" />
                          Scan
                        </Button>
                      )}
                      <Button
                        onClick={handleRefresh}
                        size="sm"
                        variant="ghost"
                        className={`${
                          theme === 'dark'
                            ? 'text-zinc-400 hover:text-zinc-200 hover:bg-zinc-800'
                            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="p-0 flex-1 min-h-0 overflow-hidden">
                  <div className="h-full overflow-y-auto">
                    <FileTree
                      nodes={fileTree}
                      selectedFileId={selectedFileId}
                      onFileSelect={handleFileSelect}
                      onDirectoryToggle={handleDirectoryToggle}
                      loading={fileTreeLoading}
                      theme={theme}
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Resize Handle */}
            <div
              ref={resizeRef}
              className={`w-1 cursor-col-resize flex-shrink-0 ${
                theme === 'dark'
                  ? 'bg-zinc-700 hover:bg-zinc-600'
                  : 'bg-gray-200 hover:bg-gray-300'
              } ${isResizing ? 'bg-blue-400' : ''}`}
              onMouseDown={handleMouseDown}
            />

            {/* Right Panel - Code Editor */}
            <div className="flex flex-col min-h-0 flex-1">
              <Card
                className={`flex-1 flex flex-col min-h-0 border-0 rounded-none ${
                  theme === 'dark' ? 'bg-zinc-900' : 'bg-white'
                }`}
              >
                <CardContent className="p-0 h-full flex-1 min-h-0">
                  <CodeEditor
                    fileContent={fileContent}
                    loading={fileContentLoading}
                    readOnly={true}
                    theme={theme}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </ThemeContext.Provider>
  );
};
