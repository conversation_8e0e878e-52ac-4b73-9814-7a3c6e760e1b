import React, { useState, useEffect } from 'react';
import { RotateCcw, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/Button';
import { gitApi, ollamaApi } from '@/services/api';
import { CommitFilesContent } from '@/types/commit';
import { useOllamaStore } from '@/stores/ollamaStore';
import { OLLAMA_CONFIG } from '@/constants';
import Stream from 'stream';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import 'highlight.js/styles/github.css';

interface CommitSummaryProps {
  repositoryId: string;
  commitHash: string;
}



interface SummaryState {
  loading: boolean;
  error: string | null;
  summary: string | null;
  progress: string | null;
}

const CHUNK_SIZE_LIMIT = OLLAMA_CONFIG.CHUNK_SIZE_LIMIT;
const MAX_FILE_SIZE = OLLAMA_CONFIG.MAX_FILE_SIZE;

export const CommitSummary: React.FC<CommitSummaryProps> = ({
  repositoryId,
  commitHash,
}) => {
  const { getSelectedModel } = useOllamaStore();

  const [summaryState, setSummaryState] = useState<SummaryState>({
    loading: false,
    error: null,
    summary: null,
    progress: null,
  });

  const generateSummary = async () => {
    setSummaryState({
      loading: true,
      error: null,
      summary: null,
      progress: 'Fetching commit files...',
    });

    try {
      // 1. 커밋의 모든 파일 내용 가져오기
      setSummaryState(prev => ({
        ...prev,
        progress: 'Loading commit files...',
      }));
      const response = await gitApi.getCommitFilesContent(
        repositoryId,
        commitHash
      );

      console.log('--- API Response from getCommitFilesContent ---', response);

      const filesContent = response.data;

      console.log('--- Extracted filesContent (response.data) ---', filesContent);

      if (!filesContent) {
        throw new Error('Failed to get commit files content (response.data is empty)');
      }

      if (!filesContent.files || filesContent.files.length === 0) {
        throw new Error('No files found in this commit');
      }

      // 2. 텍스트 크기 계산 및 분할
      setSummaryState(prev => ({ ...prev, progress: 'Preparing content for analysis...' }));
      const chunks = prepareContentChunks(filesContent);

      // 3. 각 청크에 대해 LLM 분석 수행
      const summaries: string[] = [];

      for (let i = 0; i < chunks.length; i++) {
        setSummaryState(prev => ({
          ...prev,
          progress: `Analyzing chunk ${i + 1} of ${chunks.length}...`
        }));

        const chunkSummary = await analyzeChunk(chunks[i], i + 1, chunks.length);
        // const chunkSummary = 'chunkSummary'
        summaries.push(chunkSummary);
      }

      // 4. 여러 청크가 있는 경우 최종 요약 생성
      let finalSummary: string;
      if (summaries.length > 1) {
        setSummaryState(prev => ({ ...prev, progress: 'Creating final summary...' }));
        finalSummary = await createFinalSummary(summaries);
      } else {
        finalSummary = summaries[0];
      }

      setSummaryState({
        loading: false,
        error: null,
        summary: finalSummary,
        progress: null
      });
    } catch (error) {
      console.error('Failed to generate summary:', error);
      setSummaryState({
        loading: false,
        error:
          error instanceof Error ? error.message : 'Failed to generate summary',
        summary: null,
        progress: null,
      });
    }
  };

  const prepareContentChunks = (filesContent: CommitFilesContent): string[] => {
    const chunks: string[] = [];
    let currentChunk = '';
    let currentSize = 0;

    // 기본 프롬프트 템플릿
    const basePrompt = `Please analyze this git commit and provide a comprehensive summary. Focus on:

1. **Main Purpose**: What is the primary goal of this commit?
2. **Key Changes**: What are the most important changes made?
3. **Files Modified**: Which files were changed and why?
4. **Impact**: What is the potential impact of these changes?
5. **Technical Details**: Any important technical aspects to note?

Commit Hash: ${filesContent.commit}
Total Files: ${filesContent.total_files}

Files and Changes:
`;

    const basePromptSize = basePrompt.length;

    // 파일들을 크기순으로 정렬 (작은 파일부터 처리)
    const sortedFiles = [...filesContent.files].sort((a, b) => a.size - b.size);

    for (const file of sortedFiles) {
      // 파일이 너무 큰 경우 잘라내기
      let fileContent = file.content;
      if (fileContent.length > MAX_FILE_SIZE) {
        const truncateMessage = `\n\n[... File truncated due to size limit. Original size: ${fileContent.length} characters ...]`;
        fileContent =
          fileContent.substring(0, MAX_FILE_SIZE - truncateMessage.length) +
          truncateMessage;
      }

      const fileSection = `
--- File: ${file.path} (${file.status}) ---
${fileContent}

`;

      // 현재 청크에 이 파일을 추가했을 때 크기 확인
      if (
        currentSize + fileSection.length + basePromptSize > CHUNK_SIZE_LIMIT &&
        currentChunk
      ) {
        // 현재 청크를 완성하고 새 청크 시작
        chunks.push(basePrompt + currentChunk);
        currentChunk = fileSection;
        currentSize = fileSection.length;
      } else {
        currentChunk += fileSection;
        currentSize += fileSection.length;
      }

      // 단일 파일이 청크 크기를 초과하는 경우 강제로 청크 생성
      if (currentSize + basePromptSize > CHUNK_SIZE_LIMIT) {
        chunks.push(basePrompt + currentChunk);
        currentChunk = '';
        currentSize = 0;
      }
    }

    // 마지막 청크 추가
    if (currentChunk) {
      chunks.push(basePrompt + currentChunk);
    }

    // 빈 청크가 생성된 경우 기본 메시지 추가
    if (chunks.length === 0) {
      chunks.push(basePrompt + '\n[No file content available for analysis]');
    }

    return chunks;
  };

  const analyzeChunk = async (
    chunk: string,
    chunkIndex: number,
    totalChunks: number
  ): Promise<string> => {
    // const analyzePrompt = `Analyze the following code changes from a git commit. This is part ${chunkIndex} of ${totalChunks}. Provide a concise summary of the changes in this part.\n\n${chunk}`;
    const analyzePrompt = `
당신은 20년 이상의 경력을 가진 전문 소프트웨어 엔지니어입니다.

아래는 git commit에서 검색된 파일 변경 내역입니다.
이 내용은 전체 중 ${totalChunks}개로 분할된 데이터 중 ${chunkIndex}번째 일부입니다.

아래 변경 내역을 보고 다음 항목을 반드시 모두 **한국어로만** 간결하게 요약하세요:

## 작업 내용 요약
1. **목적**: 이번 변경의 핵심 목적과 이유
2. **주요 변경사항**: 구체적으로 무엇이 어떻게 변경되었는지
3. **변경된 파일들**: 파일명과 각 파일의 주요 수정 내용

**중요**: 코드 블록은 포함하지 말고, 텍스트 설명만 제공하세요. 마크다운 형식으로 작성하되 간결하게 요약하세요.

변경 내역:
${chunk}
`;

console.log('--- analyzeChunk Prompt ---', analyzePrompt);

    try {
      const selectedModel = getSelectedModel();
      console.log(`Using model: ${selectedModel} for chunk ${chunkIndex}`);

      console.log('--- Sending data to Ollama (analyzeChunk) ---', {
        model: selectedModel,
        prompt: analyzePrompt,
        stream: false,
        options: {
          temperature: 0.1,
          top_p: 0.8,
        },
      });

      // const response = 'ollamaApi.generateText'
        

      const response = await ollamaApi.generateText(
        selectedModel,
        analyzePrompt,
        {
          temperature: 0.1,
          top_p: 0.8,
        }
      );

      console.log(`Full response object for chunk ${chunkIndex}:`, response);
      console.log(`Response type:`, typeof response);
      console.log(`Response.response:`, response?.response);

      if (!response?.response) {
        console.error(`No response.response found for chunk ${chunkIndex}. Full response:`, response);
        throw new Error(
          `No response received from LLM for chunk ${chunkIndex}`
        );
      }

      console.log(`Response for Ollama response :`, response.response);

      return response.response;
    } catch (error) {
      console.error(`Error analyzing chunk ${chunkIndex}:`, error);

      // 모델 관련 에러인지 확인
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      if (
        errorMessage.includes('model') ||
        errorMessage.includes('not found')
      ) {
        throw new Error(
          `Model "${getSelectedModel()}" not found. Please check if the model is installed in Ollama or select a different model in Settings.`
        );
      }

      throw new Error(`Failed to analyze chunk ${chunkIndex}: ${errorMessage}`);
    }
  };

  const createFinalSummary = async (summaries: string[]): Promise<string> => {
    const combinedSummaries = summaries
      .map((summary, index) => `## Part ${index + 1} Analysis:\n${summary}`)
      .join('\n\n');

    const finalPrompt = `
당신은 전문 소프트웨어 엔지니어입니다. 아래는 git commit의 부분별 분석 결과들입니다.

${combinedSummaries}

위의 부분별 분석들을 종합하여 **한국어로만** 최종 요약을 작성하세요:

## 커밋 종합 요약

### 1. 전체 목적
- 이번 커밋의 핵심 목적과 배경

### 2. 주요 변경사항
- 가장 중요한 변경사항들을 우선순위별로 정리

### 3. 기술적 영향
- 시스템에 미치는 기술적 영향과 고려사항

### 4. 변경된 파일 요약
- 수정된 파일들과 각각의 역할

**중요**: 마크다운 형식으로 작성하되, 코드 블록은 포함하지 말고 간결하게 요약하세요.
`;

    try {
      const selectedModel = getSelectedModel();
      console.log(`Using model: ${selectedModel} for final summary`);

      console.log('--- Sending data to Ollama (createFinalSummary) ---', {
        model: selectedModel,
        prompt: finalPrompt,
        options: {
          temperature: 0.2,
          top_p: 0.8,
        },
      });

      const response = await ollamaApi.generateText(
        selectedModel,
        finalPrompt,
        {
          temperature: 0.2,
          top_p: 0.8,
        }
      );

      if (!response?.response) {
        throw new Error('No response received from LLM for final summary');
      }

      return response.response;
    } catch (error) {
      console.error('Error creating final summary:', error);

      // 모델 관련 에러인지 확인
      const errorMessage =
        error instanceof Error ? error.message : 'Unknown error';
      if (
        errorMessage.includes('model') ||
        errorMessage.includes('not found')
      ) {
        throw new Error(
          `Model "${getSelectedModel()}" not found. Please check if the model is installed in Ollama or select a different model in Settings.`
        );
      }

      throw new Error(`Failed to create final summary: ${errorMessage}`);
    }
  };


  const analyzeSimple = async (): Promise<string> => {
  try {
    const selectedModel = getSelectedModel();
    console.log(`Using model: ${selectedModel} for simple analysis`);

    // 아주 간단한 프롬프트
    const simplePrompt = '리액트로 간단한 코드를 만들어라';

    console.log('--- Sending simple prompt to Ollama ---', {
      model: selectedModel,
      prompt: simplePrompt,
      stream: false,
      options: {
        temperature: 0.1,
        top_p: 0.8,
      },
    });

    const response = await ollamaApi.generateText(
      selectedModel,
      simplePrompt,
      {
        temperature: 0.1,
        top_p: 0.8,
      }
    );

    console.log('Full simple response object:', response);
    console.log('Simple response type:', typeof response);
    console.log('Simple response.response:', response?.response);

    if (!response?.response) {
      console.error('No response.response found for simple analysis. Full response:', response);
      throw new Error('No response received from LLM for simple analysis');
    }

    console.log('Simple analysis response:', response.response);

    return response.response;
  } catch (error) {
    console.error('Error in simple analysis:', error);

    // 모델 관련 에러인지 확인
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    if (errorMessage.includes('model') || errorMessage.includes('not found')) {
      throw new Error(
        `Model "${getSelectedModel()}" not found. Please check if the model is installed in Ollama or select a different model in Settings.`
      );
    }

    throw new Error(`Failed to perform simple analysis: ${errorMessage}`);
  }
};

// generateSummary 함수에서 사용할 때는 이렇게 변경하세요:
const generateSummarySimple = async () => {
  setSummaryState({
    loading: true,
    error: null,
    summary: null,
    progress: 'Generating simple summary...',
  });

  try {
    // 간단한 분석 실행
    const simpleSummary = await analyzeSimple();

    setSummaryState({
      loading: false,
      error: null,
      summary: simpleSummary,
      progress: null
    });
  } catch (error) {
    console.error('Failed to generate simple summary:', error);
    setSummaryState({
      loading: false,
      error: error instanceof Error ? error.message : 'Failed to generate simple summary',
      summary: null,
      progress: null,
    });
  }
};


  // 컴포넌트가 마운트되거나 commitHash가 변경될 때 자동으로 요약 생성
  // useEffect(() => {
  //   if (repositoryId && commitHash) {
  //     generateSummary();
  //   }
  // }, [repositoryId, commitHash]);

  if (summaryState.loading) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <RotateCcw className="h-8 w-8 animate-spin text-blue-500" />
        <div className="text-center">
          <p className="text-sm font-body text-black">
            Generating AI Summary...
          </p>
          {summaryState.progress && (
            <p className="text-xs text-gray-500 mt-1">
              {summaryState.progress}
            </p>
          )}
        </div>
      </div>
    );
  }

  if (summaryState.error) {
    return (
      <div className="flex flex-col items-center justify-center py-8 space-y-4">
        <AlertCircle className="h-8 w-8 text-red-500" />
        <div className="text-center">
          <p className="text-sm font-body text-red-600 mb-2">
            Failed to generate summary
          </p>
          <p className="text-xs text-gray-500 mb-4">{summaryState.error}</p>
          <Button
            onClick={generateSummary}
            size="sm"
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  if (!summaryState.summary) {
    return (
      <div className="flex flex-col items-center justify-center py-8">
        <p className="text-sm font-body text-gray-500">No summary available</p>
        <div className="flex gap-2 mt-4">
          <Button
            onClick={generateSummary}
            size="sm"
            variant="outline"
          >
            Generate Summary
          </Button>
          <Button
            onClick={async () => {
              try {
                const result = await analyzeSimple();
                console.log('Simple test result:', result);
                alert('Simple test successful! Check console for details.');
              } catch (error) {
                console.error('Simple test failed:', error);
                alert('Simple test failed! Check console for details.');
              }
            }}
            size="sm"
            variant="secondary"
          >
            Test Simple
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium font-heading text-black">
          AI-Generated Summary
        </h4>
        <Button
          onClick={generateSummary}
          size="sm"
          variant="outline"
          className="flex items-center gap-2"
        >
          <RefreshCw className="h-4 w-4" />
          Regenerate
        </Button>
      </div>

      <div className="prose prose-sm max-w-none">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight]}
          components={{
            h1: ({ children, ...props }) => (
              <h1
                className="text-lg font-bold text-black mb-3 border-b border-gray-200 pb-2"
                {...props}
              >
                {children}
              </h1>
            ),
            h2: ({ children, ...props }) => (
              <h2
                className="text-base font-semibold text-black mb-2 mt-4"
                {...props}
              >
                {children}
              </h2>
            ),
            h3: ({ children, ...props }) => (
              <h3
                className="text-sm font-medium text-black mb-2 mt-3"
                {...props}
              >
                {children}
              </h3>
            ),
            p: ({ children, ...props }) => (
              <p
                className="text-sm font-body text-gray-800 leading-relaxed mb-3"
                {...props}
              >
                {children}
              </p>
            ),
            ul: ({ children, ...props }) => (
              <ul
                className="list-disc list-inside text-sm font-body text-gray-800 mb-3 space-y-1"
                {...props}
              >
                {children}
              </ul>
            ),
            ol: ({ children, ...props }) => (
              <ol
                className="list-decimal list-inside text-sm font-body text-gray-800 mb-3 space-y-1"
                {...props}
              >
                {children}
              </ol>
            ),
            li: ({ children, ...props }) => (
              <li className="text-sm font-body text-gray-800" {...props}>
                {children}
              </li>
            ),
            code: ({ children, ...props }: any) => {
              const isInline = !props.className?.includes('language-');
              return isInline ? (
                <code
                  className="bg-gray-100 px-1 py-0.5 rounded text-xs font-mono text-gray-900"
                  {...props}
                >
                  {children}
                </code>
              ) : (
                <code
                  className="block bg-gray-50 p-3 rounded-md text-xs font-mono text-gray-900 overflow-x-auto"
                  {...props}
                >
                  {children}
                </code>
              );
            },
            pre: ({ children, ...props }) => (
              <pre
                className="bg-gray-50 p-3 rounded-md overflow-x-auto mb-3"
                {...props}
              >
                {children}
              </pre>
            ),
            blockquote: ({ children, ...props }) => (
              <blockquote
                className="border-l-4 border-gray-300 pl-4 italic text-gray-700 mb-3"
                {...props}
              >
                {children}
              </blockquote>
            ),
            strong: ({ children, ...props }) => (
              <strong className="font-semibold text-black" {...props}>
                {children}
              </strong>
            ),
          }}
        >
          {summaryState.summary}
        </ReactMarkdown>
      </div>
    </div>
  );
};
