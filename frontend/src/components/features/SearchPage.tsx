import React, { useState } from 'react';
import { Search, Filter, FileText, Code, Database } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { useRepositoryStore } from '@/stores/repositoryStore';

export const SearchPage: React.FC = () => {
  // Global repository state
  const { context } = useRepositoryStore();

  const [searchQuery, setSearchQuery] = useState('');
  const [searchMode, setSearchMode] = useState('auto');

  const handleSearch = () => {
    // TODO: Implement search functionality with repository context
    console.log(
      'Searching for:',
      searchQuery,
      'with mode:',
      searchMode,
      'in repository:',
      context
    );
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Code Search</h1>
        <p className="text-muted-foreground">
          Search your codebase with AI-powered semantic search
          {context.repository_name && (
            <span className="ml-2 text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded">
              Repository: {context.repository_name}
            </span>
          )}
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search Query</CardTitle>
          <CardDescription>
            Enter your search query using natural language or specific terms
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="e.g., authentication function, user login logic, database connection..."
              value={searchQuery}
              onChange={e => setSearchQuery(e.target.value)}
              className="flex-1"
              onKeyPress={e => e.key === 'Enter' && handleSearch()}
            />
            <Button onClick={handleSearch}>
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </div>

          <div className="flex space-x-2">
            <Button
              variant={searchMode === 'auto' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchMode('auto')}
            >
              Auto
            </Button>
            <Button
              variant={searchMode === 'semantic' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchMode('semantic')}
            >
              Semantic
            </Button>
            <Button
              variant={searchMode === 'structural' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchMode('structural')}
            >
              Structural
            </Button>
            <Button
              variant={searchMode === 'temporal' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSearchMode('temporal')}
            >
              Temporal
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Code className="mr-2 h-4 w-4" />
              Functions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">1,234</div>
            <p className="text-xs text-muted-foreground">Available functions</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="mr-2 h-4 w-4" />
              Classes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">567</div>
            <p className="text-xs text-muted-foreground">Available classes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="mr-2 h-4 w-4" />
              Files
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">Source files</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search Results</CardTitle>
          <CardDescription>
            Results will appear here when you perform a search
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Search className="mx-auto h-12 w-12 mb-4" />
            <p>Enter a search query to see results</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Search Tips</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p>
              <strong>Semantic Search:</strong> Use natural language like
              "function that handles user authentication"
            </p>
            <p>
              <strong>Structural Search:</strong> Search by code structure like
              "class that inherits from BaseModel"
            </p>
            <p>
              <strong>Temporal Search:</strong> Find recent changes like
              "recently modified login code"
            </p>
            <p>
              <strong>Auto Mode:</strong> Let AI choose the best search method
              for your query
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
