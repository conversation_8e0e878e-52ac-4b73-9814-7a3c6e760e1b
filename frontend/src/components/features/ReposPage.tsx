import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { Badge } from '@/components/ui/badge';
import {
  GitBranch,
  Play,
  Database,
  Loader2,
  CheckCircle,
  XCircle,
  Clock,
} from 'lucide-react';
import {
  useRepositories,
  useRepositoryData,
  useRepositoryContext,
} from '@/hooks/useRepositories';
import { useRepositoryStore } from '@/stores/repositoryStore';

import { repositoryApi, codebaseApi, lspService } from '@/services/api';
import { Repository, RepositoryBranchAnalysis } from '@/types';

interface ScanResponse {
  success: boolean;
  message?: string;
  data?: {
    aiend_task?: {
      processed_files: number;
    };
  };
}

interface AnalysisTask {
  analysis_id: string;
  repository_id: number;
  repository_name: string;
  branch: string;
  status: 'idle' | 'analyzing' | 'completed' | 'error';
  message?: string;
  started_at?: string;
}

export function ReposPage() {
  const { repositories, loading, error, refetch } = useRepositories();
  const {
    branchAnalyses,
    generateAnalysisId,
    generateCollectionName,
    addBranchAnalysis,
    updateBranchAnalysis,
  } = useRepositoryStore();

  const [selectedRepo, setSelectedRepo] = useState<Repository | null>(null);
  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [analysisTasks, setAnalysisTasks] = useState<AnalysisTask[]>([]);

  const handleRepositorySelect = (repoId: string) => {
    const repo = repositories.find(r => r.id.toString() === repoId);
    if (repo) {
      setSelectedRepo(repo);
      setSelectedBranch(repo.current_branch || '');
    }
  };

  const handleBranchSelect = (branch: string) => {
    setSelectedBranch(branch);
  };

  const handleAnalyze = async () => {
    console.log('[ReposPages] handleAnalyze 함수 시작');
    if (!selectedRepo || !selectedBranch) {
      console.log('[ReposPages] 선택된 저장소 또는 브랜치가 없음');
      return;
    }
    console.log('[ReposPages] 선택된 저장소:', selectedRepo);
    console.log('[ReposPages] 선택된 브랜치:', selectedBranch);

    const analysisId = generateAnalysisId(selectedRepo.id, selectedBranch);
    const collectionName = generateCollectionName(
      selectedRepo.id,
      selectedBranch
    );
    console.log('[ReposPages] 생성된 분석 ID:', analysisId);
    console.log('[ReposPages] 생성된 컬렉션 이름:', collectionName);

    // Create analysis task
    console.log('[ReposPages] 분석 작업 생성 시작');
    const newTask: AnalysisTask = {
      analysis_id: analysisId,
      repository_id: selectedRepo.id,
      repository_name: selectedRepo.name,
      branch: selectedBranch,
      status: 'analyzing',
      message: 'Starting analysis...',
      started_at: new Date().toISOString(),
    };
    console.log('[ReposPages] 생성된 분석 작업:', newTask);

    setAnalysisTasks(prev => {
      const updatedTasks = [
        ...prev.filter(t => t.analysis_id !== analysisId),
        newTask,
      ];
      console.log('[ReposPages] 업데이트된 분석 작업 목록:', updatedTasks);
      return updatedTasks;
    });

    // Create or update branch analysis record
    console.log('[ReposPages] 브랜치 분석 기록 생성 시작');
    const branchAnalysis: RepositoryBranchAnalysis = {
      id: analysisId,
      repository_id: selectedRepo.id,
      repository_name: selectedRepo.name,
      branch: selectedBranch,
      status: 'analyzing',
      collection_name: collectionName,
      last_analyzed_at: new Date().toISOString(),
    };
    console.log('[ReposPages] 생성된 브랜치 분석 기록:', branchAnalysis);

    addBranchAnalysis(branchAnalysis);
    console.log('[ReposPages] 브랜치 분석 기록 추가 완료');

    try {
      console.log('[ReposPages] 분석 시작 - 리포지토리 스캔과 LSP 분석');

      // 1. 리포지토리 스캔과 LSP 분석을 동시에 실행
      const [repoScanResult, lspResult] = await Promise.allSettled([
        // 리포지토리 스캔 (파일 시스템 스캔)
        (async () => {
          console.log('[ReposPages] 리포지토리 스캔 시작');
          const scanResponse = await repositoryApi.scanRepository(
            selectedRepo.id.toString()
          );
          console.log('[ReposPages] 리포지토리 스캔 응답:', scanResponse);
          return {
            type: 'repository',
            status: 'completed',
            data: scanResponse,
          };
        })(),

        // LSP 분석
        (async () => {
          console.log('[ReposPages] LSP 분석 시작');
          const lspResponse = await lspService.scanRepositoryByNameAndId(
            selectedRepo.name,
            selectedRepo.id,
            true // recursive
          );
          console.log('[ReposPages] LSP 분석 응답:', lspResponse);

          if (lspResponse.status === 'ok') {
            console.log('[ReposPages] LSP 분석 완료');
            return { type: 'lsp', status: 'completed', data: lspResponse };
          } else {
            throw new Error(lspResponse.message || 'LSP analysis failed');
          }
        })(),
      ]);

      // 2. 결과 처리
      console.log('[ReposPages] 동시 분석 결과:', {
        repoScanResult,
        lspResult,
      });

      let finalMessage = 'Analysis completed: ';
      let hasError = false;

      // 파일 스캔 결과 처리
      if (repoScanResult.status === 'fulfilled') {
        console.log('[ReposPages] 파일 스캔 성공:', repoScanResult.value);
        finalMessage += 'File scan ✓ ';
      } else {
        console.error('[ReposPages] 파일 스캔 실패:', repoScanResult.reason);
        finalMessage += 'File scan ✗ ';
        hasError = true;
      }

      // LSP 결과 처리
      if (lspResult.status === 'fulfilled') {
        console.log('[ReposPages] LSP 분석 성공:', lspResult.value);
        const lspData = lspResult.value.data;
        finalMessage += `LSP analysis ✓ (${lspData.symbolsFound || 0} symbols, ${lspData.referencesFound || 0} references)`;
      } else {
        console.error('[ReposPages] LSP 분석 실패:', lspResult.reason);
        finalMessage += 'LSP analysis ✗';
        hasError = true;
      }

      // 3. 최종 상태 업데이트
      setAnalysisTasks(prev =>
        prev.map(t =>
          t.analysis_id === analysisId
            ? {
                ...t,
                status: hasError ? 'error' : 'completed',
                message: finalMessage,
              }
            : t
        )
      );

      updateBranchAnalysis({
        ...branchAnalysis,
        status: hasError ? 'error' : 'completed',
        total_files:
          lspResult.status === 'fulfilled'
            ? lspResult.value.data.symbolsFound || 0
            : repoScanResult.status === 'fulfilled'
              ? repoScanResult.value.data.files_count || 0
              : undefined,
      });

      if (hasError) {
        throw new Error('One or more analysis tasks failed');
      }
    } catch (error) {
      console.error('[ReposPages] 동시 분석 실패:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Analysis failed';
      console.log('[ReposPages] 오류 메시지:', errorMessage);

      // Update task status
      setAnalysisTasks(prev => {
        const updatedTasks = prev.map(t =>
          t.analysis_id === analysisId
            ? { ...t, status: 'error' as const, message: errorMessage }
            : t
        );
        console.log(
          '[ReposPages] 분석 작업 상태 업데이트 (오류):',
          updatedTasks
        );
        return updatedTasks;
      });

      // Update branch analysis
      const updatedBranchAnalysis = {
        ...branchAnalysis,
        status: 'error' as const,
        error_message: errorMessage,
      };
      console.log(
        '[ReposPages] 브랜치 분석 상태 업데이트 (오류):',
        updatedBranchAnalysis
      );
      updateBranchAnalysis(updatedBranchAnalysis);
    }
  };

  const getTaskStatus = (repoId: number, branch: string) => {
    return analysisTasks.find(
      t => t.repository_id === repoId && t.branch === branch
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading repositories...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              Error loading repositories: {error}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Repository Analysis</h1>
          <p className="text-muted-foreground">
            Select a repository and branch to analyze for code intelligence
          </p>
        </div>
        <Button onClick={() => refetch()} variant="outline">
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GitBranch className="h-5 w-5" />
              Repository & Branch Selection
            </CardTitle>
            <CardDescription>
              Choose a repository and branch to analyze
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium mb-2 block">
                Repository
              </label>
              <Select onValueChange={handleRepositorySelect}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a repository" />
                </SelectTrigger>
                <SelectContent>
                  {repositories.map(repo => (
                    <SelectItem key={repo.id} value={repo.id.toString()}>
                      <div className="flex items-center gap-2">
                        <span>{repo.name}</span>
                        <Badge variant="secondary" className="text-xs">
                          {repo.status}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedRepo && (
              <div>
                <label className="text-sm font-medium mb-2 block">Branch</label>
                <Select
                  value={selectedBranch}
                  onValueChange={handleBranchSelect}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedRepo.branches?.map(branch => (
                      <SelectItem key={branch} value={branch}>
                        <div className="flex items-center gap-2">
                          <span>{branch}</span>
                          {branch === selectedRepo.current_branch && (
                            <Badge variant="default" className="text-xs">
                              current
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedRepo && selectedBranch && (
              <div className="pt-4">
                <Button
                  onClick={handleAnalyze}
                  className="w-full"
                  disabled={
                    getTaskStatus(selectedRepo.id, selectedBranch)?.status ===
                    'analyzing'
                  }
                >
                  {getTaskStatus(selectedRepo.id, selectedBranch)?.status ===
                  'analyzing' ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Play className="mr-2 h-4 w-4" />
                      Analyze Repository
                    </>
                  )}
                </Button>
                <p className="text-xs text-muted-foreground mt-2 text-center">
                  Collection: repo_{selectedRepo.id}_branch_
                  {selectedBranch.replace(/[^a-zA-Z0-9]/g, '_')}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Analysis Status
            </CardTitle>
            <CardDescription>Current and recent analysis tasks</CardDescription>
          </CardHeader>
          <CardContent>
            {analysisTasks.length === 0 && branchAnalyses.length === 0 ? (
              <div className="text-center text-muted-foreground py-8">
                No analysis tasks yet
              </div>
            ) : (
              <div className="space-y-3">
                {/* Recent analysis tasks */}
                {analysisTasks
                  .slice(-3)
                  .reverse()
                  .map((task, index) => (
                    <div
                      key={`task-${index}`}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div>
                        <div className="font-medium">
                          {task.repository_name} / {task.branch}
                        </div>
                        {task.message && (
                          <div className="text-sm text-muted-foreground">
                            {task.message}
                          </div>
                        )}
                        {task.started_at && (
                          <div className="text-xs text-muted-foreground">
                            Started:{' '}
                            {new Date(task.started_at).toLocaleTimeString()}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {task.status === 'analyzing' && (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        )}
                        {task.status === 'completed' && (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        )}
                        {task.status === 'error' && (
                          <XCircle className="h-4 w-4 text-red-600" />
                        )}
                        <Badge
                          variant={
                            task.status === 'completed'
                              ? 'default'
                              : task.status === 'analyzing'
                                ? 'secondary'
                                : task.status === 'error'
                                  ? 'destructive'
                                  : 'outline'
                          }
                        >
                          {task.status}
                        </Badge>
                      </div>
                    </div>
                  ))}

                {/* Existing branch analyses */}
                {branchAnalyses.slice(-2).map(analysis => (
                  <div
                    key={analysis.id}
                    className="flex items-center justify-between p-3 border rounded-lg bg-muted/50"
                  >
                    <div>
                      <div className="font-medium">
                        {analysis.repository_name} / {analysis.branch}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        Collection: {analysis.collection_name}
                      </div>
                      {analysis.last_analyzed_at && (
                        <div className="text-xs text-muted-foreground">
                          Last analyzed:{' '}
                          {new Date(analysis.last_analyzed_at).toLocaleString()}
                        </div>
                      )}
                      {analysis.total_files && (
                        <div className="text-xs text-muted-foreground">
                          Files: {analysis.total_files}
                        </div>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      {analysis.status === 'completed' && (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      )}
                      {analysis.status === 'error' && (
                        <XCircle className="h-4 w-4 text-red-600" />
                      )}
                      {analysis.status === 'analyzing' && (
                        <Clock className="h-4 w-4 text-blue-600" />
                      )}
                      <Badge
                        variant={
                          analysis.status === 'completed'
                            ? 'default'
                            : analysis.status === 'analyzing'
                              ? 'secondary'
                              : analysis.status === 'error'
                                ? 'destructive'
                                : 'outline'
                        }
                      >
                        {analysis.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Repositories</CardTitle>
          <CardDescription>
            Overview of all registered repositories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {repositories.map(repo => (
              <div key={repo.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{repo.name}</h3>
                  <Badge variant="secondary">{repo.status}</Badge>
                </div>
                <div className="text-sm text-muted-foreground space-y-1">
                  <div>Current: {repo.current_branch}</div>
                  <div>Branches: {repo.branches?.length || 0}</div>
                  {repo.last_indexed_at && (
                    <div>
                      Last indexed:{' '}
                      {new Date(repo.last_indexed_at).toLocaleDateString()}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
