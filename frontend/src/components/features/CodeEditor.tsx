import React, { useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import { FileContent } from '@/types';
import { FileText, Code } from 'lucide-react';
import 'highlight.js/styles/github.css';

// Theme types
type Theme = 'light' | 'dark';

interface CodeEditorProps {
  fileContent?: FileContent;
  loading?: boolean;
  readOnly?: boolean;
  onChange?: (value: string) => void;
  theme?: Theme;
}

const getLanguageFromExtension = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const languageMap: { [key: string]: string } = {
    js: 'javascript',
    jsx: 'javascript',
    ts: 'typescript',
    tsx: 'typescript',
    py: 'python',
    rb: 'ruby',
    php: 'php',
    java: 'java',
    c: 'c',
    cpp: 'cpp',
    cc: 'cpp',
    cxx: 'cpp',
    h: 'c',
    hpp: 'cpp',
    cs: 'csharp',
    go: 'go',
    rs: 'rust',
    sh: 'shell',
    bash: 'shell',
    zsh: 'shell',
    fish: 'shell',
    ps1: 'powershell',
    html: 'html',
    htm: 'html',
    xml: 'xml',
    css: 'css',
    scss: 'scss',
    sass: 'sass',
    less: 'less',
    json: 'json',
    yaml: 'yaml',
    yml: 'yaml',
    toml: 'toml',
    ini: 'ini',
    cfg: 'ini',
    conf: 'ini',
    md: 'markdown',
    markdown: 'markdown',
    sql: 'sql',
    dockerfile: 'dockerfile',
    makefile: 'makefile',
    gitignore: 'gitignore',
    txt: 'plaintext',
  };

  return languageMap[extension || ''] || 'plaintext';
};

const isMarkdownFile = (fileName: string): boolean => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  return extension === 'md' || extension === 'markdown';
};

export const CodeEditor: React.FC<CodeEditorProps> = ({
  fileContent,
  loading,
  readOnly = true,
  onChange,
  theme,
}) => {
  const editorRef = useRef<any>(null);
  const currentTheme = theme || 'light'; // 안전장치

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;

    // Configure Monaco Editor themes
    monaco.editor.defineTheme('vs-code-light', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f5f5f5',
        'editor.selectionBackground': '#add6ff',
        'editorLineNumber.foreground': '#999999',
        'editorGutter.background': '#f8f8f8',
      },
    });

    monaco.editor.defineTheme('vs-code-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#0d1117',
        'editor.foreground': '#f0f6fc',
        'editor.lineHighlightBackground': '#161b22',
        'editor.selectionBackground': '#264f78',
        'editorLineNumber.foreground': '#7d8590',
        'editorGutter.background': '#0d1117',
      },
    });

    monaco.editor.setTheme(
      currentTheme === 'dark' ? 'vs-code-dark' : 'vs-code-light'
    );
  };

  const handleEditorChange = (value: string | undefined) => {
    if (onChange && value !== undefined) {
      onChange(value);
    }
  };

  // Update Monaco theme when theme changes
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any).monaco;
      if (monaco) {
        monaco.editor.setTheme(
          currentTheme === 'dark' ? 'vs-code-dark' : 'vs-code-light'
        );
      }
    }
  }, [currentTheme]);

  if (loading) {
    return (
      <div
        className={`flex items-center justify-center h-full ${
          currentTheme === 'dark' ? 'bg-black' : 'bg-gray-50'
        }`}
      >
        <div className="text-center">
          <Code
            className={`h-12 w-12 mx-auto mb-4 animate-pulse ${
              currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-400'
            }`}
          />
          <p
            className={`font-body ${
              currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-500'
            }`}
          >
            Loading file content...
          </p>
        </div>
      </div>
    );
  }

  if (!fileContent) {
    return (
      <div
        className={`flex items-center justify-center h-full ${
          currentTheme === 'dark' ? 'bg-black' : 'bg-gray-50'
        }`}
      >
        <div className="text-center">
          <FileText
            className={`h-12 w-12 mx-auto mb-4 ${
              currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-400'
            }`}
          />
          <p
            className={`font-body ${
              currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-500'
            }`}
          >
            Select a file to view its content
          </p>
        </div>
      </div>
    );
  }

  const language =
    fileContent.file.language ||
    getLanguageFromExtension(fileContent.file.name);

  return (
    <div className="h-full flex flex-col">
      {/* File Info Header */}
      <div
        className={`flex items-center justify-between px-4 py-2 border-b ${
          currentTheme === 'dark'
            ? 'bg-zinc-900 border-zinc-700'
            : 'bg-gray-100 border-gray-200'
        }`}
      >
        <div className="flex items-center gap-2">
          <FileText
            className={`h-4 w-4 ${currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-600'}`}
          />
          <span
            className={`font-medium font-heading ${currentTheme === 'dark' ? 'text-white' : 'text-black'}`}
          >
            {fileContent.file.name}
          </span>
          <span
            className={`text-xs ${currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}
          >
            ({fileContent.file.path})
          </span>
        </div>
        <div
          className={`flex items-center gap-4 text-xs ${currentTheme === 'dark' ? 'text-gray-200' : 'text-gray-500'}`}
        >
          <span>{fileContent.file.lines} lines</span>
          <span>{(fileContent.file.size / 1024).toFixed(1)} KB</span>
          <span className="capitalize">{language}</span>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1">
        {isMarkdownFile(fileContent.file.name) ? (
          /* Markdown Renderer */
          <div
            className={`h-full p-6 ${
              currentTheme === 'dark' ? 'bg-black' : 'bg-white'
            }`}
          >
            <div className="max-w-4xl mx-auto h-full overflow-y-auto">
              <div className="prose prose-lg max-w-none">
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeHighlight]}
                  components={{
                    h1: ({ children, ...props }) => (
                      <h1
                        style={{
                          fontSize: '1.875rem',
                          fontWeight: 'bold',
                          color: currentTheme === 'dark' ? '#ffffff' : 'black',
                          marginBottom: '1rem',
                          borderBottom: `1px solid ${currentTheme === 'dark' ? '#404040' : '#e5e7eb'}`,
                          paddingBottom: '0.5rem',
                        }}
                        {...props}
                      >
                        {children}
                      </h1>
                    ),
                    h2: ({ children, ...props }) => (
                      <h2
                        style={{
                          fontSize: '1.5rem',
                          fontWeight: '600',
                          color: currentTheme === 'dark' ? '#ffffff' : 'black',
                          marginBottom: '0.75rem',
                          marginTop: '1.5rem',
                        }}
                        {...props}
                      >
                        {children}
                      </h2>
                    ),
                    h3: ({ children, ...props }) => (
                      <h3
                        style={{
                          fontSize: '1.25rem',
                          fontWeight: '500',
                          color: currentTheme === 'dark' ? '#ffffff' : 'black',
                          marginBottom: '0.5rem',
                          marginTop: '1rem',
                        }}
                        {...props}
                      >
                        {children}
                      </h3>
                    ),
                    p: ({ children, ...props }) => (
                      <p
                        style={{
                          color:
                            currentTheme === 'dark' ? '#e6e6e6' : '#374151',
                          marginBottom: '1rem',
                          lineHeight: '1.625',
                        }}
                        {...props}
                      >
                        {children}
                      </p>
                    ),
                    code: ({ inline, children, ...props }) =>
                      inline ? (
                        <code
                          style={{
                            backgroundColor:
                              currentTheme === 'dark' ? '#2d2d2d' : '#f3f4f6',
                            padding: '0.125rem 0.25rem',
                            borderRadius: '0.25rem',
                            fontSize: '0.875rem',
                            fontFamily: 'monospace',
                            color:
                              currentTheme === 'dark' ? '#ffffff' : '#374151',
                          }}
                          {...props}
                        >
                          {children}
                        </code>
                      ) : (
                        <code
                          style={{
                            display: 'block',
                            backgroundColor:
                              currentTheme === 'dark' ? '#1a1a1a' : '#f9fafb',
                            padding: '1rem',
                            borderRadius: '0.5rem',
                            fontSize: '0.875rem',
                            fontFamily: 'monospace',
                            overflowX: 'auto',
                            color:
                              currentTheme === 'dark' ? '#ffffff' : '#374151',
                          }}
                          {...props}
                        >
                          {children}
                        </code>
                      ),
                    ul: ({ children, ...props }) => (
                      <ul
                        style={{
                          listStyleType: 'disc',
                          listStylePosition: 'inside',
                          marginBottom: '1rem',
                          color:
                            currentTheme === 'dark' ? '#e6e6e6' : '#374151',
                        }}
                        {...props}
                      >
                        {children}
                      </ul>
                    ),
                    ol: ({ children, ...props }) => (
                      <ol
                        style={{
                          listStyleType: 'decimal',
                          listStylePosition: 'inside',
                          marginBottom: '1rem',
                          color:
                            currentTheme === 'dark' ? '#e6e6e6' : '#374151',
                        }}
                        {...props}
                      >
                        {children}
                      </ol>
                    ),
                    blockquote: ({ children, ...props }) => (
                      <blockquote
                        style={{
                          borderLeft: '4px solid #3b82f6',
                          paddingLeft: '1rem',
                          fontStyle: 'italic',
                          color:
                            currentTheme === 'dark' ? '#cccccc' : '#4b5563',
                          marginBottom: '1rem',
                        }}
                        {...props}
                      >
                        {children}
                      </blockquote>
                    ),
                    table: ({ children, ...props }) => (
                      <div style={{ overflowX: 'auto', marginBottom: '1rem' }}>
                        <table
                          style={{
                            minWidth: '100%',
                            border: `1px solid ${currentTheme === 'dark' ? '#404040' : '#d1d5db'}`,
                          }}
                          {...props}
                        >
                          {children}
                        </table>
                      </div>
                    ),
                    th: ({ children, ...props }) => (
                      <th
                        style={{
                          border: `1px solid ${currentTheme === 'dark' ? '#404040' : '#d1d5db'}`,
                          padding: '0.5rem 1rem',
                          backgroundColor:
                            currentTheme === 'dark' ? '#2d2d2d' : '#f9fafb',
                          fontWeight: '600',
                          textAlign: 'left',
                          color:
                            currentTheme === 'dark' ? '#ffffff' : '#374151',
                        }}
                        {...props}
                      >
                        {children}
                      </th>
                    ),
                    td: ({ children, ...props }) => (
                      <td
                        style={{
                          border: `1px solid ${currentTheme === 'dark' ? '#404040' : '#d1d5db'}`,
                          padding: '0.5rem 1rem',
                          color:
                            currentTheme === 'dark' ? '#e6e6e6' : '#374151',
                        }}
                        {...props}
                      >
                        {children}
                      </td>
                    ),
                  }}
                >
                  {fileContent.content}
                </ReactMarkdown>
              </div>
            </div>
          </div>
        ) : (
          /* Monaco Editor */
          <Editor
            height="100%"
            language={language}
            value={fileContent.content}
            onMount={handleEditorDidMount}
            onChange={handleEditorChange}
            options={{
              readOnly,
              minimap: { enabled: true },
              fontSize: 14,
              lineNumbers: 'on',
              rulers: [80, 120],
              wordWrap: 'on',
              scrollBeyondLastLine: false,
              automaticLayout: true,
              tabSize: 2,
              insertSpaces: true,
              detectIndentation: true,
              folding: true,
              foldingStrategy: 'indentation',
              showFoldingControls: 'always',
              unfoldOnClickAfterEndOfLine: false,
              contextmenu: true,
              selectOnLineNumbers: true,
              lineDecorationsWidth: 10,
              lineNumbersMinChars: 3,
              glyphMargin: false,
              fixedOverflowWidgets: true,
              overviewRulerLanes: 3,
              overviewRulerBorder: false,
              cursorBlinking: 'blink',
              cursorSmoothCaretAnimation: 'on',
              cursorWidth: 2,
              mouseWheelZoom: true,
              quickSuggestions: {
                other: true,
                comments: false,
                strings: false,
              },
              parameterHints: {
                enabled: true,
              },
              autoIndent: 'full',
              formatOnType: true,
              formatOnPaste: true,
              dragAndDrop: true,
              links: true,
              colorDecorators: true,
              lightbulb: {
                enabled: 'on',
              },
              suggest: {
                showKeywords: true,
                showSnippets: true,
                showClasses: true,
                showFunctions: true,
                showVariables: true,
              },
            }}
          />
        )}
      </div>
    </div>
  );
};
