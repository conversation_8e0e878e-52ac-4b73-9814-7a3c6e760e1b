import React, { useState, useCallback } from 'react';
import {
  ChevronRight,
  ChevronDown,
  Folder,
  FolderOpen,
  File,
  FileText,
  FileCode,
  FileImage,
  FileVideo,
  FileArchive,
  Settings,
} from 'lucide-react';
import { cn } from '@/utils/cn';
import { FileTreeNode } from '@/types';

// Theme types
type Theme = 'light' | 'dark';

interface FileTreeProps {
  nodes: FileTreeNode[];
  selectedFileId?: string;
  onFileSelect: (node: FileTreeNode) => void;
  onDirectoryToggle?: (node: FileTreeNode) => void;
  loading?: boolean;
  theme?: Theme;
}

interface FileTreeItemProps {
  node: FileTreeNode;
  selectedFileId?: string;
  onFileSelect: (node: FileTreeNode) => void;
  onDirectoryToggle?: (node: FileTreeNode) => void;
  level: number;
  theme?: Theme;
}

const getFileIcon = (fileName: string, isDirectory: boolean) => {
  if (isDirectory) {
    return { icon: Folder, color: 'text-blue-500' };
  }

  const extension = fileName.split('.').pop()?.toLowerCase();

  switch (extension) {
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
    case 'vue':
    case 'svelte':
      return { icon: FileCode, color: 'text-yellow-500' };
    case 'py':
    case 'rb':
    case 'php':
    case 'java':
    case 'c':
    case 'cpp':
    case 'cs':
    case 'go':
    case 'rs':
      return { icon: FileCode, color: 'text-green-500' };
    case 'html':
    case 'htm':
    case 'xml':
      return { icon: FileCode, color: 'text-orange-500' };
    case 'css':
    case 'scss':
    case 'sass':
    case 'less':
      return { icon: FileCode, color: 'text-blue-400' };
    case 'json':
    case 'yaml':
    case 'yml':
    case 'toml':
    case 'ini':
    case 'cfg':
    case 'conf':
      return { icon: Settings, color: 'text-gray-500' };
    case 'md':
    case 'markdown':
    case 'txt':
    case 'readme':
      return { icon: FileText, color: 'text-gray-600' };
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
    case 'ico':
      return { icon: FileImage, color: 'text-purple-500' };
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'flv':
    case 'webm':
      return { icon: FileVideo, color: 'text-red-500' };
    case 'zip':
    case 'rar':
    case 'tar':
    case 'gz':
    case '7z':
      return { icon: FileArchive, color: 'text-amber-600' };
    default:
      return { icon: File, color: 'text-gray-500' };
  }
};

const FileTreeItem: React.FC<FileTreeItemProps> = ({
  node,
  selectedFileId,
  onFileSelect,
  onDirectoryToggle,
  level,
  theme,
}) => {
  const [isExpanded, setIsExpanded] = useState(node.expanded || false);
  const { icon: IconComponent, color } = getFileIcon(
    node.name,
    node.type === 'directory'
  );
  const currentTheme = theme || 'light'; // 안전장치

  const handleToggle = useCallback(() => {
    if (node.type === 'directory') {
      const newExpanded = !isExpanded;
      setIsExpanded(newExpanded);
      if (onDirectoryToggle) {
        onDirectoryToggle({ ...node, expanded: newExpanded });
      }
    }
  }, [node, isExpanded, onDirectoryToggle]);

  const handleSelect = useCallback(() => {
    if (node.type === 'file') {
      onFileSelect(node);
    } else {
      handleToggle();
    }
  }, [node, onFileSelect, handleToggle]);

  const isSelected =
    node.type === 'file' &&
    selectedFileId ===
      (node.id.startsWith('file_') ? node.id.substring(5) : node.id);
  const paddingLeft = level * 12 + 8;

  return (
    <div>
      <div
        className={cn(
          'flex items-center py-1 px-2 cursor-pointer text-sm',
          currentTheme === 'dark'
            ? 'hover:bg-zinc-700 text-white'
            : 'hover:bg-gray-100 text-black',
          isSelected &&
            (currentTheme === 'dark'
              ? 'bg-blue-700 text-white'
              : 'bg-blue-100 text-blue-900')
        )}
        style={{ paddingLeft: `${paddingLeft}px` }}
        onClick={handleSelect}
      >
        {node.type === 'directory' && (
          <div className="flex items-center justify-center w-4 h-4 mr-1">
            {node.children &&
              node.children.length > 0 &&
              (isExpanded ? (
                <ChevronDown
                  className={cn(
                    'h-3 w-3',
                    currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  )}
                />
              ) : (
                <ChevronRight
                  className={cn(
                    'h-3 w-3',
                    currentTheme === 'dark' ? 'text-gray-400' : 'text-gray-500'
                  )}
                />
              ))}
          </div>
        )}
        {node.type === 'file' && <div className="w-4 mr-1" />}

        <div className="flex items-center min-w-0 flex-1">
          {node.type === 'directory' ? (
            isExpanded ? (
              <FolderOpen className={cn('h-4 w-4 mr-2 flex-shrink-0', color)} />
            ) : (
              <Folder className={cn('h-4 w-4 mr-2 flex-shrink-0', color)} />
            )
          ) : (
            <IconComponent
              className={cn('h-4 w-4 mr-2 flex-shrink-0', color)}
            />
          )}
          <span
            className={cn(
              'truncate font-body',
              currentTheme === 'dark' ? 'text-white' : 'text-black'
            )}
          >
            {node.name}
          </span>
        </div>
      </div>

      {node.type === 'directory' && isExpanded && node.children && (
        <div>
          {node.children.map(child => (
            <FileTreeItem
              key={child.id}
              node={child}
              selectedFileId={selectedFileId}
              onFileSelect={onFileSelect}
              onDirectoryToggle={onDirectoryToggle}
              level={level + 1}
              theme={currentTheme}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export const FileTree: React.FC<FileTreeProps> = ({
  nodes,
  selectedFileId,
  onFileSelect,
  onDirectoryToggle,
  loading,
  theme,
}) => {
  console.log('[FileTree] 렌더링:', {
    loading,
    nodesLength: nodes?.length,
    nodes: nodes?.slice(0, 3), // 처음 3개만 로그
    selectedFileId,
    theme,
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-sm text-gray-500 font-body">
          Loading file tree...
        </div>
      </div>
    );
  }

  if (!nodes || nodes.length === 0) {
    console.log('[FileTree] 노드가 없음:', {
      nodes,
      nodesLength: nodes?.length,
    });
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-sm text-gray-500 font-body">No files found</div>
      </div>
    );
  }

  return (
    <div className="overflow-y-auto h-full">
      {nodes.map(node => (
        <FileTreeItem
          key={node.id}
          node={node}
          selectedFileId={selectedFileId}
          onFileSelect={onFileSelect}
          onDirectoryToggle={onDirectoryToggle}
          level={0}
          theme={theme}
        />
      ))}
    </div>
  );
};
