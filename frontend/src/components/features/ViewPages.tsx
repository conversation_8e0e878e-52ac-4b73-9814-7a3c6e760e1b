import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
// import Editor from '@monaco-editor/react';
import {
  GitBranch,
  Calendar,
  FileText,
  Plus,
  Minus,
  RotateCcw,
  Eye,
  Code,
  List,
  ChevronDown,
  ChevronRight,
  RefreshCw,
  ChevronUp,
} from 'lucide-react';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/Select';
import { repositoryApi, gitApi, lspService } from '@/services/api';
import {
  Repository,
  GitCommit,
  GitCommitDetail,
  GitCommitGroup,
} from '@/types';
import { useRepositoryStore } from '@/stores/repositoryStore';
import { useRepositoryData, useRepositories } from '@/hooks/useRepositories';
import { CommitSummary } from './CommitSummary';

type ViewMode = 'files-only' | 'files-with-content';
type CommitDetailTab = 'details' | 'summary';

export const ViewPages: React.FC = () => {
  // Global repository state
  const { context, setRepositoryContext } = useRepositoryStore();

  // Global repositories data - API 호출이 포함된 훅 사용
  const {
    repositories,
    readyRepositories,
    loading: repositoriesLoading,
    error: repositoriesError,
    refetch,
  } = useRepositories();

  // Local state for UI
  const [selectedCommit, setSelectedCommit] = useState<string>('');
  const [viewMode, setViewMode] = useState<ViewMode>('files-only');
  const [expandedDates, setExpandedDates] = useState<Set<string>>(new Set());
  const [isControlsExpanded, setIsControlsExpanded] = useState<boolean>(true);
  const [expandedFiles, setExpandedFiles] = useState<Set<string>>(new Set()); // Files Only 모드에서 확장된 파일들
  const [commitDetailTab, setCommitDetailTab] =
    useState<CommitDetailTab>('details'); // 커밋 상세 탭 상태

  const queryClient = useQueryClient();

  // Repositories are now managed globally via useRepositoryData hook

  // Fetch branches for selected repository
  const { data: branchesData, isLoading: branchesLoading } = useQuery({
    queryKey: ['branches', context.repository_id],
    queryFn: () => repositoryApi.getBranches(context.repository_id!.toString()),
    enabled: !!context.repository_id,
  });

  // Fetch commits for selected repository and branch
  const { data: commitsData, isLoading: commitsLoading } = useQuery({
    queryKey: ['commits', context.repository_id, context.branch],
    queryFn: () =>
      gitApi.getCommits(context.repository_id!.toString(), context.branch!, {
        per_page: 50,
      }),
    enabled: !!context.repository_id && !!context.branch,
  });

  // Fetch commit detail
  const { data: commitDetailData, isLoading: commitDetailLoading } = useQuery({
    queryKey: ['commit-detail', context.repository_id, selectedCommit],
    queryFn: () =>
      gitApi.getCommitDetail(context.repository_id!.toString(), selectedCommit),
    enabled: !!context.repository_id && !!selectedCommit,
  });

  // Use global repositories data instead of local query
  const branches = branchesData?.data?.branches || [];
  const commits: GitCommit[] = commitsData?.data?.commits || [];
  const commitDetail: GitCommitDetail | null = commitDetailData?.data || null;

  // Pull repository mutation
  const pullMutation = useMutation({
    mutationFn: (repositoryId: string) =>
      repositoryApi.pullRepository(repositoryId),
    onSuccess: async data => {
      toast.success(data.message || 'Repository updated successfully');

      // Invalidate and refetch commits data and file tree
      queryClient.invalidateQueries({
        queryKey: ['commits', context.repository_id, context.branch],
      });
      queryClient.invalidateQueries({
        queryKey: ['tree', context.repository_id],
      });
      queryClient.invalidateQueries({
        queryKey: ['file-tree', context.repository_id],
      });

      // 추가적으로 모든 파일트리 관련 쿼리 무효화
      queryClient.invalidateQueries({ queryKey: ['file-tree'] });
      queryClient.invalidateQueries({ queryKey: ['file-content'] });

      // 강제로 파일트리 새로고침을 위해 약간의 지연 후 다시 무효화
      setTimeout(async () => {
        queryClient.invalidateQueries({
          queryKey: ['file-tree', context.repository_id, context.branch],
        });
        queryClient.refetchQueries({
          queryKey: ['file-tree', context.repository_id],
        });

        // 리포지토리 스캔도 트리거하여 파일트리 업데이트 보장
        try {
          if (context.repository_id) {
            console.log('[ViewPages] 리포지토리 스캔 트리거');
            await repositoryApi.scanRepository(
              context.repository_id.toString()
            );

            // 스캔 후 다시 파일트리 새로고침
            setTimeout(() => {
              queryClient.invalidateQueries({
                queryKey: ['file-tree', context.repository_id],
              });
              queryClient.refetchQueries({
                queryKey: ['file-tree', context.repository_id],
              });
              console.log('[ViewPages] 스캔 후 파일트리 재새로고침 완료');
            }, 2000);
          }
        } catch (error) {
          console.error('[ViewPages] 리포지토리 스캔 실패:', error);
        }

        console.log('[ViewPages] 파일트리 강제 새로고침 완료');
      }, 1000);

      // 10초 후 LSP 업데이트
      if (context.repository_name) {
        console.log(
          '[ViewPages] Git pull 완료, 10초 후 LSP 업데이트 예정:',
          context.repository_name
        );
        setTimeout(async () => {
          try {
            console.log(
              '[ViewPages] LSP 업데이트 시작:',
              context.repository_name
            );
            const lspResponse = await lspService.scanRepositoryByNameAndId(
              context.repository_name!,
              context.repository_id!,
              true // recursive
            );
            console.log('[ViewPages] LSP 업데이트 완료:', lspResponse);
            if (lspResponse.status === 'ok') {
              toast.success(
                `LSP updated: ${lspResponse.symbolsFound || 0} symbols found`
              );
            } else {
              toast.error(
                'LSP update failed: ' + (lspResponse.message || 'Unknown error')
              );
            }
          } catch (error) {
            console.error('[ViewPages] LSP 업데이트 실패:', error);
            toast.error('LSP update failed');
          }
        }, 10000); // 10초 후
      }
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to update repository');
    },
  });

  const handlePull = () => {
    if (context.repository_id) {
      pullMutation.mutate(context.repository_id.toString());
    }
  };

  // Checkout branch mutation
  const checkoutMutation = useMutation({
    mutationFn: ({
      repositoryId,
      branch,
    }: {
      repositoryId: string;
      branch: string;
    }) => repositoryApi.switchBranch(repositoryId, branch),
    onSuccess: data => {
      toast.success(data.message || 'Branch switched successfully');
      // Invalidate and refetch all data for the new branch
      queryClient.invalidateQueries({
        queryKey: ['commits', context.repository_id, context.branch],
      });
      queryClient.invalidateQueries({
        queryKey: ['tree', context.repository_id, context.branch],
      });
      queryClient.invalidateQueries({
        queryKey: ['branches', context.repository_id],
      });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.error || 'Failed to switch branch');
    },
  });

  const handleCheckout = () => {
    if (context.repository_id && context.branch) {
      checkoutMutation.mutate({
        repositoryId: context.repository_id.toString(),
        branch: context.branch,
      });
    }
  };

  // Group commits by date
  const groupedCommits: GitCommitGroup[] = React.useMemo(() => {
    if (!commits || commits.length === 0) {
      return [];
    }

    const groups: { [key: string]: GitCommit[] } = {};

    commits.forEach(commit => {
      try {
        const date = new Date(commit.date).toDateString();
        if (!groups[date]) {
          groups[date] = [];
        }
        groups[date].push(commit);
      } catch (error) {
        console.error('Error parsing commit date:', commit.date, error);
      }
    });

    return Object.entries(groups)
      .map(([date, commits]) => ({ date, commits }))
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [commits]);

  // Repository auto-selection is now handled globally in useRepositories hook

  useEffect(() => {
    if (
      branches.length > 0 &&
      !context.branch &&
      context.repository_id &&
      context.repository_name
    ) {
      const defaultBranch =
        branches.find((branch: any) => branch.isDefault) || branches[0];
      if (defaultBranch) {
        setRepositoryContext(
          context.repository_id,
          context.repository_name,
          defaultBranch.name
        );
      }
    }
  }, [branches, context, setRepositoryContext]);

  // Auto-expand today's commits
  useEffect(() => {
    if (groupedCommits.length > 0) {
      const today = new Date().toDateString();
      const recentDate = groupedCommits[0]?.date;
      if (recentDate && !expandedDates.has(recentDate)) {
        setExpandedDates(new Set([recentDate]));
      }
    }
  }, [groupedCommits]);

  const toggleDateExpansion = (date: string) => {
    const newExpanded = new Set(expandedDates);
    if (newExpanded.has(date)) {
      newExpanded.delete(date);
    } else {
      newExpanded.add(date);
    }
    setExpandedDates(newExpanded);
  };

  // Files Only 모드에서 파일 클릭 핸들러
  const toggleFileExpansion = (fileKey: string) => {
    const newExpanded = new Set(expandedFiles);
    if (newExpanded.has(fileKey)) {
      newExpanded.delete(fileKey);
    } else {
      newExpanded.add(fileKey);
    }
    setExpandedFiles(newExpanded);
  };

  const formatCommitMessage = (message: string) => {
    if (!message) return 'No commit message';
    const lines = message.split('\n');
    return lines[0].trim() || 'No commit message'; // First line only
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getLanguageFromPath = (filePath: string): string => {
    const extension = filePath.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      js: 'javascript',
      jsx: 'javascript',
      ts: 'typescript',
      tsx: 'typescript',
      py: 'python',
      rb: 'ruby',
      php: 'php',
      java: 'java',
      c: 'c',
      cpp: 'cpp',
      cc: 'cpp',
      cxx: 'cpp',
      h: 'c',
      hpp: 'cpp',
      cs: 'csharp',
      go: 'go',
      rs: 'rust',
      sh: 'shell',
      bash: 'shell',
      zsh: 'shell',
      fish: 'shell',
      ps1: 'powershell',
      html: 'html',
      htm: 'html',
      xml: 'xml',
      css: 'css',
      scss: 'scss',
      sass: 'sass',
      less: 'less',
      json: 'json',
      yaml: 'yaml',
      yml: 'yaml',
      toml: 'toml',
      ini: 'ini',
      cfg: 'ini',
      conf: 'ini',
      md: 'markdown',
      markdown: 'markdown',
      sql: 'sql',
      dockerfile: 'dockerfile',
      makefile: 'makefile',
      gitignore: 'gitignore',
      txt: 'plaintext',
    };

    return languageMap[extension || ''] || 'plaintext';
  };

  const parseDiffContent = (diff: string) => {
    const lines = diff.split('\n');
    let inDiffSection = false;
    const diffLines: Array<{
      type: 'context' | 'add' | 'remove' | 'header';
      content: string;
    }> = [];

    for (const line of lines) {
      if (
        line.startsWith('diff --git') ||
        line.startsWith('index ') ||
        line.startsWith('+++') ||
        line.startsWith('---')
      ) {
        diffLines.push({ type: 'header', content: line });
        inDiffSection = true;
      } else if (line.startsWith('@@')) {
        diffLines.push({ type: 'header', content: line });
        inDiffSection = true;
      } else if (inDiffSection) {
        if (line.startsWith('+')) {
          diffLines.push({ type: 'add', content: line });
        } else if (line.startsWith('-')) {
          diffLines.push({ type: 'remove', content: line });
        } else {
          diffLines.push({ type: 'context', content: line });
        }
      }
    }

    return diffLines;
  };

  if (repositoriesLoading) {
    return (
      <div className="py-4">
        <div className="flex items-center justify-center py-8">
          <RotateCcw className="h-6 w-6 animate-spin mr-2" />
          <span className="font-body text-black">Loading repositories...</span>
        </div>
      </div>
    );
  }

  if (repositoriesError) {
    return (
      <div className="py-4">
        <Card>
          <CardHeader>
            <CardTitle className="font-heading text-black">
              Error Loading Repositories
            </CardTitle>
            <CardDescription className="font-body text-red-600">
              {repositoriesError}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => refetch()} variant="outline">
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (repositories.length === 0) {
    return (
      <div className="py-4">
        <Card>
          <CardHeader>
            <CardTitle className="font-heading text-black">
              No Repositories Found
            </CardTitle>
            <CardDescription className="font-body text-gray-700">
              Please clone a repository in Settings first.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full max-w-none h-full flex flex-col space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between flex-shrink-0">
        <div>
          <h1 className="text-3xl font-bold font-heading text-black tracking-tight">
            Repository View
          </h1>
          <p className="font-body text-gray-700">
            Browse commits and file changes in your repositories.
          </p>
        </div>
        <Button
          onClick={() => setIsControlsExpanded(!isControlsExpanded)}
          size="sm"
          variant="ghost"
          className="p-2"
        >
          {isControlsExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Controls */}
      {isControlsExpanded && (
        <Card className="flex-shrink-0">
          <CardHeader>
            <CardTitle className="flex items-center justify-between font-heading text-black">
              <div className="flex items-center gap-2">
                <GitBranch className="h-5 w-5" />
                Repository & Branch Selection
              </div>
              <Button
                onClick={handlePull}
                disabled={!context.repository_id || pullMutation.isPending}
                size="sm"
                variant="outline"
                className="flex items-center gap-2"
              >
                <RefreshCw
                  className={`h-4 w-4 ${pullMutation.isPending ? 'animate-spin' : ''}`}
                />
                {pullMutation.isPending ? 'Updating...' : 'Refresh'}
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
              {/* Repository Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium font-heading text-black">
                  Repository
                </label>
                <Select
                  value={context.repository_id?.toString() || ''}
                  onValueChange={value => {
                    const repo = repositories.find(
                      r => r.id.toString() === value
                    );
                    if (repo) {
                      setRepositoryContext(
                        repo.id,
                        repo.name,
                        repo.current_branch || ''
                      );
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select repository" />
                  </SelectTrigger>
                  <SelectContent>
                    {readyRepositories.map(repo => (
                      <SelectItem key={repo.id} value={repo.id.toString()}>
                        <div className="flex items-center gap-2">
                          <span className="font-body text-black">
                            {repo.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            (ID: {repo.id}, {repo.current_branch})
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Branch Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium font-heading text-black">
                  Branch{' '}
                  {context.branch && (
                    <span className="text-xs text-blue-600 font-normal">
                      (Current: {context.branch})
                    </span>
                  )}
                  {checkoutMutation.isPending && (
                    <span className="text-xs text-orange-600 font-normal">
                      (Switching...)
                    </span>
                  )}
                </label>
                <div className="flex items-center gap-2">
                  <Select
                    value={context.branch || ''}
                    onValueChange={value => {
                      if (context.repository_id && context.repository_name) {
                        // Update context first
                        setRepositoryContext(
                          context.repository_id,
                          context.repository_name,
                          value
                        );
                        // Then trigger checkout
                        checkoutMutation.mutate({
                          repositoryId: context.repository_id.toString(),
                          branch: value,
                        });
                      }
                    }}
                    disabled={
                      !context.repository_id ||
                      branchesLoading ||
                      checkoutMutation.isPending
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select branch" />
                    </SelectTrigger>
                    <SelectContent>
                      {branches.map((branch: any) => (
                        <SelectItem key={branch.name} value={branch.name}>
                          <div className="flex items-center gap-2">
                            <span className="font-body text-black">
                              {branch.name}
                            </span>
                            {branch.isDefault && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-1 rounded">
                                default
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* Checkout Button */}
                  <Button
                    onClick={handleCheckout}
                    disabled={
                      !context.repository_id ||
                      !context.branch ||
                      checkoutMutation.isPending
                    }
                    size="sm"
                    variant="outline"
                    className="flex items-center gap-2 whitespace-nowrap"
                  >
                    <GitBranch
                      className={`h-4 w-4 ${checkoutMutation.isPending ? 'animate-spin' : ''}`}
                    />
                    {checkoutMutation.isPending
                      ? 'Checking out...'
                      : 'Checkout'}
                  </Button>
                </div>
              </div>

              {/* View Mode Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium font-heading text-black">
                  View Mode
                </label>
                <Select
                  value={viewMode}
                  onValueChange={(value: ViewMode) => setViewMode(value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="files-only">
                      <div className="flex items-center gap-2">
                        <List className="h-4 w-4" />
                        <span className="font-body text-black">Files Only</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="files-with-content">
                      <div className="flex items-center gap-2">
                        <Code className="h-4 w-4" />
                        <span className="font-body text-black">
                          Files with Content
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Content */}
      {context.repository_id && context.branch && (
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 flex-1 min-h-0">
          {/* Left Panel - Commits List */}
          <div className="xl:col-span-1 order-1 xl:order-1 flex flex-col min-h-0">
            <Card className="flex-1 flex flex-col min-h-0">
              <CardHeader className="flex-shrink-0">
                <CardTitle className="flex items-center gap-2 font-heading text-black">
                  <Calendar className="h-5 w-5" />
                  Commits
                </CardTitle>
                <CardDescription className="font-body text-gray-700">
                  {commits.length} commits found
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0 flex-1 min-h-0">
                <div className="overflow-y-auto h-full">
                  {commitsLoading ? (
                    <div className="flex items-center justify-center py-8">
                      <RotateCcw className="h-4 w-4 animate-spin mr-2" />
                      <span className="text-sm font-body text-black">
                        Loading commits...
                      </span>
                    </div>
                  ) : !context.repository_id || !context.branch ? (
                    <div className="p-4 text-center text-gray-500 font-body">
                      Please select repository and branch
                    </div>
                  ) : groupedCommits.length === 0 ? (
                    <div className="p-4 text-center text-gray-500 font-body">
                      No commits found for branch "{context.branch}"
                      <br />
                      <span className="text-xs">
                        Total commits available: {commits.length}
                      </span>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      {groupedCommits.map(group => (
                        <div key={group.date}>
                          {/* Date Header */}
                          <button
                            onClick={() => toggleDateExpansion(group.date)}
                            className="w-full flex items-center gap-2 px-4 py-2 text-left hover:bg-gray-50 border-b"
                          >
                            {expandedDates.has(group.date) ? (
                              <ChevronDown className="h-4 w-4" />
                            ) : (
                              <ChevronRight className="h-4 w-4" />
                            )}
                            <span className="text-sm font-medium font-heading text-black">
                              {formatDate(group.date)}
                            </span>
                            <span className="text-xs text-gray-500 ml-auto">
                              {group.commits.length} commits
                            </span>
                          </button>

                          {/* Commits */}
                          {expandedDates.has(group.date) && (
                            <div className="space-y-1">
                              {group.commits.map(commit => (
                                <button
                                  key={commit.hash}
                                  onClick={() => {
                                    setSelectedCommit(commit.hash);
                                  }}
                                  className={`w-full text-left px-6 py-3 hover:bg-gray-50 border-l-2 transition-colors ${
                                    selectedCommit === commit.hash
                                      ? 'border-blue-500 bg-blue-50'
                                      : 'border-transparent'
                                  }`}
                                >
                                  <div className="space-y-1">
                                    <div className="flex items-center gap-2">
                                      <span className="text-xs font-mono text-gray-500">
                                        {commit.shortHash}
                                      </span>
                                      <span className="text-xs text-gray-500">
                                        {formatTime(commit.date)}
                                      </span>
                                    </div>
                                    <p className="text-sm font-body text-black line-clamp-2">
                                      {formatCommitMessage(commit.message)}
                                    </p>
                                    <div className="flex items-center gap-2 text-xs text-gray-500">
                                      <span>{commit.author}</span>
                                      <span>•</span>
                                      <span className="flex items-center gap-1">
                                        <Plus className="h-3 w-3 text-green-600" />
                                        {commit.insertions}
                                      </span>
                                      <span className="flex items-center gap-1">
                                        <Minus className="h-3 w-3 text-red-600" />
                                        {commit.deletions}
                                      </span>
                                    </div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Commit Details */}
          <div className="xl:col-span-2 order-2 xl:order-2 flex flex-col min-h-0">
            {selectedCommit ? (
              <Card className="flex-1 flex flex-col min-h-0">
                <CardHeader className="flex-shrink-0">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2 font-heading text-black">
                      <FileText className="h-5 w-5" />
                      {commitDetailTab === 'details'
                        ? 'Commit Details'
                        : 'Summary by AI'}
                    </CardTitle>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={
                          commitDetailTab === 'details' ? 'default' : 'outline'
                        }
                        size="sm"
                        onClick={() => setCommitDetailTab('details')}
                        className="text-xs"
                      >
                        Details
                      </Button>
                      <Button
                        variant={
                          commitDetailTab === 'summary' ? 'default' : 'outline'
                        }
                        size="sm"
                        onClick={() => setCommitDetailTab('summary')}
                        className="text-xs"
                      >
                        Summary
                      </Button>
                    </div>
                  </div>
                  {commitDetail && commitDetailTab === 'details' && (
                    <CardDescription className="font-body text-gray-700">
                      {commitDetail.stats.totalFiles} files changed,{' '}
                      {commitDetail.stats.totalAdditions} insertions,{' '}
                      {commitDetail.stats.totalDeletions} deletions
                    </CardDescription>
                  )}
                </CardHeader>
                <CardContent className="p-0 flex-1 min-h-0">
                  <div className="overflow-y-auto h-full">
                    {commitDetailLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <RotateCcw className="h-4 w-4 animate-spin mr-2" />
                        <span className="text-sm font-body text-black">
                          Loading commit details...
                        </span>
                      </div>
                    ) : commitDetail ? (
                      commitDetailTab === 'summary' ? (
                        <CommitSummary
                          repositoryId={context.repository_id!.toString()}
                          commitHash={selectedCommit}
                        />
                      ) : (
                        <div className="p-4 space-y-4">
                          {/* Commit Info */}
                          <div className="border-b pb-4">
                            <h3 className="font-medium font-heading text-black mb-2">
                              {commitDetail.commit.subject}
                            </h3>
                            {commitDetail.commit.body && (
                              <p className="text-sm font-body text-gray-700 whitespace-pre-wrap mb-3">
                                {commitDetail.commit.body}
                              </p>
                            )}
                            <div className="flex items-center gap-4 text-sm text-gray-500">
                              <span className="font-mono">
                                {commitDetail.commit.hash}
                              </span>
                              <span>{commitDetail.commit.author}</span>
                              <span>
                                {formatDate(commitDetail.commit.date)}
                              </span>
                            </div>
                          </div>

                          {/* Files */}
                          <div className="space-y-2">
                            <h4 className="font-medium font-heading text-black">
                              Changed Files
                            </h4>
                            {commitDetail.files.map((file, index) => {
                              const fileKey = `${selectedCommit}-${file.path}`;
                              const isFileExpanded = expandedFiles.has(fileKey);

                              return (
                                <div
                                  key={index}
                                  className="border rounded-lg p-3"
                                >
                                  <div
                                    className={`flex items-center gap-2 mb-2 ${
                                      viewMode === 'files-only'
                                        ? 'cursor-pointer hover:bg-gray-50'
                                        : ''
                                    }`}
                                    onClick={() => {
                                      if (viewMode === 'files-only') {
                                        toggleFileExpansion(fileKey);
                                      }
                                    }}
                                  >
                                    <span
                                      className={`text-xs px-2 py-1 rounded ${
                                        file.status === 'added'
                                          ? 'bg-green-100 text-green-800'
                                          : file.status === 'modified'
                                            ? 'bg-blue-100 text-blue-800'
                                            : file.status === 'deleted'
                                              ? 'bg-red-100 text-red-800'
                                              : 'bg-gray-100 text-gray-800'
                                      }`}
                                    >
                                      {file.status}
                                    </span>
                                    <span className="font-mono text-sm font-body text-black">
                                      {file.path}
                                    </span>
                                    <span className="text-xs text-gray-500 ml-auto">
                                      +{file.additions} -{file.deletions}
                                    </span>
                                  </div>

                                  {(viewMode === 'files-with-content' ||
                                    (viewMode === 'files-only' &&
                                      isFileExpanded)) &&
                                    file.diff && (
                                      <div className="mt-2">
                                        <div className="border rounded-lg overflow-hidden">
                                          <div className="bg-gray-100 px-3 py-2 border-b">
                                            <span className="text-xs font-medium text-gray-700">
                                              Diff for {file.path}
                                            </span>
                                          </div>
                                          <div className="max-h-[50vh] overflow-y-auto">
                                            {(() => {
                                              const diffLines =
                                                parseDiffContent(file.diff);
                                              return (
                                                <div className="font-mono text-xs">
                                                  {diffLines.map(
                                                    (line, lineIndex) => (
                                                      <div
                                                        key={lineIndex}
                                                        className={`px-3 py-1 ${
                                                          line.type === 'add'
                                                            ? 'bg-green-50 text-green-800'
                                                            : line.type ===
                                                                'remove'
                                                              ? 'bg-red-50 text-red-800'
                                                              : line.type ===
                                                                  'header'
                                                                ? 'bg-gray-100 text-gray-700 font-medium'
                                                                : 'bg-white text-gray-800'
                                                        }`}
                                                      >
                                                        {line.content}
                                                      </div>
                                                    )
                                                  )}
                                                </div>
                                              );
                                            })()}
                                          </div>
                                        </div>
                                      </div>
                                    )}
                                </div>
                              );
                            })}
                          </div>
                        </div>
                      )
                    ) : (
                      <div className="p-4 text-center text-gray-500 font-body">
                        Failed to load commit details
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card className="h-[calc(100vh-300px)] min-h-[400px] xl:min-h-[500px]">
                <CardContent className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500 font-body">
                      Select a commit to view details
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
