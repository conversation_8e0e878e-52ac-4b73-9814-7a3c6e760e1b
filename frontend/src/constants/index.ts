// API endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REGISTER: '/auth/register',
    VALIDATE: '/auth/validate',
    REFRESH: '/auth/refresh',
  },
  SEARCH: {
    QUERY: '/search',
    SUGGESTIONS: '/search/suggestions',
    HISTORY: '/search/history',
  },
  CHAT: {
    SESSIONS: '/chat/sessions',
    MESSAGES: '/chat/messages',
    SEND: '/chat/send',
  },
  ANALYSIS: {
    METRICS: '/analysis/metrics',
    FILES: '/analysis/files',
    DEPENDENCIES: '/analysis/dependencies',
    COMPLEXITY: '/analysis/complexity',
  },
  MODELS: {
    LIST: '/models',
    CONFIG: '/models/config',
    STATUS: '/models/status',
  },
} as const;

// Application routes
export const ROUTES = {
  HOME: '/',
  SEARCH: '/search',
  CHAT: '/chat',
  ANALYSIS: '/analysis',
  MODELS: '/models',
  DATA: '/data',
  DOCS: '/docs',
  SETTINGS: '/settings',
  LOGIN: '/login',
  REGISTER: '/register',
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  SIDEBAR_STATE: 'sidebar_state',
  RECENT_SEARCHES: 'recent_searches',
  OLLAMA_SETTINGS: 'ollama-settings',
} as const;

// Theme configuration
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system',
} as const;

// File type mappings
export const FILE_TYPES = {
  JAVASCRIPT: ['js', 'jsx', 'ts', 'tsx'],
  PYTHON: ['py', 'pyx', 'pyi'],
  JAVA: ['java'],
  CSHARP: ['cs'],
  CPP: ['cpp', 'cc', 'cxx', 'c++', 'c'],
  RUST: ['rs'],
  GO: ['go'],
  PHP: ['php'],
  RUBY: ['rb'],
  SWIFT: ['swift'],
  KOTLIN: ['kt', 'kts'],
  SCALA: ['scala'],
  CLOJURE: ['clj', 'cljs'],
  HASKELL: ['hs'],
  ERLANG: ['erl'],
  ELIXIR: ['ex', 'exs'],
  LUA: ['lua'],
  PERL: ['pl', 'pm'],
  R: ['r'],
  MATLAB: ['m'],
  SQL: ['sql'],
  HTML: ['html', 'htm'],
  CSS: ['css', 'scss', 'sass', 'less'],
  XML: ['xml'],
  JSON: ['json'],
  YAML: ['yaml', 'yml'],
  TOML: ['toml'],
  MARKDOWN: ['md', 'markdown'],
  TEXT: ['txt'],
  CONFIG: ['conf', 'config', 'ini'],
  SHELL: ['sh', 'bash', 'zsh', 'fish'],
  DOCKERFILE: ['dockerfile'],
  MAKEFILE: ['makefile', 'mk'],
} as const;

// Language colors for syntax highlighting
export const LANGUAGE_COLORS = {
  javascript: '#f7df1e',
  typescript: '#3178c6',
  python: '#3776ab',
  java: '#ed8b00',
  csharp: '#239120',
  cpp: '#00599c',
  rust: '#000000',
  go: '#00add8',
  php: '#777bb4',
  ruby: '#cc342d',
  swift: '#fa7343',
  kotlin: '#7f52ff',
  scala: '#dc322f',
  clojure: '#5881d8',
  haskell: '#5d4f85',
  erlang: '#b83998',
  elixir: '#6e4a7e',
  lua: '#000080',
  perl: '#39457e',
  r: '#276dc3',
  matlab: '#e16737',
  sql: '#336791',
  html: '#e34c26',
  css: '#1572b6',
  xml: '#0060ac',
  json: '#000000',
  yaml: '#cb171e',
  markdown: '#083fa1',
  shell: '#89e051',
} as const;

// Search filters
export const SEARCH_FILTERS = {
  FILE_TYPES: Object.keys(FILE_TYPES),
  SORT_OPTIONS: [
    { value: 'relevance', label: 'Relevance' },
    { value: 'date', label: 'Date Modified' },
    { value: 'name', label: 'File Name' },
    { value: 'size', label: 'File Size' },
  ],
  DATE_RANGES: [
    { value: 'today', label: 'Today' },
    { value: 'week', label: 'This Week' },
    { value: 'month', label: 'This Month' },
    { value: 'year', label: 'This Year' },
    { value: 'custom', label: 'Custom Range' },
  ],
} as const;

// Chat configuration
export const CHAT_CONFIG = {
  MAX_MESSAGE_LENGTH: 4000,
  MAX_HISTORY_LENGTH: 50,
  TYPING_DELAY: 100,
  AUTO_SAVE_INTERVAL: 30000, // 30 seconds
} as const;

// Analysis configuration
export const ANALYSIS_CONFIG = {
  COMPLEXITY_THRESHOLDS: {
    LOW: 10,
    MEDIUM: 20,
    HIGH: 30,
  },
  FILE_SIZE_LIMITS: {
    SMALL: 1024, // 1KB
    MEDIUM: 10240, // 10KB
    LARGE: 102400, // 100KB
  },
} as const;

// Ollama configuration
export const OLLAMA_CONFIG = {
  DEFAULT_MODEL: 'gemma3:12b',
  RECOMMENDED_MODELS: [
    {
      name: 'gemma3:12b',
      description: 'Gemma 3 12B - General purpose language model',
      size: '6.5GB',
      use_case: 'General text analysis and code understanding',
    },
    {
      name: 'codellama:7b',
      description:
        'Code Llama 7B - Specialized for code generation and analysis',
      size: '3.8GB',
      use_case: 'Code analysis, commit summaries, and code explanations',
    },
    {
      name: 'deepseek-coder:6.7b',
      description: 'DeepSeek Coder - Optimized for coding tasks',
      size: '3.7GB',
      use_case: 'Code completion, code review, and technical analysis',
    },
    {
      name: 'mistral:7b',
      description: 'Mistral 7B - Fast and efficient model',
      size: '4.1GB',
      use_case: 'Quick analysis and general purpose tasks',
    },
  ],
  CHUNK_SIZE_LIMIT: 6000 * 1024, // 6000k characters
  MAX_FILE_SIZE: 500 * 1024, // 500k characters per file
} as const;
