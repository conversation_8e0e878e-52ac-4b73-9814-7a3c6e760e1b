class CSRFManager {
  private token: string | null = null;
  private cookieName: string | null = null;

  async fetchToken(): Promise<string> {
    try {
      const response = await fetch('/api/csrf_token', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      this.token = data.csrf_token;
      this.cookieName = data.cookie_name;
      return this.token;
    } catch (error) {
      console.error('CSRF token fetch failed:', error);
      throw error;
    }
  }

  getToken(): string | null {
    return this.token;
  }

  setToken(token: string): void {
    this.token = token;
  }

  getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['X-CSRF-Token'] = this.token;
    }

    return headers;
  }

  getCookieName(): string | null {
    return this.cookieName;
  }
}

export const csrfManager = new CSRFManager();
