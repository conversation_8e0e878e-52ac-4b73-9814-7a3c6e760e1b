import {
  createRootRoute,
  Outlet,
  redirect,
  useRouterState,
} from '@tanstack/react-router';
import { useAuth } from '@/contexts/AuthContext';
import { TanStackRouterDevtools } from '@tanstack/router-devtools';
import { Toaster } from 'react-hot-toast';

import { Layout } from '@/components/layout/Layout';

export const Route = createRootRoute({
  // This is not a component, so we can't use hooks. But we can use the context.
  // The context is passed from the router instance.
  // The router instance gets the context from the App component.
  // The App component gets the context from the AuthProvider.

  beforeLoad: async ({ context, location }) => {
    const { isAuthenticated, isLoading } = context.auth;

    if (isLoading) {
      // We can show a loading spinner here if we want
      return;
    }

    if (!isAuthenticated && location.pathname !== '/login') {
      throw redirect({
        to: '/login',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: function Root(): React.ReactElement {
    const { isAuthenticated, isLoading } = useAuth();
    const router = useRouterState();
    const isLoginPage = router.location.pathname === '/login';

    if (isLoading) {
      return <div>Loading...</div>; // Or a proper spinner component
    }

    return (
      <>
        {isLoginPage ? (
          <Outlet />
        ) : (
          <Layout isAuthenticated={isAuthenticated}>
            <Outlet />
          </Layout>
        )}
        <Toaster
          position="top-right"
          toastOptions={{
            duration: 4000,
            style: {
              background: 'hsl(var(--card))',
              color: 'hsl(var(--card-foreground))',
              border: '1px solid hsl(var(--border))',
            },
          }}
        />
        {process.env.NODE_ENV === 'development' && <TanStackRouterDevtools />}
      </>
    );
  },
});
