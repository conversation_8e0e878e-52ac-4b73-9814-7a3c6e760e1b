import { createFileRoute, redirect } from '@tanstack/react-router';
import { LoginPage } from '@/components/features/LoginPage';

export const Route = createFileRoute('/login')({
  beforeLoad: ({ context, location }) => {
    // @ts-ignore
    if (context.auth.isAuthenticated) {
      throw redirect({
        to: '/',
        search: {
          redirect: location.href,
        },
      });
    }
  },
  component: LoginPage,
});
