import path from 'path';
import { defineConfig } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import { tanstackRouter } from '@tanstack/router-plugin/rspack';

export default defineConfig({
  plugins: [pluginReact(), pluginSass()],
  source: {
    entry: { index: './src/index.tsx' },
  },
  resolve: {
    alias: { 
      '@': path.resolve(__dirname, './src'),
      '@/components': path.resolve(__dirname, './src/components'),
      '@/utils': path.resolve(__dirname, './src/utils'),
      '@/types': path.resolve(__dirname, './src/types'),
      '@/hooks': path.resolve(__dirname, './src/hooks'),
      '@/services': path.resolve(__dirname, './src/services'),
      '@/constants': path.resolve(__dirname, './src/constants'),
    },
  },
  tools: {
    rspack: {
      plugins: [
        tanstackRouter({
          routesDirectory: './src/routes',
          generatedRouteTree: './src/routeTree.gen.ts',
          target: 'react',
          autoCodeSplitting: true,
        }),
      ],
    },
    postcss: {
      postcssOptions: {
        plugins: [
          require('tailwindcss'),
          require('autoprefixer'),
        ],
      },
    },
  },
  html: {
    template: path.resolve(__dirname, './public/index.html'),
    title: 'CodeBase Intelligence',
    // favicon: './public/favicon.ico',
  },
  server: {
    port: 5173,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://gserver.parrot-mine.ts.net:7101',
        changeOrigin: true,
      },
      '/ai': {
        target: 'http://gserver.parrot-mine.ts.net:7102',
        changeOrigin: true,
        pathRewrite: {
          '^/ai': '/api/v1',
        },
      },
      '/qdrant': {
        target: 'http://gserver.parrot-mine.ts.net:7105',
        changeOrigin: true,
        pathRewrite: { '^/qdrant': '' },
      },
      '/lsp': {
        target: 'http://gserver.parrot-mine.ts.net:7107',
        changeOrigin: true,
        pathRewrite: { '^/lsp': '' },
      },
      '/ollama': {
        target: 'http://gserver.parrot-mine.ts.net:11434',
        changeOrigin: true,
        pathRewrite: { '^/ollama': '' },
      },
    },
  },
  output: {
    distPath: {
      root: 'dist',
    },
    cleanDistPath: true,
  },
});
