// Simple test script to verify aiend API integration
// Run this in browser console to test the aiend service

// Test aiend service health check
async function testAiendHealth() {
  try {
    const response = await fetch('/aiend/api/v1/health/');
    const data = await response.json();
    console.log('✅ aiend Health Check:', data);
    return true;
  } catch (error) {
    console.error('❌ aiend Health Check Failed:', error);
    return false;
  }
}

// Test aiend detailed health check
async function testAiendDetailedHealth() {
  try {
    const response = await fetch('/aiend/api/v1/health/detailed');
    const data = await response.json();
    console.log('✅ aiend Detailed Health Check:', data);
    return true;
  } catch (error) {
    console.error('❌ aiend Detailed Health Check Failed:', error);
    return false;
  }
}

// Test search modes
async function testSearchModes() {
  try {
    const response = await fetch('/aiend/api/v1/search/modes');
    const data = await response.json();
    console.log('✅ Search Modes:', data);
    return true;
  } catch (error) {
    console.error('❌ Search Modes Failed:', error);
    return false;
  }
}

// Test context types
async function testContextTypes() {
  try {
    const response = await fetch('/aiend/api/v1/search/context-types');
    const data = await response.json();
    console.log('✅ Context Types:', data);
    return true;
  } catch (error) {
    console.error('❌ Context Types Failed:', error);
    return false;
  }
}

// Test vector store status
async function testVectorStoreStatus() {
  try {
    const response = await fetch('/aiend/api/v1/search/vector/status');
    const data = await response.json();
    console.log('✅ Vector Store Status:', data);
    return true;
  } catch (error) {
    console.error('❌ Vector Store Status Failed:', error);
    return false;
  }
}

// Test text encoding
async function testTextEncoding() {
  try {
    const response = await fetch('/aiend/api/v1/search/encode', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        text: 'Hello world test encoding'
      })
    });
    const data = await response.json();
    console.log('✅ Text Encoding:', data);
    return true;
  } catch (error) {
    console.error('❌ Text Encoding Failed:', error);
    return false;
  }
}

// Test natural language query
async function testNaturalLanguageQuery() {
  try {
    const response = await fetch('/aiend/api/v1/search/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'How does authentication work?',
        max_results: 3
      })
    });
    const data = await response.json();
    console.log('✅ Natural Language Query:', data);
    return true;
  } catch (error) {
    console.error('❌ Natural Language Query Failed:', error);
    return false;
  }
}

// Test codebase search
async function testCodebaseSearch() {
  try {
    const response = await fetch('/aiend/api/v1/search/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: 'authentication',
        mode: 'semantic',
        max_results: 5
      })
    });
    const data = await response.json();
    console.log('✅ Codebase Search:', data);
    return true;
  } catch (error) {
    console.error('❌ Codebase Search Failed:', error);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Starting aiend API Integration Tests...\n');
  
  const tests = [
    { name: 'Health Check', fn: testAiendHealth },
    { name: 'Detailed Health Check', fn: testAiendDetailedHealth },
    { name: 'Search Modes', fn: testSearchModes },
    { name: 'Context Types', fn: testContextTypes },
    { name: 'Vector Store Status', fn: testVectorStoreStatus },
    { name: 'Text Encoding', fn: testTextEncoding },
    { name: 'Natural Language Query', fn: testNaturalLanguageQuery },
    { name: 'Codebase Search', fn: testCodebaseSearch }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    console.log(`\n🧪 Running ${test.name}...`);
    try {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    } catch (error) {
      console.error(`❌ ${test.name} threw an error:`, error);
      failed++;
    }
    
    // Add a small delay between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! aiend API integration is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Check the aiend service configuration and network connectivity.');
  }
}

// Export functions for manual testing
window.aiendTests = {
  runAllTests,
  testAiendHealth,
  testAiendDetailedHealth,
  testSearchModes,
  testContextTypes,
  testVectorStoreStatus,
  testTextEncoding,
  testNaturalLanguageQuery,
  testCodebaseSearch
};

console.log('🔧 aiend API Test Suite loaded. Run window.aiendTests.runAllTests() to start testing.');
