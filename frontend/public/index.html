<!DOCTYPE html>
<html lang="ko">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  
  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap" rel="stylesheet">
  
  <!-- Favicon - 필요시 public 폴더에 favicon.ico 추가 후 활성화 -->
  <!-- <link rel="icon" type="image/svg+xml" href="/favicon.ico" /> -->
  
  <title>React App</title>
  
  <style>
    /* CSS 변수로 폰트 패밀리 정의 */
    :root {
      --font-roboto: 'Roboto', sans-serif;
      --font-open-sans: 'Open Sans', sans-serif;
      --font-lato: 'Lato', sans-serif;
    }
    
    /* 기본 폰트 설정 */
    body {
      margin: 0;
      font-family: var(--font-roboto);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    /* 제목용 폰트 */
    h1, h2, h3, h4, h5, h6 {
      font-family: var(--font-lato);
    }
    
    /* 본문용 폰트 */
    p, span, div {
      font-family: var(--font-open-sans);
    }
    
    /* 코드용 폰트 */
    code {
      font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New', monospace;
    }
  </style>
</head>
<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <script type="module" src="/src/main.jsx"></script>
</body>
</html>