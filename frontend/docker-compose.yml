services:
  # Frontend Service (React + RSBuild) - Development
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: codebase-intelligence-frontend-dev
    ports:
      - "7103:5173"
    environment:
      - VITE_API_URL=http://localhost:7102
      - VITE_WS_URL=ws://localhost:7102/cable
      - VITE_AI_ENGINE_URL=http://localhost:7101
      - NODE_ENV=development
    volumes:
      - .:/app
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service (React + RSBuild) - Production
  frontend-prod:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: codebase-intelligence-frontend-prod
    ports:
      - "7104:80"
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - production

networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence
