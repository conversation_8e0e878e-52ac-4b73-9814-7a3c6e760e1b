version: '3.8'

services:
  # AI Engine Service
  aiend:
    build:
      context: ./aiend
      dockerfile: Dockerfile
    container_name: codebase-intelligence-aiend
    ports:
      - "7102:8000"  # FastAPI
    environment:
      - PYTHONPATH=/app
      - CODEBASE_DB_PATH=/app/data/codebase.db
      - CODEBASE_VECTOR_PATH=/app/data/vectordb
      - CODEBASE_VECTOR_MODEL=dengcao/Qwen3-Embedding-8B:Q5_K_M
      - CODEBASE_REPO_PATH=/app/repositories
      - QDRANT_URL=http://qdrant:6333
      - QDRANT_PORT=6333
      - CODEBASE_LOG_LEVEL=INFO
      - OLLAMA_HOST=http://host.docker.internal:11434
      - REDIS_URL=redis://redis:6379
    volumes:
      # 소스코드 마운트 (개발용 - 코드 변경 시 즉시 반영)
      - ./aiend/src:/app/src
      - ./aiend/config:/app/config

      # 데이터 마운트 (영구 저장용)
      - /mnt/hdd500/data/codebase-intelligence/aiend/data:/app/data
      - /mnt/hdd500/data/codebase-intelligence/aiend/logs:/app/logs
      - /mnt/hdd500/data/codebase-intelligence/repos:/app/repositories
    networks:
      - codebase-intelligence
    depends_on:
      - qdrant
      - redis
    restart: unless-stopped
    user: "1000:1000"  # aiend 사용자 UID:GID
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend Service (Ruby on Rails)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: codebase-intelligence-backend
    ports:
      - "7101:3000"
    environment:
      - RAILS_ENV=development
      - DATABASE_URL=sqlite3:/app/storage/development.sqlite3
      - AI_ENGINE_URL=http://aiend:8000
      - AIEND_BASE_URL=http://aiend:8000
      - AIEND_ENABLED=true
      - RAILS_LOG_LEVEL=info
    volumes:
      - ./backend:/app
      - /mnt/hdd500/data/codebase-intelligence/backend/storage:/app/storage
      - /mnt/hdd500/data/codebase-intelligence/repos:/app/repositories
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/up"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service (React + RSBuild) - Development
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: development
    container_name: codebase-intelligence-frontend-dev
    ports:
      - "7103:5173"
    environment:
      - VITE_API_URL=http://localhost:7101
      - VITE_WS_URL=ws://localhost:7101/cable
      - VITE_AI_ENGINE_URL=http://localhost:7102
      - NODE_ENV=development
    volumes:
      - ./frontend:/app
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Service (React + RSBuild) - Production
  frontend-prod:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: codebase-intelligence-frontend-prod
    ports:
      - "7104:80"
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - production

  # Qdrant 벡터DB - 모든 벡터 인덱싱과 검색을 처리
  qdrant:
    image: qdrant/qdrant:latest
    container_name: codebase-intelligence-qdrant
    ports:
      - "7105:6333"  # REST API
      - "7106:6334"  # gRPC API
    environment:
      - QDRANT__LOG_LEVEL=INFO
      - QDRANT__SERVICE__HOST=0.0.0.0
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    volumes:
      - /mnt/hdd500/data/codebase-intelligence/vectordb/qdrant:/qdrant/storage
      - /mnt/hdd500/data/codebase-intelligence/vectordb/snapshots:/qdrant/snapshots
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      disable: true
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 2G
          cpus: '1'

  # Redis 캐시 서버 (aiend용)
  redis:
    image: redis:7.0-alpine
    container_name: codebase-intelligence-redis
    ports:
      - "7108:6379"
    command: redis-server --appendonly yes
    volumes:
      - /mnt/hdd500/data/codebase-intelligence/redis:/data
    networks:
      - codebase-intelligence
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence