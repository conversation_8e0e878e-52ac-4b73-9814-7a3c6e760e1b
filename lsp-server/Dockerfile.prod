# 프로덕션용 Dockerfile

FROM node:18-slim
WORKDIR /usr/src/app

# Install dependencies for tree-sitter
R<PERSON> apt-get update && apt-get install -y python3 make g++ && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY tsconfig.json ./

# Install dev dependencies for build
RUN npm install --save-dev typescript ts-node @types/node

# Build the application
RUN npm run build

# Remove dev dependencies to reduce image size
RUN npm prune --production

EXPOSE 7107

# 프로덕션 실행
CMD ["node", "dist/main.js"]
