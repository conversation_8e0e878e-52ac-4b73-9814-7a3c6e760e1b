version: '3.8'
services:
  lsp-server:
    build:
      context: .
      dockerfile: Dockerfile.dev  # 개발용 Dockerfile 사용
    container_name: codebase-lsp-server-dev
    ports:
      - "7107:7107"
    volumes:
      - ../backend:/code/backend
      - ../frontend:/code/frontend
      - ../aiend:/code/aiend
      - ./data_lsp:/usr/src/app/data_lsp  # Bind mount for direct access
      - /mnt/hdd500/data/codebase-intelligence/repos:/usr/src/app/repositories:ro  # Shared repositories
      # 개발 편의성을 위한 소스 코드 볼륨 마운트
      - ./src:/usr/src/app/src  # 소스 코드 실시간 반영
      - ./package.json:/usr/src/app/package.json  # package.json 동기화
      - ./tsconfig.json:/usr/src/app/tsconfig.json  # TypeScript 설정 동기화
      - ./nodemon.json:/usr/src/app/nodemon.json  # nodemon 설정 동기화
      - ./.env:/usr/src/app/.env  # 환경변수 파일 동기화
    networks:
      - codebase-intelligence
    env_file:
      - .env
    environment:
      - NODE_ENV=development  # 개발 모드로 변경
      - REPOSITORIES_PATH=/usr/src/app/repositories
    # 개발 모드에서는 nodemon으로 실행하여 실시간 반영
    command: ["npm", "run", "dev"]
    restart: unless-stopped

networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence
