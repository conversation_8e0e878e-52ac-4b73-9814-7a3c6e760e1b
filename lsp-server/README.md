# LSP Server

LSP(Language Server Protocol) & Tree-sitter 통합 분석 서버입니다.

## Directory Structure

- `src/` - Source code
- `data_lsp/` - SQLite database storage
- `dist/` - Compiled JavaScript files

## Development

### Local Development

```bash
# 의존성 설치
npm install

# 개발 모드 실행 (nodemon으로 자동 재시작)
npm run dev

# 직접 실행
npm run dev:direct

# 빌드
npm run build

# 프로덕션 실행
npm start
```

### Docker Development

개발 편의성을 위해 소스 코드가 실시간으로 반영되는 Docker 개발 환경을 제공합니다.

```bash
# 개발 모드 실행 (소스 코드 볼륨 마운트, 자동 재시작)
npm run docker:dev

# 개발 모드 종료
npm run docker:dev:down

# 프로덕션 모드 실행
npm run docker:prod

# 프로덕션 모드 종료
npm run docker:prod:down
```

#### 개발 모드 특징

- ✅ **최적화된 이미지**: 개발용 Dockerfile(`Dockerfile.dev`) 사용으로 불필요한 파일 복사 방지
- ✅ **실시간 코드 반영**: `src/` 폴더가 볼륨 마운트되어 코드 변경 시 즉시 반영
- ✅ **자동 재시작**: `nodemon`을 사용하여 파일 변경 시 자동으로 서버 재시작
- ✅ **설정 동기화**: `package.json`, `tsconfig.json`, `nodemon.json`, `.env` 파일 실시간 동기화
- ✅ **개발 환경**: `NODE_ENV=development`로 설정

#### Docker 파일 구조

| 파일 | 용도 | 특징 |
|------|------|------|
| `Dockerfile` | 프로덕션용 | 소스 코드 복사 + 빌드 + 최적화 |
| `Dockerfile.dev` | 개발용 | 의존성만 설치, 소스는 볼륨 마운트 |
| `docker-compose.yml` | 개발 환경 | 볼륨 마운트 + nodemon |
| `docker-compose.prod.yml` | 프로덕션 환경 | 빌드된 코드 실행 |

#### 볼륨 마운트 구조

```
Host                              Container
./src/                    →      /usr/src/app/src/
./package.json           →      /usr/src/app/package.json
./tsconfig.json          →      /usr/src/app/tsconfig.json
./nodemon.json           →      /usr/src/app/nodemon.json
./.env                   →      /usr/src/app/.env
./data_lsp/              →      /usr/src/app/data_lsp/
/mnt/hdd500/.../repos/   →      /usr/src/app/repositories/ (read-only)
```

### API Endpoints

서버 실행 후 다음 엔드포인트들을 사용할 수 있습니다:

- `GET /health` - 서버 상태 확인
- `POST /analysis/scan` - 직접 경로 스캔
- `POST /analysis/scan-repository` - 리포지토리 폴더명으로 스캔
- `GET /repositories` - 사용 가능한 리포지토리 목록
- `GET /repositories/:folderName` - 특정 리포지토리 정보
- `GET /symbols/definition` - 심볼 정의 조회
- `GET /symbols/references` - 심볼 참조 조회
- `GET /files/:path/symbols` - 파일의 심볼 목록
- `GET /files/:path/ast` - 파일의 AST (디버깅용)
- `GET /stats` - 분석 통계

## Architecture

자세한 설계 및 아키텍처 정보는 `md/lsp.guide.md`를 참조하세요.

## API Documentation

완전한 API 문서는 `md/lsp.api.md`를 참조하세요.
