{"name": "lsp-server", "version": "1.0.0", "description": "LSP and Tree-sitter server for CodeBase Intelligence", "main": "dist/main.js", "scripts": {"build": "tsc", "start": "node dist/main.js", "start:lsp-client": "USE_LSP_CLIENT=true node dist/main.js", "start:lsp-only": "LSP_MODE=true node dist/main.js", "dev": "nodemon --exec 'npm run build && npm run start'", "dev:lsp-client": "nodemon --exec 'npm run build && npm run start:lsp-client'", "standalone:lsp": "node dist/lsp/standalone.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"better-sqlite3": "^11.1.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "tree-sitter": "^0.21.1", "tree-sitter-javascript": "^0.21.2", "tree-sitter-python": "^0.21.0", "tree-sitter-ruby": "^0.23.1", "tree-sitter-typescript": "^0.23.2", "vscode-languageserver": "^9.0.1", "vscode-languageserver-textdocument": "^1.0.11"}, "devDependencies": {"@types/better-sqlite3": "^7.6.11", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/node": "^20.14.11", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}