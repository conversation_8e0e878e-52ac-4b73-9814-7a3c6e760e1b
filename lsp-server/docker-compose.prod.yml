version: '3.8'
services:
  lsp-server:
    build:
      context: .
      dockerfile: Dockerfile.prod  # 프로덕션용 Dockerfile 사용 (기본값)
    container_name: codebase-lsp-server-prod
    ports:
      - "7107:7107"
    volumes:
      - ./data_lsp:/usr/src/app/data_lsp  # DB 저장용
      - /mnt/hdd500/data/codebase-intelligence/repos:/usr/src/app/repositories:ro  # 공유 리포지토리
    networks:
      - codebase-intelligence
    env_file:
      - .env
    environment:
      - NODE_ENV=production
      - REPOSITORIES_PATH=/usr/src/app/repositories
    # 프로덕션에서는 빌드된 파일로 실행
    command: ["node", "dist/main.js"]
    restart: unless-stopped

networks:
  codebase-intelligence:
    external: true
    name: codebase-intelligence
