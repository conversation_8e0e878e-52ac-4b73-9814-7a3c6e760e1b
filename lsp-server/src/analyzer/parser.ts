// Tree-sitter 공식 권장 방식 적용
const Parser = require('tree-sitter');
const JavaScript = require('tree-sitter-javascript');
const TypeScript = require('tree-sitter-typescript').typescript;
const Ruby = require('tree-sitter-ruby');
const Python = require('tree-sitter-python');
import { languageQueries, getLanguageFromExtension } from './queries';
import { Symbol, Reference, SymbolKind } from '../shared/types';
import logger from '../shared/logger';

// 타입 선언 보완
// eslint-disable-next-line @typescript-eslint/no-namespace
declare namespace Parser {
  interface Tree {
    rootNode: SyntaxNode;
  }
  interface SyntaxNode {
    type: string;
    startPosition: { row: number; column: number };
    endPosition: { row: number; column: number };
    children: SyntaxNode[];
    text?: string;
    [key: string]: any;
  }
}

export class TreeSitterParser {
  private parsers: { [key: string]: any } = {};

  constructor() {
    const languages = [
      { name: 'javascript', lang: JavaScript },
      { name: 'typescript', lang: TypeScript },
      { name: 'ruby', lang: Ruby },
      { name: 'python', lang: Python }
    ];

    languages.forEach(({ name, lang }) => {
      try {
        this.parsers[name] = new Parser();
        this.parsers[name].setLanguage(lang);
        logger.info(`[INFO] Initialized ${name} parser`);
      } catch (error) {
        logger.error(`[ERROR] Failed to initialize ${name} parser:`, error);
      }
    });
  }

  // 파일 분석
  async analyzeFile(filePath: string, content: string, repositoryFolderName: string): Promise<{ symbols: Symbol[], references: Reference[] }> {
    const language = getLanguageFromExtension(filePath);

    if (!language || !this.parsers[language]) {
      logger.warn(`Unsupported language for file: ${filePath}`);
      return { symbols: [], references: [] };
    }

    const parser = this.parsers[language];
    const tree = parser.parse(content);

    logger.debug(`Parsed ${filePath} with ${language} parser`);

    return this.extractSymbolsAndReferences(tree, filePath, content, language, repositoryFolderName);
  }

  // 심볼과 참조 추출
  private extractSymbolsAndReferences(
    tree: Parser.Tree,
    filePath: string,
    content: string,
    language: string,
    repositoryFolderName: string
  ): { symbols: Symbol[], references: Reference[] } {
    const symbols: Symbol[] = [];
    const references: Reference[] = [];

    // 간단한 AST 순회로 심볼 추출
    this.traverseNode(tree.rootNode, content, filePath, language, symbols, references, repositoryFolderName);

    return { symbols, references };
  }

  // AST 노드 순회
  private traverseNode(
    node: Parser.SyntaxNode,
    content: string,
    filePath: string,
    language: string,
    symbols: Symbol[],
    references: Reference[],
    repositoryFolderName: string
  ): void {
    // 언어별 노드 타입에 따라 심볼 추출
    switch (language) {
      case 'javascript':
      case 'typescript':
        this.extractJSSymbols(node, content, filePath, symbols, repositoryFolderName);
        break;
      case 'ruby':
        this.extractRubySymbols(node, content, filePath, symbols, repositoryFolderName);
        break;
      case 'python':
        this.extractPythonSymbols(node, content, filePath, symbols, repositoryFolderName);
        break;
    }

    // 자식 노드들도 순회
    for (const child of node.children) {
      this.traverseNode(child, content, filePath, language, symbols, references, repositoryFolderName);
    }
  }

  // JavaScript/TypeScript 심볼 추출
  private extractJSSymbols(
    node: Parser.SyntaxNode,
    content: string,
    filePath: string,
    symbols: Symbol[],
    repositoryFolderName: string
  ): void {
    switch (node.type) {
      case 'function_declaration':
        const funcName = this.findChildByType(node, 'identifier');
        if (funcName) {
          symbols.push({
            name: this.getNodeText(funcName, content),
            kind: SymbolKind.FUNCTION,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
      case 'class_declaration':
        const className = this.findChildByType(node, 'identifier');
        if (className) {
          symbols.push({
            name: this.getNodeText(className, content),
            kind: SymbolKind.CLASS,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
      case 'variable_declarator':
        const varName = this.findChildByType(node, 'identifier');
        if (varName) {
          symbols.push({
            name: this.getNodeText(varName, content),
            kind: SymbolKind.VARIABLE,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
    }
  }

  // Ruby 심볼 추출
  private extractRubySymbols(
    node: Parser.SyntaxNode,
    content: string,
    filePath: string,
    symbols: Symbol[],
    repositoryFolderName: string
  ): void {
    switch (node.type) {
      case 'method':
        const methodName = this.findChildByType(node, 'identifier');
        if (methodName) {
          symbols.push({
            name: this.getNodeText(methodName, content),
            kind: SymbolKind.METHOD,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
      case 'class':
        const className = this.findChildByType(node, 'constant');
        if (className) {
          symbols.push({
            name: this.getNodeText(className, content),
            kind: SymbolKind.CLASS,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
    }
  }

  // Python 심볼 추출
  private extractPythonSymbols(
    node: Parser.SyntaxNode,
    content: string,
    filePath: string,
    symbols: Symbol[],
    repositoryFolderName: string
  ): void {
    switch (node.type) {
      case 'function_definition':
        const funcName = this.findChildByType(node, 'identifier');
        if (funcName) {
          symbols.push({
            name: this.getNodeText(funcName, content),
            kind: SymbolKind.FUNCTION,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
      case 'class_definition':
        const className = this.findChildByType(node, 'identifier');
        if (className) {
          symbols.push({
            name: this.getNodeText(className, content),
            kind: SymbolKind.CLASS,
            filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
        break;
    }
  }

  // 특정 타입의 자식 노드 찾기
  private findChildByType(node: Parser.SyntaxNode, type: string): Parser.SyntaxNode | null {
    for (const child of node.children) {
      if (child.type === type) {
        return child;
      }
    }
    return null;
  }

  // 참조 추출
  private extractReferences(
    tree: Parser.Tree,
    filePath: string,
    content: string,
    language: string,
    symbols: Symbol[],
    repositoryFolderName: string
  ): Reference[] {
    const references: Reference[] = [];

    // 간단한 참조 추출 로직
    this.findReferences(tree.rootNode, content, filePath, symbols, references, repositoryFolderName);

    return references;
  }

  // 참조 찾기
  private findReferences(
    node: Parser.SyntaxNode,
    content: string,
    filePath: string,
    symbols: Symbol[],
    references: Reference[],
    repositoryFolderName: string
  ): void {
    // 식별자 노드에서 참조 찾기
    if (node.type === 'identifier') {
      const refName = this.getNodeText(node, content);

      // 해당 이름의 심볼이 있는지 확인
      const matchingSymbol = symbols.find(s => s.name === refName);

      if (matchingSymbol && matchingSymbol.id) {
        // 정의 위치와 같은 위치는 참조에서 제외
        const isDefinition = matchingSymbol.startLine === node.startPosition.row &&
                           matchingSymbol.startCharacter === node.startPosition.column;

        if (!isDefinition) {
          references.push({
            symbolId: matchingSymbol.id,
            filePath: filePath,
            startLine: node.startPosition.row,
            startCharacter: node.startPosition.column,
            endLine: node.endPosition.row,
            endCharacter: node.endPosition.column,
            repositoryFolderName
          });
        }
      }
    }

    // 자식 노드들도 순회
    for (const child of node.children) {
      this.findReferences(child, content, filePath, symbols, references, repositoryFolderName);
    }
  }

  // 노드의 텍스트 추출
  private getNodeText(node: Parser.SyntaxNode, content: string): string {
    return content.slice(node.startIndex, node.endIndex);
  }

  // AST를 JSON으로 변환 (디버깅용)
  getASTAsJson(filePath: string, content: string): any {
    const language = getLanguageFromExtension(filePath);
    
    if (!language || !this.parsers[language]) {
      return null;
    }

    const parser = this.parsers[language];
    const tree = parser.parse(content);
    
    return this.nodeToObject(tree.rootNode, content);
  }

  // 노드를 객체로 변환
  private nodeToObject(node: Parser.SyntaxNode, content: string): any {
    const obj: any = {
      type: node.type,
      startPosition: node.startPosition,
      endPosition: node.endPosition,
      text: this.getNodeText(node, content)
    };
    if (node.children && node.children.length > 0) {
      obj.children = node.children.map((child: Parser.SyntaxNode) => this.nodeToObject(child, content));
    }
    return obj;
  }
}

export default TreeSitterParser;
