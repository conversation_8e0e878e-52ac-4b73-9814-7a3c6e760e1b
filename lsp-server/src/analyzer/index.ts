// Tree-sitter 분석기 메인 모듈
import fs from 'fs';
import path from 'path';
import TreeSitterParser from './parser';
import database from '../database';
import { Symbol, Reference, AnalysisResponse } from '../shared/types';
import logger from '../shared/logger';

export class CodeAnalyzer {
  private parser: TreeSitterParser;
  private supportedExtensions = ['.js', '.jsx', '.ts', '.tsx', '.rb', '.ruby', '.py', '.pyw'];

  constructor() {
    this.parser = new TreeSitterParser();
  }

  // 디렉토리 분석
  async analyzeDirectory(directoryPath: string, repositoryFolderName: string, recursive: boolean = true): Promise<AnalysisResponse> {
    try {
      logger.info(`Starting analysis of directory: ${directoryPath} (repository: ${repositoryFolderName})`);
      
      const files = this.getSourceFiles(directoryPath, recursive);
      let totalSymbols = 0;
      let totalReferences = 0;

      // 각 파일을 순차적으로 분석
      for (const filePath of files) {
        try {
          const result = await this.analyzeFile(filePath, repositoryFolderName);
          totalSymbols += result.symbols.length;
          totalReferences += result.references.length;
        } catch (error) {
          logger.error(`Error analyzing file ${filePath}:`, error);
        }
      }

      logger.info(`Analysis completed. Found ${totalSymbols} symbols and ${totalReferences} references in ${files.length} files`);
      
      return {
        status: 'ok',
        message: `Analysis completed successfully`,
        symbolsFound: totalSymbols,
        referencesFound: totalReferences,
        repositoryFolderName
      };
    } catch (error) {
      logger.error('Directory analysis failed:', error);
      return {
        status: 'error',
        message: `Analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  // 단일 파일 분석
  async analyzeFile(filePath: string, repositoryFolderName: string): Promise<{ symbols: Symbol[], references: Reference[] }> {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      const result = await this.parser.analyzeFile(filePath, content, repositoryFolderName);

      // repositoryFolderName은 이미 parser에서 추가되므로 별도 처리 불필요
      
      // 데이터베이스에 저장
      await this.saveAnalysisResults(filePath, result, repositoryFolderName);
      
      logger.debug(`Analyzed file: ${filePath} (${result.symbols.length} symbols, ${result.references.length} references)`);
      
      return result;
    } catch (error) {
      logger.error(`Error analyzing file ${filePath}:`, error);
      throw error;
    }
  }

  // 분석 결과를 데이터베이스에 저장
  private async saveAnalysisResults(filePath: string, result: { symbols: Symbol[], references: Reference[] }, repositoryFolderName: string): Promise<void> {
    try {
      database.transaction(() => {
        // 기존 데이터 삭제
        database.clearSymbolsByFile(filePath, repositoryFolderName);
        
        // 새 심볼 저장
        const symbolIdMap = new Map<string, number>();
        
        result.symbols.forEach(symbol => {
          const symbolId = database.insertSymbol(symbol);
          symbolIdMap.set(symbol.name, symbolId);
        });

        // 참조 저장 (심볼 ID 매핑)
        result.references.forEach(reference => {
          // 심볼 ID가 있으면 그대로 사용, 없으면 이름으로 찾기
          let symbolId = reference.symbolId;
          
          if (!symbolId) {
            // 같은 파일 내에서 심볼 찾기
            const symbols = database.getSymbolsByFile(filePath, repositoryFolderName);
            const matchingSymbol = symbols.find(s => 
              s.name === reference.symbolId.toString() || // 임시로 이름이 symbolId에 들어갈 수 있음
              (s.startLine === reference.startLine && s.startCharacter === reference.startCharacter)
            );
            
            if (matchingSymbol && matchingSymbol.id) {
              symbolId = matchingSymbol.id;
            } else {
              // 다른 파일에서 심볼 찾기
              const allSymbols = database.getSymbolsByName(reference.symbolId.toString(), repositoryFolderName);
              if (allSymbols.length > 0 && allSymbols[0].id) {
                symbolId = allSymbols[0].id;
              }
            }
          }

          if (symbolId) {
            database.insertReference({
              ...reference,
              symbolId: symbolId
            });
          }
        });
      });
    } catch (error) {
      logger.error('Error saving analysis results:', error);
      throw error;
    }
  }

  // 디렉토리에서 소스 파일 찾기
  private getSourceFiles(directoryPath: string, recursive: boolean): string[] {
    const files: string[] = [];
    
    const processDirectory = (dir: string) => {
      try {
        const entries = fs.readdirSync(dir, { withFileTypes: true });
        
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          
          if (entry.isDirectory()) {
            // 숨김 폴더나 node_modules 등은 제외
            if (recursive && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
              processDirectory(fullPath);
            }
          } else if (entry.isFile()) {
            const ext = path.extname(entry.name).toLowerCase();
            if (this.supportedExtensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        }
      } catch (error) {
        logger.error(`Error reading directory ${dir}:`, error);
      }
    };

    processDirectory(directoryPath);
    return files;
  }

  // 특정 파일의 AST 가져오기 (디버깅용)
  async getFileAST(filePath: string): Promise<any> {
    try {
      const content = fs.readFileSync(filePath, 'utf8');
      return this.parser.getASTAsJson(filePath, content);
    } catch (error) {
      logger.error(`Error getting AST for file ${filePath}:`, error);
      throw error;
    }
  }

  // 디렉토리 분석 (repositoryFolderName 기반 재인덱싱 지원)
  async analyzeRepository(repositoryFolderName: string, repositoryPath: string, recursive: boolean = true): Promise<AnalysisResponse> {
    try {
      logger.info(`[Reindex] Clearing all symbols/references for repository: ${repositoryFolderName}`);
      database.clearSymbolsByRepository(repositoryFolderName);
      return await this.analyzeDirectory(repositoryPath, repositoryFolderName, recursive);
    } catch (error) {
      logger.error(`[Reindex] Failed for repository ${repositoryFolderName}:`, error);
      return {
        status: 'error',
        message: `Reindex failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }
}

export default CodeAnalyzer;
