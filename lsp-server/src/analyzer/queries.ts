// Tree-sitter 쿼리 정의
import { SymbolKind } from '../shared/types';

export interface QueryDefinition {
  language: string;
  patterns: {
    [key: string]: {
      query: string;
      symbolKind: SymbolKind;
    };
  };
}

// JavaScript/TypeScript 쿼리
export const javascriptQueries: QueryDefinition = {
  language: 'javascript',
  patterns: {
    functions: {
      query: `
        (function_declaration
          name: (identifier) @function.name) @function.definition

        (arrow_function
          parameter: (identifier) @function.name) @function.definition

        (method_definition
          name: (property_identifier) @function.name) @function.definition
      `,
      symbolKind: SymbolKind.FUNCTION
    },
    classes: {
      query: `
        (class_declaration
          name: (identifier) @class.name) @class.definition
      `,
      symbolKind: SymbolKind.CLASS
    },
    variables: {
      query: `
        (variable_declarator
          name: (identifier) @variable.name) @variable.definition

        (lexical_declaration
          (variable_declarator
            name: (identifier) @variable.name)) @variable.definition
      `,
      symbolKind: SymbolKind.VARIABLE
    },
    properties: {
      query: `
        (property_definition
          name: (property_identifier) @property.name) @property.definition

        (pair
          key: (property_identifier) @property.name) @property.definition
      `,
      symbolKind: SymbolKind.PROPERTY
    },
    references: {
      query: `
        (identifier) @reference
      `,
      symbolKind: SymbolKind.VARIABLE // 기본값, 실제로는 정의와 매칭해서 결정
    }
  }
};

// TypeScript 쿼리 (JavaScript 확장)
export const typescriptQueries: QueryDefinition = {
  language: 'typescript',
  patterns: {
    ...javascriptQueries.patterns,
    interfaces: {
      query: `
        (interface_declaration
          name: (type_identifier) @interface.name) @interface.definition
      `,
      symbolKind: SymbolKind.INTERFACE
    },
    types: {
      query: `
        (type_alias_declaration
          name: (type_identifier) @type.name) @type.definition
      `,
      symbolKind: SymbolKind.TYPE_PARAMETER
    },
    enums: {
      query: `
        (enum_declaration
          name: (identifier) @enum.name) @enum.definition
      `,
      symbolKind: SymbolKind.ENUM
    }
  }
};

// Ruby 쿼리
export const rubyQueries: QueryDefinition = {
  language: 'ruby',
  patterns: {
    classes: {
      query: `
        (class
          name: (constant) @class.name) @class.definition
      `,
      symbolKind: SymbolKind.CLASS
    },
    modules: {
      query: `
        (module
          name: (constant) @module.name) @module.definition
      `,
      symbolKind: SymbolKind.MODULE
    },
    methods: {
      query: `
        (method
          name: (identifier) @method.name) @method.definition
      `,
      symbolKind: SymbolKind.METHOD
    },
    variables: {
      query: `
        (assignment
          left: (identifier) @variable.name) @variable.definition

        (assignment
          left: (instance_variable) @variable.name) @variable.definition

        (assignment
          left: (class_variable) @variable.name) @variable.definition
      `,
      symbolKind: SymbolKind.VARIABLE
    },
    constants: {
      query: `
        (assignment
          left: (constant) @constant.name) @constant.definition
      `,
      symbolKind: SymbolKind.CONSTANT
    },
    references: {
      query: `
        (identifier) @reference
        (constant) @reference
        (instance_variable) @reference
        (class_variable) @reference
      `,
      symbolKind: SymbolKind.VARIABLE
    }
  }
};

// Python 쿼리
export const pythonQueries: QueryDefinition = {
  language: 'python',
  patterns: {
    functions: {
      query: `
        (function_definition
          name: (identifier) @function.name) @function.definition
      `,
      symbolKind: SymbolKind.FUNCTION
    },
    classes: {
      query: `
        (class_definition
          name: (identifier) @class.name) @class.definition
      `,
      symbolKind: SymbolKind.CLASS
    },
    variables: {
      query: `
        (assignment
          left: (identifier) @variable.name) @variable.definition

        (assignment
          left: (pattern_list (identifier) @variable.name)) @variable.definition
      `,
      symbolKind: SymbolKind.VARIABLE
    },
    imports: {
      query: `
        (import_statement
          name: (dotted_name (identifier) @import.name)) @import.definition

        (import_from_statement
          name: (dotted_name (identifier) @import.name)) @import.definition
      `,
      symbolKind: SymbolKind.MODULE
    },
    references: {
      query: `
        (identifier) @reference
      `,
      symbolKind: SymbolKind.VARIABLE
    }
  }
};

// 언어별 쿼리 맵
export const languageQueries: { [key: string]: QueryDefinition } = {
  javascript: javascriptQueries,
  typescript: typescriptQueries,
  ruby: rubyQueries,
  python: pythonQueries
};

// 파일 확장자로 언어 감지
export function getLanguageFromExtension(filePath: string): string | null {
  const ext = filePath.split('.').pop()?.toLowerCase();
  
  switch (ext) {
    case 'js':
    case 'jsx':
    case 'mjs':
      return 'javascript';
    case 'ts':
    case 'tsx':
      return 'typescript';
    case 'rb':
    case 'ruby':
      return 'ruby';
    case 'py':
    case 'pyw':
      return 'python';
    default:
      return null;
  }
}
