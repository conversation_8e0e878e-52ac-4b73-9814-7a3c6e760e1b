// TODO: Define shared types for the application.
// 공통 타입 정의
export interface Position {
  line: number;
  character: number;
}

export interface Range {
  start: Position;
  end: Position;
}

export interface Location {
  uri: string;
  range: Range;
}

export interface Symbol {
  id?: number;
  name: string;
  kind: SymbolKind;
  filePath: string;
  startLine: number;
  startCharacter: number;
  endLine: number;
  endCharacter: number;
  repositoryFolderName: string; // 어떤 리포지토리에 속하는지 식별
}

export interface Reference {
  id?: number;
  symbolId: number;
  filePath: string;
  startLine: number;
  startCharacter: number;
  endLine: number;
  endCharacter: number;
  repositoryFolderName: string; // 어떤 리포지토리에 속하는지 식별
}

export enum SymbolKind {
  FILE = 'file',
  MODULE = 'module',
  NAMESPACE = 'namespace',
  PACKAGE = 'package',
  CLASS = 'class',
  METHOD = 'method',
  PROPERTY = 'property',
  FIELD = 'field',
  CONSTRUCTOR = 'constructor',
  ENUM = 'enum',
  INTERFACE = 'interface',
  FUNCTION = 'function',
  VARIABLE = 'variable',
  CONSTANT = 'constant',
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  ARRAY = 'array',
  OBJECT = 'object',
  KEY = 'key',
  NULL = 'null',
  ENUM_MEMBER = 'enumMember',
  STRUCT = 'struct',
  EVENT = 'event',
  OPERATOR = 'operator',
  TYPE_PARAMETER = 'typeParameter'
}

export interface AnalysisRequest {
  path: string;
  repositoryFolderName: string;  // 필수: 어떤 리포지토리에 속하는지 식별
  recursive?: boolean;
}

export interface RepositoryAnalysisRequest {
  repositoryFolderName: string;  // 명확히 폴더명임을 표시 (예: "codebase-sqlite_13")
  recursive?: boolean;
}

export interface AnalysisResponse {
  status: 'ok' | 'error';
  message: string;
  symbolsFound?: number;
  referencesFound?: number;
  repositoryFolderName?: string;  // 응답에서도 명확히 표시
  repositoryPath?: string;
}

export interface DefinitionRequest {
  name: string;
  filePath?: string;
  repositoryFolderName: string;  // 필수: 어떤 리포지토리에 속하는지 식별
}

export interface ReferencesRequest {
  name?: string;
  symbolId?: number;
  repositoryFolderName: string;  // 필수: 어떤 리포지토리에 속하는지 식별
}
