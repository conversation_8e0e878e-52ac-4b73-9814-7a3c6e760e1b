// src/main.ts

// 환경 변수 로드
import dotenv from 'dotenv';
dotenv.config();

// 메인 진입점 - LSP 서버와 REST API 서버를 함께 실행
import APIServer from './api/server';
import LSPServer from './lsp/server';
import { LSPClientWrapper } from './lsp/client';
import logger, { LogLevel } from './shared/logger';

// 환경 변수 설정
const PORT = process.env.PORT ? parseInt(process.env.PORT) : 7107;
const LOG_LEVEL = process.env.LOG_LEVEL || 'INFO';
const LSP_MODE = process.env.LSP_MODE === 'true';
const NODE_ENV = process.env.NODE_ENV || 'development';
const USE_LSP_CLIENT = process.env.USE_LSP_CLIENT === 'true';

// 로그 레벨 설정
logger.setLogLevel(LogLevel[LOG_LEVEL as keyof typeof LogLevel] || LogLevel.INFO);

let lspClientWrapper: LSPClientWrapper | null = null;

async function main() {
  try {
    logger.info('Starting LSP & Tree-sitter Server...');
    
    if (LSP_MODE) {
      // LSP 프로토콜 서버 모드 (독립 실행)
      logger.info('Starting in LSP Protocol mode...');
      const lspServer = new LSPServer(true);
      lspServer.start();
      return;
    }

    // REST API 서버 모드
    let lspServerInstance: LSPServer | LSPClientWrapper;

    if (USE_LSP_CLIENT) {
      // LSP 클라이언트 래퍼 사용 (내부에서 LSP 서버 프로세스 시작)
      logger.info('Initializing LSP client wrapper...');
      lspClientWrapper = new LSPClientWrapper();
      lspServerInstance = lspClientWrapper;
      
      // LSP 초기화 대기
      await new Promise(resolve => setTimeout(resolve, 3000));
      logger.info('LSP client wrapper initialized');
    } else {
      // 직접 LSP 서버 인스턴스 사용 (라이브러리 모드)
      logger.info('Using direct LSP server instance...');
      lspServerInstance = new LSPServer(false);
    }
    
    // REST API 서버 시작
    const apiServer = new APIServer(PORT);
    
    // LSP 서버 인스턴스를 API 서버에 주입
    apiServer.setLSPServer(lspServerInstance);
    
    await apiServer.start();
    
    logger.info(`Server is running on port ${PORT}`);
    logger.info(`Environment: ${NODE_ENV}`);
    logger.info(`LSP Client Mode: ${USE_LSP_CLIENT ? 'Enabled' : 'Disabled'}`);
    logger.info('Available endpoints:');
    logger.info('  GET  /api/v1/health');
    logger.info('  POST /api/v1/analysis/scan');
    logger.info('  POST /api/v1/analysis/scan-repository');
    logger.info('  GET  /api/v1/repositories');
    logger.info('  GET  /api/v1/repositories/:folderName');
    logger.info('  GET  /api/v1/symbols/definition');
    logger.info('  GET  /api/v1/symbols/references');
    logger.info('  GET  /api/v1/files/:path/symbols');
    logger.info('  GET  /api/v1/files/:path/ast');
    logger.info('  GET  /api/v1/stats');
    
    if (USE_LSP_CLIENT) {
      logger.info('  POST /api/v1/lsp/definition');
      logger.info('  POST /api/v1/lsp/references');
      logger.info('  POST /api/v1/lsp/completion');
    }

    // 종료 신호 처리
    process.on('SIGINT', async () => {
      logger.info('Received SIGINT, shutting down gracefully...');
      await shutdown(apiServer);
    });

    process.on('SIGTERM', async () => {
      logger.info('Received SIGTERM, shutting down gracefully...');
      await shutdown(apiServer);
    });

    // 예외 처리
    process.on('uncaughtException', async (error) => {
      logger.error('Uncaught exception:', error);
      await shutdown(apiServer);
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      logger.error('Unhandled rejection at:', promise, 'reason:', reason);
      await shutdown(apiServer);
      process.exit(1);
    });

  } catch (error) {
    logger.error('Failed to start server:', error);
    if (lspClientWrapper) {
      await lspClientWrapper.dispose();
    }
    process.exit(1);
  }
}

async function shutdown(apiServer: APIServer) {
  try {
    apiServer.stop();
    
    if (lspClientWrapper) {
      await lspClientWrapper.dispose();
    }
    
    logger.info('Server shutdown completed');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown:', error);
    process.exit(1);
  }
}

// 메인 함수 실행
main().catch(async error => {
  logger.error('Fatal error:', error);
  if (lspClientWrapper) {
    await lspClientWrapper.dispose();
  }
  process.exit(1);
});