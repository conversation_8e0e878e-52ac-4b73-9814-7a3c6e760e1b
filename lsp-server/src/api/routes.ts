// REST API 라우터
import { Router } from 'express';
import path from 'path';
import fs from 'fs';
import database from '../database';
import CodeAnalyzer from '../analyzer';
import logger from '../shared/logger';
import { AnalysisRequest, RepositoryAnalysisRequest, DefinitionRequest, ReferencesRequest, Reference } from '../shared/types';
import { LSPClientWrapper } from '../lsp/client';

declare global {
  // eslint-disable-next-line no-var
  var lspClientWrapper: LSPClientWrapper | undefined;
}

const router = Router();
const analyzer = new CodeAnalyzer();

// 환경변수에서 repositories 경로 가져오기
const REPOSITORIES_PATH = process.env.REPOSITORIES_PATH || './repositories';

// 헬스 체크
router.get('/health', (req, res) => {
  res.json({ status: 'ok', message: 'LSP server is running' });
});

// 코드 분석 트리거
router.post('/analysis/scan', async (req, res) => {
  try {
    const { path, repositoryFolderName, recursive = true }: AnalysisRequest = req.body;
    
    if (!path) {
      return res.status(400).json({ status: 'error', message: 'Path is required' });
    }

    if (!repositoryFolderName) {
      return res.status(400).json({ status: 'error', message: 'repositoryFolderName is required' });
    }

    logger.info(`Analysis request received for: ${path} (repository: ${repositoryFolderName})`);
    const result = await analyzer.analyzeDirectory(path, repositoryFolderName, recursive);
    
    res.json(result);
  } catch (error) {
    logger.error('Analysis request failed:', error);
    res.status(500).json({ 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// 리포지토리 폴더명을 통한 코드 분석
router.post('/analysis/scan-repository', async (req, res) => {
  try {
    const { repositoryFolderName, recursive = true }: RepositoryAnalysisRequest = req.body;
    
    if (!repositoryFolderName) {
      return res.status(400).json({ status: 'error', message: 'Repository folder name is required' });
    }

    // 리포지토리 경로 구성
    const repositoryPath = path.join(REPOSITORIES_PATH, repositoryFolderName);
    
    // 리포지토리 존재 확인
    if (!fs.existsSync(repositoryPath)) {
      return res.status(404).json({ 
        status: 'error', 
        message: `Repository not found: ${repositoryFolderName}` 
      });
    }

    // 디렉토리인지 확인
    const stat = fs.statSync(repositoryPath);
    if (!stat.isDirectory()) {
      return res.status(400).json({ 
        status: 'error', 
        message: `Repository path is not a directory: ${repositoryFolderName}` 
      });
    }

    logger.info(`Repository analysis request received for: ${repositoryFolderName} (${repositoryPath})`);
    // 재인덱싱: 기존 심볼/참조 모두 삭제 후 분석
    const result = await analyzer.analyzeRepository(repositoryFolderName, repositoryPath, recursive);
    res.json({
      ...result,
      repositoryFolderName,
      repositoryPath
    });
  } catch (error) {
    logger.error('Repository analysis request failed:', error);
    res.status(500).json({ 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// 심볼 정의 조회
router.get('/symbols/definition', (req, res) => {
  try {
    const { name, filePath, repositoryFolderName }: DefinitionRequest = req.query as any;
    
    if (!name) {
      return res.status(400).json({ status: 'error', message: 'Symbol name is required' });
    }

    if (!repositoryFolderName) {
      return res.status(400).json({ status: 'error', message: 'Repository folder name is required' });
    }

    logger.info(`Symbol definition request for: ${name} (repository: ${repositoryFolderName})`);
    let symbols = database.getSymbolsByName(name, repositoryFolderName);

    if (filePath) {
      // 특정 파일 내의 심볼로 필터링
      symbols = symbols.filter(symbol => symbol.filePath === filePath);
    }

    res.json({
      status: 'ok',
      symbols
    });
  } catch (error) {
    logger.error('Symbol definition request failed:', error);
    res.status(500).json({ 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// 심볼 참조 조회
router.get('/symbols/references', (req, res) => {
  try {
    const { name, symbolId, repositoryFolderName }: ReferencesRequest = req.query as any;
    
    if ((!name && !symbolId) || (name && symbolId)) {
      return res.status(400).json({ 
        status: 'error', 
        message: 'Either name or symbolId must be provided (but not both)' 
      });
    }

    if (!repositoryFolderName) {
      return res.status(400).json({ status: 'error', message: 'Repository folder name is required' });
    }

    logger.info(`Symbol references request for: ${name || symbolId} (repository: ${repositoryFolderName})`);
    
    let references: Reference[] = [];
    if (symbolId) {
      references = database.getReferencesBySymbolId(parseInt(symbolId.toString()), repositoryFolderName);
    } else if (name) {
      references = database.getReferencesByName(name, repositoryFolderName);
    }

    res.json({
      status: 'ok',
      references
    });
  } catch (error) {
    logger.error('Symbol references request failed:', error);
    res.status(500).json({ 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// 파일별 심볼 조회
router.get('/files/symbols', (req, res) => {
  try {
    const filePath = req.query.path as string;
    const repositoryFolderName = req.query.repositoryFolderName as string;
    
    if (!filePath) {
      return res.status(400).json({ status: 'error', message: 'File path is required' });
    }

    if (!repositoryFolderName) {
      return res.status(400).json({ status: 'error', message: 'Repository folder name is required' });
    }

    logger.info(`File symbols request for: ${filePath} (repository: ${repositoryFolderName})`);
    const symbols = database.getSymbolsByFile(filePath, repositoryFolderName);
    
    res.json({
      status: 'ok',
      symbols
    });
  } catch (error) {
    logger.error('File symbols request failed:', error);
    res.status(500).json({ 
      status: 'error', 
      message: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

// 파일 AST 조회 (디버깅용)
router.get('/files/:path/ast', async (req, res) => {
  try {
    const filePath = decodeURIComponent(req.params.path);
    const ast = await analyzer.getFileAST(filePath);
    
    if (!ast) {
      return res.status(404).json({ error: 'File not found or unsupported language' });
    }

    res.json({ ast });
  } catch (error) {
    logger.error('AST request failed:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 통계 조회
router.get('/stats', (req, res) => {
  try {
    // 실제 DB에서 통계 쿼리 (타입 단언 추가)
    const totalSymbols = (database.getDb().prepare('SELECT COUNT(*) as cnt FROM symbols').get() as { cnt: number }).cnt;
    const totalReferences = (database.getDb().prepare('SELECT COUNT(*) as cnt FROM symbol_references').get() as { cnt: number }).cnt;
    const filesCovered = (database.getDb().prepare('SELECT COUNT(DISTINCT file_path) as cnt FROM symbols').get() as { cnt: number }).cnt;
    const symbolsByKindRows = database.getDb().prepare('SELECT kind, COUNT(*) as cnt FROM symbols GROUP BY kind').all() as { kind: string, cnt: number }[];
    const symbolsByKind: Record<string, number> = {};
    for (const row of symbolsByKindRows) {
      symbolsByKind[row.kind] = row.cnt;
    }
    const stats = {
      totalSymbols,
      totalReferences,
      symbolsByKind,
      filesCovered
    };
    res.json(stats);
  } catch (error) {
    logger.error('Stats request failed:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 리포지토리 목록 조회
router.get('/repositories', (req, res) => {
  try {
    if (!fs.existsSync(REPOSITORIES_PATH)) {
      return res.status(404).json({ 
        status: 'error', 
        message: 'Repositories directory not found' 
      });
    }

    const repositories = fs.readdirSync(REPOSITORIES_PATH, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => {
        const repositoryPath = path.join(REPOSITORIES_PATH, dirent.name);
        const stat = fs.statSync(repositoryPath);
        
        return {
          folderName: dirent.name,
          displayName: dirent.name.replace(/_\d+$/, ''), // 뒤의 _숫자 제거하여 표시용 이름 생성
          path: repositoryPath,
          lastModified: stat.mtime.toISOString(),
          size: stat.size
        };
      });

    res.json(repositories);
  } catch (error) {
    logger.error('Repositories list request failed:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 특정 리포지토리 정보 조회 (폴더명 기반)
router.get('/repositories/:folderName', (req, res) => {
  try {
    const repositoryFolderName = req.params.folderName;
    const repositoryPath = path.join(REPOSITORIES_PATH, repositoryFolderName);

    if (!fs.existsSync(repositoryPath)) {
      return res.status(404).json({
        status: 'error',
        message: `Repository not found: ${repositoryFolderName}`
      });
    }

    const stat = fs.statSync(repositoryPath);
    if (!stat.isDirectory()) {
      return res.status(400).json({
        status: 'error',
        message: `Repository path is not a directory: ${repositoryFolderName}`
      });
    }

    // 리포지토리 내 파일 개수 계산 (간단한 통계)
    const fileCount = countFiles(repositoryPath);
    const symbolCount = database.getSymbolsByFilePattern('%', repositoryFolderName).length; // 모든 파일 매칭, repositoryFolderName으로 필터링

    res.json({
      folderName: repositoryFolderName,
      displayName: repositoryFolderName.replace(/_\d+$/, ''), // 뒤의 _숫자 제거
      path: repositoryPath,
      lastModified: stat.mtime.toISOString(),
      size: stat.size,
      fileCount,
      symbolCount
    });
  } catch (error) {
    logger.error('Repository info request failed:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// 리포지토리 삭제 (폴더명 기반)
router.delete('/repositories/:folderName', (req, res) => {
  try {
    const repositoryFolderName = req.params.folderName;

    logger.info(`Repository deletion request received for: ${repositoryFolderName}`);

    // 1. 데이터베이스에서 관련 심볼과 참조 삭제
    try {
      const deletedSymbols = database.deleteSymbolsByRepository(repositoryFolderName);
      const deletedReferences = database.deleteReferencesByRepository(repositoryFolderName);

      logger.info(`Deleted ${deletedSymbols} symbols and ${deletedReferences} references for repository: ${repositoryFolderName}`);
    } catch (dbError) {
      logger.error(`Failed to delete database records for repository ${repositoryFolderName}:`, dbError);
      // 데이터베이스 삭제 실패해도 계속 진행
    }

    res.json({
      status: 'ok',
      message: `Repository '${repositoryFolderName}' deleted successfully from LSP server`,
      repositoryFolderName,
      deletedSymbols: true,
      deletedReferences: true
    });

  } catch (error) {
    logger.error('Repository deletion request failed:', error);
    res.status(500).json({
      status: 'error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// 파일 개수 계산 헬퍼 함수
function countFiles(dir: string): number {
  let count = 0;
  try {
    const files = fs.readdirSync(dir, { withFileTypes: true });
    for (const file of files) {
      if (file.isFile()) {
        count++;
      } else if (file.isDirectory()) {
        count += countFiles(path.join(dir, file.name));
      }
    }
  } catch (error) {
    logger.warn(`Error counting files in ${dir}:`, error);
  }
  return count;
}

// LSP 프로토콜 기반 APIs (LSPClientWrapper에서만 사용 가능)
router.post('/lsp/definition', async (req, res) => {
  try {
    const { filePath, line, character, fileContent } = req.body;
    if (!filePath || line === undefined || character === undefined) {
      return res.status(400).json({ status: 'error', message: 'filePath, line, and character are required' });
    }
    if (!(globalThis.lspClientWrapper)) {
      return res.status(400).json({ status: 'error', message: 'LSP client mode not enabled' });
    }
    const locations = await (globalThis.lspClientWrapper as LSPClientWrapper).getDefinition(filePath, line, character, fileContent);
    res.json(locations.map((loc: any) => ({
      filePath: loc.uri.replace('file://', ''),
      position: loc.range
    })));
  } catch (error) {
    logger.error('LSP definition request failed:', error);
    res.status(500).json({ status: 'error', message: error instanceof Error ? error.message : String(error) });
  }
});

router.post('/lsp/references', async (req, res) => {
  try {
    const { filePath, line, character, includeDeclaration = true, fileContent } = req.body;
    if (!filePath || line === undefined || character === undefined) {
      return res.status(400).json({ status: 'error', message: 'filePath, line, and character are required' });
    }
    if (!(globalThis.lspClientWrapper)) {
      return res.status(400).json({ status: 'error', message: 'LSP client mode not enabled' });
    }
    const locations = await (globalThis.lspClientWrapper as LSPClientWrapper).getReferences(filePath, line, character, includeDeclaration, fileContent);
    res.json(locations.map((loc: any) => ({
      filePath: loc.uri.replace('file://', ''),
      position: loc.range
    })));
  } catch (error) {
    logger.error('LSP references request failed:', error);
    res.status(500).json({ status: 'error', message: error instanceof Error ? error.message : String(error) });
  }
});

router.post('/lsp/completion', async (req, res) => {
  try {
    const { filePath, line, character, fileContent } = req.body;
    if (!filePath || line === undefined || character === undefined) {
      return res.status(400).json({ status: 'error', message: 'filePath, line, and character are required' });
    }
    if (!(globalThis.lspClientWrapper)) {
      return res.status(400).json({ status: 'error', message: 'LSP client mode not enabled' });
    }
    const items = await (globalThis.lspClientWrapper as LSPClientWrapper).getCompletions(filePath, line, character, fileContent);
    res.json(items);
  } catch (error) {
    logger.error('LSP completion request failed:', error);
    res.status(500).json({ status: 'error', message: error instanceof Error ? error.message : String(error) });
  }
});

export default router;
