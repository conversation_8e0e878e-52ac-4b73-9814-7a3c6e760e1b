// Express 기반 REST API 서버
import express from 'express';
import cors from 'cors';
import apiRoutes from './routes';
import logger from '../shared/logger';

export class APIServer {
  private app: express.Application;
  private port: number;
  private lspServerInstance: any;

  constructor(port: number = 7107) {
    this.app = express();
    this.port = port;
    this.setupMiddleware();
    this.setupRoutes();
  }

  // 미들웨어 설정
  private setupMiddleware(): void {
    // CORS 설정
    this.app.use(cors({
      origin: ['http://localhost:7103', 'http://localhost:7107', 'http://gserver.parrot-mine.ts.net:7103'],
      credentials: true
    }));

    // JSON 파싱
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // 로깅 미들웨어
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path} - ${req.ip}`);
      next();
    });
  }

  // 라우터 설정
  private setupRoutes(): void {
    // API 라우터
    this.app.use('/api/v1', apiRoutes);

    // 루트 경로
    this.app.get('/', (req, res) => {
      res.json({
        message: 'LSP & Tree-sitter Server',
        version: '1.0.0',
        endpoints: {
          health: '/api/v1/health',
          analysis: '/api/v1/analysis/scan',
          definition: '/api/v1/symbols/definition',
          references: '/api/v1/symbols/references',
          fileSymbols: '/api/v1/files/:path/symbols',
          fileAST: '/api/v1/files/:path/ast',
          stats: '/api/v1/stats'
        }
      });
    });

    // 404 핸들러
    this.app.use('*', (req, res) => {
      res.status(404).json({ error: 'Endpoint not found' });
    });

    // 에러 핸들러
    this.app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      logger.error('Express error:', err);
      res.status(500).json({ error: 'Internal server error' });
    });
  }

  // 서버 시작
  public start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.app.listen(this.port, () => {
          logger.info(`REST API server started on port ${this.port}`);
          resolve();
        });
      } catch (error) {
        logger.error('Failed to start REST API server:', error);
        reject(error);
      }
    });
  }

  // 서버 중지
  public stop(): void {
    logger.info('REST API server stopped');
  }

  // Express 앱 인스턴스 반환 (테스트용)
  public getApp(): express.Application {
    return this.app;
  }

  setLSPServer(lspServerInstance: any) {
    this.lspServerInstance = lspServerInstance;
  }
}

export default APIServer;
