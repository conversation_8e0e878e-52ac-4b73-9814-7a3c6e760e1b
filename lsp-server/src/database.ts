import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import { Symbol, Reference } from './shared/types';
import logger from './shared/logger';

// 데이터베이스 파일 경로 설정
const dbPath = path.join(process.cwd(), 'data_lsp', 'codebase.sqlite');
const dbDir = path.dirname(dbPath);

if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
    logger.info(`Created database directory: ${dbDir}`);
}

// 기존 테이블 삭제(초기화)
const db = new Database(dbPath, { verbose: console.log });
db.exec(`
DROP TABLE IF EXISTS symbol_references;
DROP TABLE IF EXISTS symbols;
DROP TABLE IF EXISTS files;
DROP TABLE IF EXISTS repositories;
`);

// 새 스키마 (repositoryFolderName 컬럼 추가)
const createRepositoriesTable = `
    CREATE TABLE IF NOT EXISTS repositories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        repository_folder_name TEXT NOT NULL UNIQUE,
        path TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
`;

const createFilesTable = `
    CREATE TABLE IF NOT EXISTS files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        repository_folder_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        content TEXT,
        last_analyzed DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
`;

const createSymbolsTable = `
    CREATE TABLE IF NOT EXISTS symbols (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        repository_folder_name TEXT NOT NULL,
        file_id INTEGER,
        name TEXT NOT NULL,
        kind TEXT NOT NULL,
        file_path TEXT NOT NULL,
        start_line INTEGER NOT NULL,
        start_character INTEGER NOT NULL,
        end_line INTEGER NOT NULL,
        end_character INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    );
`;

const createSymbolReferencesTable = `
    CREATE TABLE IF NOT EXISTS symbol_references (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        repository_folder_name TEXT NOT NULL,
        symbol_id INTEGER NOT NULL,
        file_path TEXT NOT NULL,
        start_line INTEGER NOT NULL,
        start_character INTEGER NOT NULL,
        end_line INTEGER NOT NULL,
        end_character INTEGER NOT NULL
    );
`;

db.exec(createRepositoriesTable);
db.exec(createFilesTable);
db.exec(createSymbolsTable);
db.exec(createSymbolReferencesTable);

// Prepared statements
const insertSymbol = db.prepare(`
  INSERT OR REPLACE INTO symbols (name, kind, file_path, start_line, start_character, end_line, end_character, repository_folder_name)
  VALUES (?, ?, ?, ?, ?, ?, ?, ?)
`);

const insertReference = db.prepare(`
  INSERT OR REPLACE INTO symbol_references (symbol_id, file_path, start_line, start_character, end_line, end_character, repository_folder_name)
  VALUES (?, ?, ?, ?, ?, ?, ?)
`);

const selectSymbolsByName = db.prepare(`
  SELECT * FROM symbols WHERE name = ? AND repository_folder_name = ?
`);

const selectSymbolsByFile = db.prepare(`
  SELECT * FROM symbols WHERE file_path = ? AND repository_folder_name = ?
`);

const selectReferencesById = db.prepare(`
  SELECT * FROM symbol_references WHERE symbol_id = ? AND repository_folder_name = ?
`);

const selectReferencesByName = db.prepare(`
  SELECT r.* FROM symbol_references r
  JOIN symbols s ON r.symbol_id = s.id
  WHERE s.name = ? AND s.repository_folder_name = ?
`);

const clearSymbolsByFile = db.prepare(`
  DELETE FROM symbols WHERE file_path = ? AND repository_folder_name = ?
`);

// 데이터베이스 작업 클래스
class DatabaseManager {
  constructor() {
    // 이미 전역 db 인스턴스가 초기화되어 있으므로 별도 작업 불필요
  }

  public getDb(): Database.Database {
    return db;
  }

  // 심볼 저장
  insertSymbol(symbol: Symbol): number {
    const result = insertSymbol.run(
      symbol.name,
      symbol.kind,
      symbol.filePath,
      symbol.startLine,
      symbol.startCharacter,
      symbol.endLine,
      symbol.endCharacter,
      symbol.repositoryFolderName
    );
    return result.lastInsertRowid as number;
  }

  // 참조 저장
  insertReference(reference: Reference): number {
    const result = insertReference.run(
      reference.symbolId,
      reference.filePath,
      reference.startLine,
      reference.startCharacter,
      reference.endLine,
      reference.endCharacter,
      reference.repositoryFolderName
    );
    return result.lastInsertRowid as number;
  }

  // 이름으로 심볼 조회
  getSymbolsByName(name: string, repositoryFolderName: string): Symbol[] {
    const rows = selectSymbolsByName.all(name, repositoryFolderName);
    return rows.map(this.mapRowToSymbol);
  }

  // 파일별 심볼 조회
  getSymbolsByFile(filePath: string, repositoryFolderName: string): Symbol[] {
    const rows = selectSymbolsByFile.all(filePath, repositoryFolderName);
    return rows.map(this.mapRowToSymbol);
  }

  // 파일 경로 패턴으로 심볼 조회 (리포지토리 스캔 시 사용)
  getSymbolsByFilePattern(filePathPattern: string, repositoryFolderName: string): Symbol[] {
    const query = db.prepare(`
      SELECT * FROM symbols 
      WHERE file_path LIKE ? AND repository_folder_name = ?
      ORDER BY file_path, start_line, start_character
    `);
    const rows = query.all(filePathPattern, repositoryFolderName);
    return rows.map(this.mapRowToSymbol);
  }

  // 심볼 ID로 참조 조회
  getReferencesBySymbolId(symbolId: number, repositoryFolderName: string): Reference[] {
    const rows = selectReferencesById.all(symbolId, repositoryFolderName);
    return rows.map(this.mapRowToReference);
  }

  // 심볼 이름으로 참조 조회
  getReferencesByName(name: string, repositoryFolderName: string): Reference[] {
    const rows = selectReferencesByName.all(name, repositoryFolderName);
    return rows.map(this.mapRowToReference);
  }

  // 파일의 모든 심볼 삭제 (재분석 시 사용)
  clearSymbolsByFile(filePath: string, repositoryFolderName: string): void {
    clearSymbolsByFile.run(filePath, repositoryFolderName);
  }

  // 리포지토리 내 모든 심볼/참조 삭제 (repositoryFolderName 기준)
  clearSymbolsByRepository(repositoryFolderName: string): void {
    // 이제 파일 경로 패턴이 아닌 repository_folder_name 컬럼으로 삭제
    db.prepare('DELETE FROM symbols WHERE repository_folder_name = ?').run(repositoryFolderName);
    db.prepare('DELETE FROM symbol_references WHERE repository_folder_name = ?').run(repositoryFolderName);
  }

  // 리포지토리 삭제 (삭제된 개수 반환)
  deleteSymbolsByRepository(repositoryFolderName: string): number {
    const result = db.prepare('DELETE FROM symbols WHERE repository_folder_name = ?').run(repositoryFolderName);
    return result.changes;
  }

  deleteReferencesByRepository(repositoryFolderName: string): number {
    const result = db.prepare('DELETE FROM symbol_references WHERE repository_folder_name = ?').run(repositoryFolderName);
    return result.changes;
  }

  // 트랜잭션 실행
  transaction<T>(fn: () => T): T {
    return db.transaction(fn)();
  }

  // 데이터베이스 종료
  close(): void {
    db.close();
  }

  // 행을 Symbol 객체로 변환
  private mapRowToSymbol(row: any): Symbol {
    return {
      id: row.id,
      name: row.name,
      kind: row.kind,
      filePath: row.file_path,
      startLine: row.start_line,
      startCharacter: row.start_character,
      endLine: row.end_line,
      endCharacter: row.end_character,
      repositoryFolderName: row.repository_folder_name
    };
  }

  // 행을 Reference 객체로 변환
  private mapRowToReference(row: any): Reference {
    return {
      id: row.id,
      symbolId: row.symbol_id,
      filePath: row.file_path,
      startLine: row.start_line,
      startCharacter: row.start_character,
      endLine: row.end_line,
      endCharacter: row.end_character,
      repositoryFolderName: row.repository_folder_name
    };
  }
}

logger.info(`Database initialized at: ${dbPath}`);

export default new DatabaseManager();
export { DatabaseManager };
