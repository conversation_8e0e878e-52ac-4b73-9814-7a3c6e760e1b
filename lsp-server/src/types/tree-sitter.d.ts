// Tree-sitter 관련 타입 선언
declare module 'tree-sitter-typescript' {
  const typescript: any;
  const tsx: any;
  export { typescript, tsx };
}

declare module 'tree-sitter-ruby' {
  const ruby: any;
  export = ruby;
}

declare module 'tree-sitter-python' {
  const python: any;
  export = python;
}

declare module 'tree-sitter-javascript' {
  const javascript: any;
  export = javascript;
}

declare module 'tree-sitter-typescript';
declare module 'tree-sitter-ruby';
