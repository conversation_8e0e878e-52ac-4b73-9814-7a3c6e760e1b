// src/lsp/standalone.ts - LSP 서버를 독립 프로세스로 실행

import LSPServer from './server';
import logger from '../shared/logger';

// 이 파일은 LSP 서버를 독립 프로세스로 실행할 때 사용됩니다.
// 내부 LSP 클라이언트에서 spawn으로 이 스크립트를 실행합니다.

async function main() {
  try {
    logger.info('Starting standalone LSP server process...');
    
    // LSP 서버를 standalone 모드로 생성
    const lspServer = new LSPServer(true);
    
    // LSP 프로토콜 서버 시작 (stdio 통신)
    lspServer.start();
    
    logger.info('Standalone LSP server started');
  } catch (error) {
    logger.error('Failed to start standalone LSP server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  logger.info('Standalone LSP server shutting down (SIGINT)...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Standalone LSP server shutting down (SIGTERM)...');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  logger.error('Uncaught exception in standalone LSP server:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled rejection in standalone LSP server:', promise, 'reason:', reason);
  process.exit(1);
});

main();