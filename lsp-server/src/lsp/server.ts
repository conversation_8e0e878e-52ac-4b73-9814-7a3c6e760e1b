// src/lsp/server.ts

// LSP 서버 구현
import {
  createConnection,
  TextDocuments,
  Diagnostic,
  DiagnosticSeverity,
  ProposedFeatures,
  InitializeParams,
  DidChangeConfigurationNotification,
  CompletionItem,
  CompletionItemKind,
  TextDocumentPositionParams,
  TextDocumentSyncKind,
  InitializeResult,
  LocationLink,
  Location,
  Range,
  Position,
  DocumentSymbol,
  SymbolKind
} from 'vscode-languageserver/node';

import { TextDocument } from 'vscode-languageserver-textdocument';
import database from '../database';
import logger from '../shared/logger';
import { getLanguageFromExtension } from '../analyzer/queries';

export class LSPServer {
  private connection?: any;
  private documents?: TextDocuments<TextDocument>;
  private hasConfigurationCapability = false;
  private hasWorkspaceFolderCapability = false;
  private hasDiagnosticRelatedInformationCapability = false;
  private isStandaloneMode: boolean;

  constructor(standaloneMode: boolean = false) {
    this.isStandaloneMode = standaloneMode;
    
    if (this.isStandaloneMode) {
      // Standalone 모드: LSP 프로토콜 서버로 동작
      this.connection = createConnection(ProposedFeatures.all);
      this.documents = new TextDocuments(TextDocument);
      this.setupEventHandlers();
      logger.info('LSP Server initialized in standalone mode');
    } else {
      // Library 모드: REST API에서 호출 가능한 메서드들만 제공
      logger.info('LSP Server initialized in library mode');
    }
  }

  // REST API용 메서드들 - LSP 기능을 직접 호출 가능
  async getDefinitionByName(symbolName: string, filePath?: string, repositoryFolderName?: string): Promise<Array<{uri: string, range: any}>> {
    try {
      if (!repositoryFolderName) {
        logger.warn('getDefinitionByName: repositoryFolderName not provided, using unknown_repository');
        repositoryFolderName = 'unknown_repository';
      }

      const symbols = database.getSymbolsByName(symbolName, repositoryFolderName);

      let filteredSymbols = symbols;
      if (filePath) {
        filteredSymbols = symbols.filter(s => s.filePath === filePath);
      }

      return filteredSymbols.map(symbol => ({
        uri: this.pathToUri(symbol.filePath),
        range: {
          start: { line: symbol.startLine, character: symbol.startCharacter },
          end: { line: symbol.endLine, character: symbol.endCharacter }
        }
      }));
    } catch (error) {
      logger.error('getDefinitionByName failed:', error);
      return [];
    }
  }

  async getReferencesByName(symbolName: string, includeDeclaration: boolean = true, repositoryFolderName?: string): Promise<Array<{uri: string, range: any}>> {
    try {
      if (!repositoryFolderName) {
        logger.warn('getReferencesByName: repositoryFolderName not provided, using unknown_repository');
        repositoryFolderName = 'unknown_repository';
      }

      const references = database.getReferencesByName(symbolName, repositoryFolderName);

      const locations = references.map(ref => ({
        uri: this.pathToUri(ref.filePath),
        range: {
          start: { line: ref.startLine, character: ref.startCharacter },
          end: { line: ref.endLine, character: ref.endCharacter }
        }
      }));

      // 정의도 포함
      if (includeDeclaration) {
        const symbols = database.getSymbolsByName(symbolName, repositoryFolderName);
        symbols.forEach(symbol => {
          locations.push({
            uri: this.pathToUri(symbol.filePath),
            range: {
              start: { line: symbol.startLine, character: symbol.startCharacter },
              end: { line: symbol.endLine, character: symbol.endCharacter }
            }
          });
        });
      }

      return locations;
    } catch (error) {
      logger.error('getReferencesByName failed:', error);
      return [];
    }
  }

  async getFileSymbols(filePath: string, repositoryFolderName?: string): Promise<DocumentSymbol[]> {
    try {
      if (!repositoryFolderName) {
        logger.warn('getFileSymbols: repositoryFolderName not provided, using unknown_repository');
        repositoryFolderName = 'unknown_repository';
      }

      const symbols = database.getSymbolsByFile(filePath, repositoryFolderName);

      return symbols.map(symbol => {
        const range = {
          start: { line: symbol.startLine, character: symbol.startCharacter },
          end: { line: symbol.endLine, character: symbol.endCharacter }
        };

        return {
          name: symbol.name,
          kind: this.symbolKindToLSPSymbolKind(symbol.kind),
          range: range,
          selectionRange: range,
          children: []
        };
      });
    } catch (error) {
      logger.error('getFileSymbols failed:', error);
      return [];
    }
  }

  async getCompletionItems(filePath: string, repositoryFolderName?: string): Promise<CompletionItem[]> {
    try {
      const language = getLanguageFromExtension(filePath);

      if (!language) {
        return [];
      }

      if (!repositoryFolderName) {
        logger.warn('getCompletionItems: repositoryFolderName not provided, using unknown_repository');
        repositoryFolderName = 'unknown_repository';
      }

      const symbols = database.getSymbolsByFile(filePath, repositoryFolderName);

      return symbols.map(symbol => ({
        label: symbol.name,
        kind: this.symbolKindToCompletionItemKind(symbol.kind),
        detail: `${symbol.kind} in ${symbol.filePath}`,
        documentation: `Definition at line ${symbol.startLine + 1}`
      }));
    } catch (error) {
      logger.error('getCompletionItems failed:', error);
      return [];
    }
  }

  private setupEventHandlers(): void {
    if (!this.connection || !this.documents) return;

    // 초기화
    this.connection.onInitialize((params: InitializeParams) => {
      const capabilities = params.capabilities;

      // 클라이언트 기능 확인
      this.hasConfigurationCapability = !!(
        capabilities.workspace && !!capabilities.workspace.configuration
      );
      this.hasWorkspaceFolderCapability = !!(
        capabilities.workspace && !!capabilities.workspace.workspaceFolders
      );
      this.hasDiagnosticRelatedInformationCapability = !!(
        capabilities.textDocument &&
        capabilities.textDocument.publishDiagnostics &&
        capabilities.textDocument.publishDiagnostics.relatedInformation
      );

      const result: InitializeResult = {
        capabilities: {
          textDocumentSync: TextDocumentSyncKind.Incremental,
          // 정의로 이동
          definitionProvider: true,
          // 참조 찾기
          referencesProvider: true,
          // 자동완성
          completionProvider: {
            resolveProvider: true,
            triggerCharacters: ['.', ':']
          },
          // 심볼 제공
          documentSymbolProvider: true,
          workspaceSymbolProvider: true
        }
      };

      if (this.hasWorkspaceFolderCapability) {
        result.capabilities.workspace = {
          workspaceFolders: {
            supported: true
          }
        };
      }

      logger.info('LSP server initialized');
      return result;
    });

    // 초기화 완료
    this.connection.onInitialized(() => {
      if (this.hasConfigurationCapability) {
        this.connection!.client.register(DidChangeConfigurationNotification.type, undefined);
      }
      if (this.hasWorkspaceFolderCapability) {
        this.connection!.workspace.onDidChangeWorkspaceFolders((_event: import('vscode-languageserver').DidChangeWorkspaceFoldersParams) => {
          logger.info('Workspace folder change event received.');
        });
      }
    });

    // 정의 요청 처리
    this.connection.onDefinition((params: TextDocumentPositionParams): Location[] => {
      const uri = params.textDocument.uri;
      const position = params.position;
      
      // URI에서 리포지토리 폴더명 추출
      const repositoryFolderName = this.getRepositoryFolderNameFromUri(uri);
      
      logger.debug(`Definition request for ${uri} at ${position.line}:${position.character} (repository: ${repositoryFolderName})`);
      
      try {
        const document = this.documents!.get(uri);
        if (!document) {
          return [];
        }

        const word = this.getWordAtPosition(document, position);
        if (!word) {
          return [];
        }

        const symbols = database.getSymbolsByName(word, repositoryFolderName);
        
        return symbols.map(symbol => ({
          uri: this.pathToUri(symbol.filePath),
          range: {
            start: { line: symbol.startLine, character: symbol.startCharacter },
            end: { line: symbol.endLine, character: symbol.endCharacter }
          }
        }));
      } catch (error) {
        logger.error('Definition request failed:', error);
        return [];
      }
    });

    // 참조 요청 처리
    interface ReferenceParams {
      textDocument: { uri: string };
      position: Position;
      context: { includeDeclaration: boolean };
    }

    interface Reference {
      filePath: string;
      startLine: number;
      startCharacter: number;
      endLine: number;
      endCharacter: number;
    }

    interface Symbol {
      filePath: string;
      startLine: number;
      startCharacter: number;
      endLine: number;
      endCharacter: number;
      name: string;
      kind: string;
    }

    this.connection.onReferences((params: ReferenceParams): Location[] => {
      const uri = params.textDocument.uri;
      const position = params.position;
      
      // URI에서 리포지토리 폴더명 추출
      const repositoryFolderName = this.getRepositoryFolderNameFromUri(uri);
      
      logger.debug(`References request for ${uri} at ${position.line}:${position.character} (repository: ${repositoryFolderName})`);
      
      try {
        const document = this.documents!.get(uri);
        if (!document) {
          return [];
        }

        const word = this.getWordAtPosition(document, position);
        if (!word) {
          return [];
        }

        const references: Reference[] = database.getReferencesByName(word, repositoryFolderName);
        
        const locations: Location[] = references.map(ref => ({
          uri: this.pathToUri(ref.filePath),
          range: {
            start: { line: ref.startLine, character: ref.startCharacter },
            end: { line: ref.endLine, character: ref.endCharacter }
          }
        }));

        // 정의도 포함하려면
        if (params.context.includeDeclaration) {
          const symbols: Symbol[] = database.getSymbolsByName(word, repositoryFolderName);
          symbols.forEach(symbol => {
            locations.push({
              uri: this.pathToUri(symbol.filePath),
              range: {
                start: { line: symbol.startLine, character: symbol.startCharacter },
                end: { line: symbol.endLine, character: symbol.endCharacter }
              }
            });
          });
        }

        return locations;
      } catch (error) {
        logger.error('References request failed:', error);
        return [];
      }
    });

    // 자동완성 요청 처리
    this.connection.onCompletion((params: TextDocumentPositionParams): CompletionItem[] => {
      const uri = params.textDocument.uri;
      const position = params.position;

      // URI에서 리포지토리 폴더명 추출
      const repositoryFolderName = this.getRepositoryFolderNameFromUri(uri);

      logger.debug(`Completion request for ${uri} at ${position.line}:${position.character} (repository: ${repositoryFolderName})`);

      try {
        const document = this.documents!.get(uri);
        if (!document) {
          return [];
        }

        const filePath = this.uriToPath(uri);
        const language = getLanguageFromExtension(filePath);

        if (!language) {
          return [];
        }

        // 현재 파일의 심볼들을 자동완성 후보로 제공
        const symbols = database.getSymbolsByFile(filePath, repositoryFolderName);

        return symbols.map(symbol => ({
          label: symbol.name,
          kind: this.symbolKindToCompletionItemKind(symbol.kind),
          detail: `${symbol.kind} in ${symbol.filePath}`,
          documentation: `Definition at line ${symbol.startLine + 1}`
        }));
      } catch (error) {
        logger.error('Completion request failed:', error);
        return [];
      }
    });

    // 문서 심볼 요청 처리
    interface DocumentSymbolParams {
      textDocument: { uri: string };
    }

    interface SymbolFromDB {
      filePath: string;
      startLine: number;
      startCharacter: number;
      endLine: number;
      endCharacter: number;
      name: string;
      kind: string;
    }

    interface RangeType {
      start: { line: number; character: number };
      end: { line: number; character: number };
    }

    this.connection.onDocumentSymbol((params: DocumentSymbolParams): DocumentSymbol[] => {
      const uri = params.textDocument.uri;
      const filePath = this.uriToPath(uri);

      // URI에서 리포지토리 폴더명 추출
      const repositoryFolderName = this.getRepositoryFolderNameFromUri(uri);

      logger.debug(`Document symbol request for ${uri} (repository: ${repositoryFolderName})`);

      try {
        const symbols: SymbolFromDB[] = database.getSymbolsByFile(filePath, repositoryFolderName);

        return symbols.map((symbol: SymbolFromDB) => {
          const range: RangeType = {
            start: { line: symbol.startLine, character: symbol.startCharacter },
            end: { line: symbol.endLine, character: symbol.endCharacter }
          };

          // DocumentSymbol 타입 반환
          const documentSymbol: DocumentSymbol = {
            name: symbol.name,
            kind: this.symbolKindToLSPSymbolKind(symbol.kind),
            range: range,
            selectionRange: range,
            children: []
          };

          return documentSymbol;
        });
      } catch (error) {
        logger.error('Document symbol request failed:', error);
        return [];
      }
    });

    // 설정 변경 처리
    this.connection.onDidChangeConfiguration((change: { settings: any }) => {
      logger.info('Configuration changed:', change.settings);
    });

    // 문서 관리
    this.documents.onDidClose((e) => {
      logger.debug(`Document closed: ${e.document.uri}`);
    });

    this.documents.onDidChangeContent((change) => {
      logger.debug(`Document changed: ${change.document.uri}`);
    });

    this.documents.listen(this.connection);
  }

  // 서버 시작 (standalone 모드에서만 사용)
  public start(): void {
    if (!this.isStandaloneMode || !this.connection) {
      logger.warn('LSP server start() called but not in standalone mode');
      return;
    }
    
    logger.info('Starting LSP server...');
    this.connection.listen();
  }

  // 유틸리티 메서드들
  private getWordAtPosition(document: TextDocument, position: Position): string | null {
    const text = document.getText();
    const lines = text.split('\n');
    
    if (position.line >= lines.length) {
      return null;
    }

    const line = lines[position.line];
    const char = position.character;
    
    // 단어 경계 찾기
    let start = char;
    let end = char;
    
    // 뒤로 이동
    while (start > 0 && /\w/.test(line[start - 1])) {
      start--;
    }
    
    // 앞으로 이동
    while (end < line.length && /\w/.test(line[end])) {
      end++;
    }
    
    return line.slice(start, end);
  }

  private pathToUri(filePath: string): string {
    return `file://${filePath}`;
  }

  private uriToPath(uri: string): string {
    return uri.replace('file://', '');
  }

  private symbolKindToCompletionItemKind(kind: string): CompletionItemKind {
    switch (kind) {
      case 'function': return CompletionItemKind.Function;
      case 'method': return CompletionItemKind.Method;
      case 'class': return CompletionItemKind.Class;
      case 'variable': return CompletionItemKind.Variable;
      case 'constant': return CompletionItemKind.Constant;
      case 'property': return CompletionItemKind.Property;
      case 'interface': return CompletionItemKind.Interface;
      case 'enum': return CompletionItemKind.Enum;
      case 'module': return CompletionItemKind.Module;
      default: return CompletionItemKind.Text;
    }
  }

  private symbolKindToLSPSymbolKind(kind: string): SymbolKind {
    switch (kind) {
      case 'function': return SymbolKind.Function;
      case 'method': return SymbolKind.Method;
      case 'class': return SymbolKind.Class;
      case 'variable': return SymbolKind.Variable;
      case 'constant': return SymbolKind.Constant;
      case 'property': return SymbolKind.Property;
      case 'interface': return SymbolKind.Interface;
      case 'enum': return SymbolKind.Enum;
      case 'module': return SymbolKind.Module;
      default: return SymbolKind.Variable;
    }
  }

  // URI에서 리포지토리 폴더명 추출 (예: file:///repositories/my-repo_123/src/file.ts -> my-repo_123)
  private getRepositoryFolderNameFromUri(uri: string): string {
    try {
      // file:// 제거 후 경로 처리
      const filePath = uri.replace(/^file:\/\//, '');
      
      // repositoriesPath 환경변수 가져오기
      const repositoriesPath = process.env.REPOSITORIES_PATH || '/repositories';
      
      // URI가 repositories 경로 내에 있는지 확인
      if (filePath.includes(repositoriesPath)) {
        // /repositories/ 다음 첫번째 폴더명이 리포지토리 폴더명
        const parts = filePath.split(repositoriesPath + '/');
        if (parts.length > 1) {
          const pathParts = parts[1].split('/');
          if (pathParts.length > 0) {
            return pathParts[0]; // 첫번째 디렉토리 = 리포지토리 폴더명
          }
        }
      }
      
      // 기본값: 'unknown_repository'
      return 'unknown_repository';
    } catch (error) {
      logger.error(`Failed to extract repository folder name from URI: ${uri}`, error);
      return 'unknown_repository';
    }
  }
}

export default LSPServer;