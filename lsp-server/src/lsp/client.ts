// src/lsp/client.ts - LSP 클라이언트 래퍼
import {
  InitializeRequest,
  DefinitionRequest,
  ReferencesRequest,
  DocumentSymbolRequest,
  CompletionRequest,
  TextDocumentPositionParams,
  Location,
  DocumentSymbol,
  CompletionItem,
  DidOpenTextDocumentNotification,
  DidCloseTextDocumentNotification,
  TextDocumentItem
} from 'vscode-languageserver-protocol/node';

import { createMessageConnection, StreamMessageReader, StreamMessageWriter } from 'vscode-jsonrpc/node';
import { spawn, ChildProcess } from 'child_process';
import logger from '../shared/logger';

export class LSPClientWrapper {
  private connection: any;
  private lspProcess: ChildProcess | null = null;
  private isInitialized = false;
  private openDocuments = new Map<string, string>();

  constructor() {
    // LSP 서버 프로세스 시작
    this.startLSPServer();
  }

  private startLSPServer(): void {
    try {
      // 같은 컨테이너 내에서 LSP 서버 프로세스 시작
      this.lspProcess = spawn('node', ['dist/lsp/standalone.js', '--stdio'], {
        stdio: ['pipe', 'pipe', 'inherit']
      });

      if (!this.lspProcess.stdin || !this.lspProcess.stdout) {
        throw new Error('Failed to create LSP server process');
      }

      // JSON-RPC 연결 생성
      const reader = new StreamMessageReader(this.lspProcess.stdout);
      const writer = new StreamMessageWriter(this.lspProcess.stdin);
      this.connection = createMessageConnection(reader, writer);

      // 연결 시작
      this.connection.listen();

      // LSP 서버 초기화
      this.initializeLSPServer();

      logger.info('LSP server process started and connected');
    } catch (error) {
      logger.error('Failed to start LSP server:', error);
      throw error;
    }
  }

  private async initializeLSPServer(): Promise<void> {
    try {
      const initParams = {
        processId: process.pid,
        rootPath: process.cwd(),
        rootUri: `file://${process.cwd()}`,
        capabilities: {
          workspace: {
            workspaceFolders: true,
            configuration: true
          },
          textDocument: {
            definition: true,
            references: true,
            documentSymbol: true,
            completion: true,
            synchronization: {
              dynamicRegistration: true,
              willSave: false,
              willSaveWaitUntil: false,
              didSave: false
            }
          }
        },
        workspaceFolders: []
      };

      const result = await this.connection.sendRequest(InitializeRequest.type, initParams);
      await this.connection.sendNotification('initialized', {});
      
      this.isInitialized = true;
      logger.info('LSP server initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize LSP server:', error);
      throw error;
    }
  }

  // 파일을 LSP 서버에 열기
  private async openDocument(uri: string, content: string, languageId: string): Promise<void> {
    if (this.openDocuments.has(uri)) {
      return; // 이미 열린 문서
    }

    const textDocument: TextDocumentItem = {
      uri,
      languageId,
      version: 1,
      text: content
    };

    await this.connection.sendNotification(DidOpenTextDocumentNotification.type, {
      textDocument
    });

    this.openDocuments.set(uri, content);
  }

  // 문서 닫기
  private async closeDocument(uri: string): Promise<void> {
    if (!this.openDocuments.has(uri)) {
      return;
    }

    await this.connection.sendNotification(DidCloseTextDocumentNotification.type, {
      textDocument: { uri }
    });

    this.openDocuments.delete(uri);
  }

  // REST API용 메서드들
  async getDefinition(filePath: string, line: number, character: number, fileContent?: string): Promise<Location[]> {
    if (!this.isInitialized) {
      throw new Error('LSP server not initialized');
    }

    try {
      const uri = `file://${filePath}`;
      
      // 파일이 열려있지 않다면 열기
      if (fileContent && !this.openDocuments.has(uri)) {
        const languageId = this.getLanguageId(filePath);
        await this.openDocument(uri, fileContent, languageId);
      }

      const params: TextDocumentPositionParams = {
        textDocument: { uri },
        position: { line, character }
      };

      const result = await this.connection.sendRequest(DefinitionRequest.type, params);
      return Array.isArray(result) ? result : result ? [result] : [];
    } catch (error) {
      logger.error('getDefinition failed:', error);
      return [];
    }
  }

  async getReferences(filePath: string, line: number, character: number, includeDeclaration: boolean = true, fileContent?: string): Promise<Location[]> {
    if (!this.isInitialized) {
      throw new Error('LSP server not initialized');
    }

    try {
      const uri = `file://${filePath}`;
      
      if (fileContent && !this.openDocuments.has(uri)) {
        const languageId = this.getLanguageId(filePath);
        await this.openDocument(uri, fileContent, languageId);
      }

      const params = {
        textDocument: { uri },
        position: { line, character },
        context: { includeDeclaration }
      };

      const result = await this.connection.sendRequest(ReferencesRequest.type, params);
      return result || [];
    } catch (error) {
      logger.error('getReferences failed:', error);
      return [];
    }
  }

  async getDocumentSymbols(filePath: string, fileContent?: string): Promise<DocumentSymbol[]> {
    if (!this.isInitialized) {
      throw new Error('LSP server not initialized');
    }

    try {
      const uri = `file://${filePath}`;
      
      if (fileContent && !this.openDocuments.has(uri)) {
        const languageId = this.getLanguageId(filePath);
        await this.openDocument(uri, fileContent, languageId);
      }

      const params = {
        textDocument: { uri }
      };

      const result = await this.connection.sendRequest(DocumentSymbolRequest.type, params);
      return result || [];
    } catch (error) {
      logger.error('getDocumentSymbols failed:', error);
      return [];
    }
  }

  async getCompletions(filePath: string, line: number, character: number, fileContent?: string): Promise<CompletionItem[]> {
    if (!this.isInitialized) {
      throw new Error('LSP server not initialized');
    }

    try {
      const uri = `file://${filePath}`;
      
      if (fileContent && !this.openDocuments.has(uri)) {
        const languageId = this.getLanguageId(filePath);
        await this.openDocument(uri, fileContent, languageId);
      }

      const params: TextDocumentPositionParams = {
        textDocument: { uri },
        position: { line, character }
      };

      const result = await this.connection.sendRequest(CompletionRequest.type, params);
      
      if (result && typeof result === 'object' && 'items' in result) {
        return result.items;
      }
      
      return Array.isArray(result) ? result : [];
    } catch (error) {
      logger.error('getCompletions failed:', error);
      return [];
    }
  }

  // 특정 심볼명으로 정의 찾기 (데이터베이스 기반)
  async findSymbolDefinition(symbolName: string, repositoryFolderName: string = 'unknown_repository'): Promise<Location[]> {
    try {
      // 여기서는 데이터베이스에서 직접 조회하거나
      // LSP 서버에 커스텀 요청을 보낼 수 있음
      const database = require('../database').default;
      const symbols = database.getSymbolsByName(symbolName, repositoryFolderName);

      return symbols.map((symbol: any) => ({
        uri: `file://${symbol.filePath}`,
        range: {
          start: { line: symbol.startLine, character: symbol.startCharacter },
          end: { line: symbol.endLine, character: symbol.endCharacter }
        }
      }));
    } catch (error) {
      logger.error('findSymbolDefinition failed:', error);
      return [];
    }
  }

  // 특정 심볼명으로 참조 찾기 (데이터베이스 기반)
  async findSymbolReferences(symbolName: string, repositoryFolderName: string = 'unknown_repository'): Promise<Location[]> {
    try {
      const database = require('../database').default;
      const references = database.getReferencesByName(symbolName, repositoryFolderName);

      return references.map((ref: any) => ({
        uri: `file://${ref.filePath}`,
        range: {
          start: { line: ref.startLine, character: ref.startCharacter },
          end: { line: ref.endLine, character: ref.endCharacter }
        }
      }));
    } catch (error) {
      logger.error('findSymbolReferences failed:', error);
      return [];
    }
  }

  private getLanguageId(filePath: string): string {
    const ext = filePath.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'js': return 'javascript';
      case 'ts': return 'typescript';
      case 'tsx': return 'typescriptreact';
      case 'py': return 'python';
      case 'rb': return 'ruby';
      default: return 'plaintext';
    }
  }

  // 정리
  async dispose(): Promise<void> {
    try {
      // 열린 문서들 모두 닫기
      for (const uri of this.openDocuments.keys()) {
        await this.closeDocument(uri);
      }

      // 연결 종료
      if (this.connection) {
        this.connection.dispose();
      }

      // LSP 서버 프로세스 종료
      if (this.lspProcess) {
        this.lspProcess.kill();
      }

      logger.info('LSP client disposed');
    } catch (error) {
      logger.error('Error disposing LSP client:', error);
    }
  }
}