# Production Dockerfile

FROM node:18-slim
WORKDIR /usr/src/app

# Install dependencies for tree-sitter
R<PERSON> apt-get update && apt-get install -y python3 make g++ && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package*.json ./

# Install only production dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/
COPY tsconfig.json ./

# Install dev dependencies for build
RUN npm install --save-dev typescript ts-node @types/node

# LSP standalone 스크립트 빌드 확인
RUN npm run build

# Remove dev dependencies to reduce image size
RUN npm prune --production

EXPOSE 7107

# 환경 변수 설정
ENV USE_LSP_CLIENT=true
ENV NODE_ENV=production

# 실행 명령
CMD ["npm", "run", "start:lsp-client"]
