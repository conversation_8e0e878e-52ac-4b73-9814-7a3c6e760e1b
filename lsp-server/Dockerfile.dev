# 개발용 Dockerfile - 최소한의 설정만 포함
FROM node:18-slim
WORKDIR /usr/src/app

# Install dependencies for tree-sitter
R<PERSON> apt-get update && apt-get install -y python3 make g++ && rm -rf /var/lib/apt/lists/*

# Copy package files only
COPY package*.json ./

# Install all dependencies (including dev dependencies for development)
RUN npm install

# 소스 코드는 볼륨 마운트로 제공되므로 복사하지 않음
# 개발 환경에서는 ts-node로 직접 실행

EXPOSE 7107

# 개발용 기본 명령어 (docker-compose에서 override됨)
CMD ["npm", "run", "dev"]
