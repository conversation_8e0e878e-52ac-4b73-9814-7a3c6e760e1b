# Repository ID 표준화 가이드

## 📋 개요

이 문서는 전체 CodeBase Intelligence 시스템에서 Repository 식별자의 혼재 문제를 해결하고 일관성 있는 표준을 정립하기 위한 가이드입니다.

## 🚨 현재 문제점

### 1. 용어 혼재로 인한 혼란

현재 시스템에서 `repository_id` 또는 `repositoryId`가 다음과 같이 혼재되어 사용되고 있습니다:

- **때로는 숫자 ID**: `13` (데이터베이스 Primary Key)
- **때로는 폴더명**: `codebase-sqlite_13` (실제 파일시스템 폴더명)
- **때로는 리포지토리명**: `codebase-sqlite` (사용자가 인식하는 이름)

### 2. 실제 폴더 구조

```
/mnt/hdd500/data/codebase-intelligence/repos/
├── codebase-sqlite_13/     # 실제 클론된 폴더
├── my-awesome-project_25/  # 실제 클론된 폴더
└── frontend-app_7/         # 실제 클론된 폴더
```

- 백엔드 `Repository` 모델의 `sanitized_repository_name` 메서드가 `"#{repo_name}_#{id}"` 형태로 생성
- 예: `codebase-sqlite_13`

### 3. 각 서비스별 혼란 상황

#### Backend (Ruby on Rails)
```ruby
# Repository 모델에서
def sanitized_repository_name
  "#{sanitized}_#{id}"  # codebase-sqlite_13
end

# 하지만 코드 곳곳에서 repository_id는 숫자 ID로 사용
scope :by_repository, ->(repository_id) { where(repository_id: repository_id) }
```

#### LSP Server (현재)
```typescript
// 혼란: repositoryId가 무엇을 의미하는지 불명확
interface RepositoryAnalysisRequest {
  repositoryId: string;  // "13"? "codebase-sqlite_13"? 
}
```

#### Frontend (TypeScript)
```typescript
// 혼재: 때로는 숫자, 때로는 문자열
setRepositoryContext: (repositoryId: number, repositoryName: string, branch: string)
```

#### Aiend (Python)
```python
# repository_branch_id 파싱에서 혼란
def parse_repository_branch_id(repository_branch_id: str):
    # "codebase-sqlite_13_main"에서 repository_id 추출
    # 하지만 repository_id가 "13"인지 "codebase-sqlite_13"인지 불명확
```

### 4. 개발자 및 AI 혼란

- **개발자**: 코드 작성 시 어떤 형태의 ID를 사용해야 할지 혼란
- **AI 어시스턴트**: 컨텍스트에 따라 다른 해석으로 인한 잘못된 코드 생성
- **버그 발생**: 잘못된 형태의 ID 사용으로 인한 런타임 오류

## 🎯 표준화 방안

### 1. 용어 정의

| 용어 | 타입 | 의미 | 예시 | 사용 위치 |
|------|------|------|------|-----------|
| `repository_id` | `number` | 데이터베이스 Primary Key | `13` | 백엔드 DB, 내부 참조 |
| `repository_folder_name` | `string` | 실제 파일시스템 폴더명 | `codebase-sqlite_13` | 파일시스템 접근, 외부 API |
| `repository_name` | `string` | 사용자가 인식하는 이름 | `codebase-sqlite` | UI 표시, 로그 메시지 |

### 2. 폴더명 생성 규칙

```
{repository_name_sanitized}_{repository_id}
```

예시:
- `codebase-sqlite_13`
- `my-awesome-project_25`
- `frontend_app_7`

### 3. API 표준화

#### 요청 시
```json
{
  "repositoryFolderName": "codebase-sqlite_13",
  "recursive": true
}
```

#### 응답 시
```json
{
  "status": "ok",
  "repositoryFolderName": "codebase-sqlite_13",
  "repositoryPath": "/app/repositories/codebase-sqlite_13",
  "symbolsFound": 1250
}
```
├── codebase-sqlite_13/
├── my-awesome-project_24/
├── test-repo_7/
└── ...
```

- 폴더명 형식: `{repository_name}_{database_id}`
- 예: `codebase-sqlite_13` = repository name "codebase-sqlite" + database ID "13"

### 3. 서비스별 혼란 사례

#### Backend (Ruby on Rails)
```ruby
# 혼란 1: repository_id가 DB ID인지 폴더명인지 불분명
def scan_repository(repository_id)
  # repository_id가 "13"인지 "codebase-sqlite_13"인지?
end

# 혼란 2: 폴더명 생성 로직이 분산
def sanitized_repository_name
  "#{repo_name}_#{id}"  # 여기서 생성
end
```

#### LSP Server (TypeScript)
```typescript
// 혼란 1: repositoryId가 무엇을 의미하는지 불분명
interface RepositoryAnalysisRequest {
  repositoryId: string;  // "13"? "codebase-sqlite_13"?
}

// 혼란 2: 파일 경로 구성 시 혼란
const repositoryPath = path.join(REPOSITORIES_PATH, repositoryId);
```

#### Frontend (TypeScript)
```typescript
// 혼란 1: 숫자 ID와 문자열 ID 혼재
setRepositoryContext: (repositoryId: number, repositoryName: string, branch: string)

// 혼란 2: API 호출 시 어떤 값을 전달해야 하는지 불분명
getFileTree: (repositoryId: string, options?: {...})
```

#### Aiend (Python)
```python
# 혼란 1: repository_branch_id 파싱 시 혼란
def parse_repository_branch_id(repository_branch_id):
    # "codebase-sqlite_13_main"에서 어떻게 분리할지?
    # repository_id는 "13"인지 "codebase-sqlite_13"인지?
```

## 💡 표준화 방안

### 1. 용어 정의 표준화

| 용어 | 의미 | 타입 | 예시 | 사용 위치 |
|------|------|------|------|----------|
| `repository_id` | 데이터베이스 숫자 ID | `number` | `13` | 백엔드 DB, 내부 참조 |
| `repository_folder_name` | 실제 폴더명 | `string` | `codebase-sqlite_13` | 파일시스템, 외부 API |
| `repository_name` | 사용자가 인식하는 이름 | `string` | `codebase-sqlite` | UI 표시용 |

### 2. 명명 규칙 표준화

#### Backend (Ruby)
```ruby
# 명확한 구분
class Repository
  def id                    # => 13 (숫자 DB ID)
  def folder_name          # => "codebase-sqlite_13" (폴더명)
  def display_name         # => "codebase-sqlite" (표시용 이름)
end
```

#### LSP Server (TypeScript)
```typescript
// 명확한 구분
interface RepositoryAnalysisRequest {
  repositoryFolderName: string;  // 명확히 폴더명임을 표시
}

interface RepositoryInfo {
  id: number;                    // DB ID
  folderName: string;           // 폴더명
  displayName: string;          // 표시용 이름
}
```

#### Frontend (TypeScript)
```typescript
// 명확한 구분
interface RepositoryContext {
  id: number;                    // DB ID
  folderName: string;           // 폴더명  
  displayName: string;          // 표시용 이름
  branch: string;
}
```

#### Aiend (Python)
```python
# 명확한 구분
@dataclass
class RepositoryInfo:
    id: int                      # DB ID
    folder_name: str            # 폴더명
    display_name: str           # 표시용 이름
    branch: str
```

### 3. API 표준화

#### REST API 파라미터
```bash
# 이전 (혼란스러운 방식)
POST /analysis/scan-repository
{
  "repositoryId": "codebase-sqlite_13"  # 이것이 ID인지 폴더명인지 불분명
}

# 이후 (명확한 방식)
POST /analysis/scan-repository
{
  "repositoryFolderName": "codebase-sqlite_13"  # 명확히 폴더명
}
```

#### URL 경로 표준화
```bash
# 이전 (혼란스러운 방식)
GET /repositories/:id                    # id가 숫자인지 폴더명인지 불분명

# 이후 (명확한 방식)
GET /repositories/:id                    # 숫자 DB ID
GET /repositories/by-folder/:folderName  # 폴더명으로 조회
```

## 🔄 단계별 마이그레이션 계획

### Phase 1: LSP Server 표준화 (우선순위 1)
**목표**: LSP Server에서 명확한 네이밍 사용

**수정 사항**:
1. `repositoryId` → `repositoryFolderName`
2. 타입 정의 업데이트
3. API 문서 업데이트
4. 환경 변수 이름 명확화

**수정 파일**:
- `src/shared/types.ts`
- `src/api/routes.ts`
- `md/lsp.api.md`
- `docker-compose.yml`

### Phase 2: Backend API 표준화 (우선순위 2)
**목표**: Backend API 응답에서 명확한 필드 제공

**수정 사항**:
1. Repository 모델에 `folder_name` 메서드 추가
2. API 응답에서 `id`, `folder_name`, `display_name` 명시적 제공
3. 기존 API 호환성 유지 (deprecated 마크)

**수정 파일**:
- `app/models/repository.rb`
- `app/controllers/api/repositories_controller.rb`
- API 문서 업데이트

### Phase 3: Frontend 표준화 (우선순위 3)
**목표**: Frontend Store와 API 호출 명확화

**수정 사항**:
1. Repository Store 타입 정의 업데이트
2. API 호출 시 명확한 파라미터 사용
3. UI에서 적절한 필드 표시

**수정 파일**:
- `src/stores/repositoryStore.ts`
- API 호출 관련 서비스 파일들
- 컴포넌트 업데이트

### Phase 4: Aiend 표준화 (우선순위 4)
**목표**: Aiend에서 명확한 Repository 처리

**수정 사항**:
1. `repository_branch_id` 파싱 로직 명확화
2. 폴더명 기반 처리 표준화
3. Qdrant collection 명명 규칙 업데이트

**수정 파일**:
- Repository 관련 파싱 함수들
- Qdrant 연동 코드
- 환경 변수 및 설정

### Phase 5: VectorDB 표준화 (우선순위 5)
**목표**: VectorDB collection 명명 규칙 표준화

**수정 사항**:
1. Collection 이름 규칙 명확화
2. 메타데이터 필드 표준화

## 📋 구현 체크리스트

### LSP Server
- [ ] `RepositoryAnalysisRequest` 타입 업데이트
- [ ] API 엔드포인트 파라미터명 변경
- [ ] 환경 변수 `REPOSITORIES_PATH` 명확화
- [ ] API 문서 업데이트
- [ ] 에러 메시지 업데이트

### Backend
- [ ] `Repository` 모델에 `folder_name` 메서드 추가
- [ ] API 응답 필드 표준화
- [ ] 기존 API 호환성 유지
- [ ] 문서 업데이트

### Frontend
- [ ] `RepositoryStore` 타입 업데이트
- [ ] API 호출 표준화
- [ ] UI 표시 필드 명확화

### Aiend
- [ ] 파싱 로직 명확화
- [ ] 폴더 경로 처리 표준화
- [ ] Collection 명명 규칙 업데이트

### VectorDB
- [ ] Collection 명명 규칙 표준화
- [ ] 메타데이터 필드 표준화

## 🎯 기대 효과

### 1. 개발자 경험 개선
- **명확성**: 변수명만 봐도 의미 파악 가능
- **일관성**: 모든 서비스에서 동일한 네이밍 규칙
- **유지보수성**: 코드 수정 시 실수 방지

### 2. AI 어시스턴트 성능 향상
- **정확성**: AI가 코드 컨텍스트를 정확히 이해
- **효율성**: 혼란 없는 코드 생성 및 수정
- **일관성**: 모든 서비스에서 동일한 패턴 적용

### 3. 시스템 안정성 향상
- **버그 방지**: 잘못된 ID 사용으로 인한 오류 방지
- **디버깅 용이성**: 로그에서 명확한 정보 확인
- **테스트 용이성**: 명확한 데이터 구조로 테스트 작성

## 🚀 실행 계획

### 1주차: LSP Server 표준화
- 타입 정의 업데이트
- API 엔드포인트 수정
- 문서 업데이트
- 테스트 수행

### 2주차: Backend API 표준화
- Repository 모델 업데이트
- API 응답 표준화
- 하위 호환성 확보
- 문서 업데이트

### 3주차: Frontend 표준화
- Store 타입 업데이트
- API 호출 표준화
- UI 업데이트
- 테스트 수행

### 4주차: Aiend & VectorDB 표준화
- 파싱 로직 업데이트
- Collection 명명 규칙 적용
- 통합 테스트 수행

### 5주차: 검증 및 문서화
- 전체 시스템 통합 테스트
- 문서 완성
- 팀 가이드 배포

## 📚 참고 자료

### 현재 구현 상태
- **Backend**: `Repository#sanitized_repository_name` 메서드가 폴더명 생성
- **LSP Server**: `repositoryId` 파라미터로 폴더명 전달
- **Frontend**: 숫자 ID와 문자열 ID 혼재
- **Aiend**: `repository_branch_id` 파싱으로 처리

### 관련 파일
- `backend/app/models/repository.rb`
- `lsp-server/src/shared/types.ts`
- `frontend/src/stores/repositoryStore.ts`
- `aiend/src/codebase_intelligence/`

### 환경 설정
- Docker volumes: `/mnt/hdd500/data/codebase-intelligence/repos`
- 네트워크: `codebase-intelligence`
- 포트: Backend(7101), LSP(7107), Frontend(7105), Aiend(7102)

---

**작성일**: 2025-07-10  
**버전**: 1.0  
**작성자**: AI Assistant  
**승인**: 팀 검토 필요
