웹 검색을 통해 tree-sitter 관련 문서들을 분석한 결과, 근본적인 해결책을 찾았습니다.

## 🔍 문제의 근본 원인

tree-sitter-typescript는 실제로 두 개의 서로 다른 문법(TypeScript와 TSX)을 정의하므로, 올바른 import 방식을 사용해야 합니다. Tree-sitter의 공식 사용법은 require() 방식을 권장합니다.

## ✅ 근본적인 해결책

**src/analyzer/parser.ts** 파일을 다음과 같이 수정하세요:

```typescript
// 잘못된 방식 (현재)
import TypeScript from 'tree-sitter-typescript';
import Ruby from 'tree-sitter-ruby';

// 올바른 방식 (수정)
const Parser = require('tree-sitter');
const JavaScript = require('tree-sitter-javascript');
const TypeScript = require('tree-sitter-typescript').typescript;  // 핵심!
const Ruby = require('tree-sitter-ruby');
```

## 🎯 핵심 포인트

1. **tree-sitter-typescript의 특수성**: 이 모듈은 typescript와 tsx 두 개의 다른 문법을 제공하므로, `.typescript`를 명시적으로 지정해야 합니다

2. **require() vs import**: Tree-sitter 공식 문서와 예제들은 모두 CommonJS의 require() 방식을 사용합니다

3. **타입 안전성**: TypeScript에서 사용할 때는 `import Parser = require('tree-sitter')` 방식을 사용할 수도 있습니다

## 🔧 완전한 수정 코드

```typescript
// src/analyzer/parser.ts
const Parser = require('tree-sitter');
const JavaScript = require('tree-sitter-javascript');
const TypeScript = require('tree-sitter-typescript').typescript;
const Ruby = require('tree-sitter-ruby');

export class TreeSitterParser {
  private parsers: { [key: string]: any } = {};

  constructor() {
    const languages = [
      { name: 'javascript', lang: JavaScript },
      { name: 'typescript', lang: TypeScript },
      { name: 'ruby', lang: Ruby }
    ];

    languages.forEach(({ name, lang }) => {
      try {
        this.parsers[name] = new Parser();
        this.parsers[name].setLanguage(lang);
        console.log(`[INFO] Initialized ${name} parser`);
      } catch (error) {
        console.error(`[ERROR] Failed to initialize ${name} parser:`, error);
      }
    });
  }
}
```

이 방식이 **tree-sitter 공식 문서에서 권장하는 표준 사용법**이며, "Invalid language object" 오류를 해결할 것입니다.