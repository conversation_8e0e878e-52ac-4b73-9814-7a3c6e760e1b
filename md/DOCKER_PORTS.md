# Docker Compose 포트 구성

## 🚀 서비스 포트 매핑

### 기본 포트 범위: 7100-7199

| 서비스 | 내부 포트 | 외부 포트 | 설명 |
|--------|-----------|-----------|------|
| **aiend** | 8000 | **7101** | AI Engine FastAPI 서버 |
| **backend** | 3000 | **7102** | Ruby on Rails 백엔드 |
| **frontend** | 5173 | **7103** | React + RSBuild 프론트엔드 |
| **ollama** | 11434 | **11434** | 로컬 LLM 서버 (호스트에서 실행) |
| **qdrant** | 6333 | **7105** | Qdrant HTTP API |
| **qdrant** | 6334 | **7106** | Qdrant gRPC API |
| **faiss-gpu** | 8001 | **7107** | FAISS GPU 벡터 검색 |
| **redis** | 6379 | **7108** | Redis 캐시 서버 |

## 🔧 서비스 접속 URL

### 개발 환경
- **Frontend**: http://localhost:7103
- **Backend API**: http://localhost:7102
- **AI Engine**: http://localhost:7101
- **Ollama**: http://localhost:11434 (호스트에서 실행)
- **Qdrant**: http://localhost:7105
- **FAISS GPU**: http://localhost:7107
- **Redis**: localhost:7108

### 컨테이너 간 통신
- **aiend → ollama**: http://host.docker.internal:11434 (호스트 Ollama)
- **aiend → qdrant**: http://qdrant:6333
- **aiend → redis**: redis://redis:6379/0
- **backend → aiend**: http://aiend:8000
- **frontend → backend**: http://backend:3000 (프록시 설정)

## 📋 서비스 구성

### 🤖 AI Engine (aiend)
- **기술**: Python, FastAPI, ChromaDB, Qdrant, FAISS
- **포트**: 7101
- **의존성**: qdrant, redis
- **외부 연결**: 호스트의 Ollama (11434)

### 🚀 Backend (backend)
- **기술**: Ruby on Rails 8, SQLite, Solid Cache/Queue/Cable
- **포트**: 7102
- **의존성**: aiend
- **Redis**: 불필요 (Solid 기능 사용)

### 🎨 Frontend (frontend)
- **기술**: React 18, RSBuild, TypeScript
- **포트**: 7103
- **의존성**: backend
- **프록시**: /api → backend, /ai → aiend

### 🧠 Ollama (ollama)
- **기술**: 로컬 LLM 서버
- **포트**: 11434 (호스트에서 실행)
- **GPU**: 필요
- **상태**: Docker 외부에서 실행

### 🔍 Qdrant (qdrant)
- **기술**: 벡터 데이터베이스
- **포트**: 7105 (HTTP), 7106 (gRPC)
- **용도**: 의미적 검색

### ⚡ FAISS GPU (faiss-gpu)
- **기술**: Facebook AI Similarity Search
- **포트**: 7107
- **GPU**: 필요
- **용도**: 고성능 벡터 검색

### 💾 Redis (redis)
- **기술**: 인메모리 캐시
- **포트**: 7108
- **용도**: aiend 캐싱 전용

## 🐳 Docker Compose 사용법

### 전체 시스템 시작
```bash
docker-compose up -d
```

### 특정 서비스만 시작
```bash
# AI Engine과 의존성
docker-compose up -d aiend qdrant faiss-gpu redis

# Backend만
docker-compose up -d backend aiend

# Frontend만
docker-compose up -d frontend backend aiend

# 벡터 데이터베이스만
docker-compose up -d qdrant faiss-gpu
```

### 개별 폴더에서 실행
```bash
# AI Engine 개별 실행
cd aiend && docker-compose up -d

# Backend 개별 실행
cd backend && docker-compose up -d

# Frontend 개별 실행 (개발 모드)
cd frontend && docker-compose up -d frontend-dev

# FAISS GPU 개별 실행
cd faiss-gpu && docker-compose up -d

# Qdrant 개별 실행
cd qdrant && docker-compose up -d
```

### 로그 확인
```bash
# 전체 로그
docker-compose logs -f

# 특정 서비스 로그
docker-compose logs -f aiend
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 서비스 상태 확인
```bash
# 컨테이너 상태
docker-compose ps

# 헬스체크 상태
docker-compose exec aiend curl http://localhost:8000/health
docker-compose exec qdrant curl http://localhost:6333/health
```

## 🔒 보안 고려사항

1. **포트 범위**: 7100-7199로 제한하여 다른 서비스와 충돌 방지
2. **내부 통신**: 컨테이너 간 통신은 내부 포트 사용
3. **외부 접근**: 필요한 서비스만 외부 포트 노출
4. **GPU 접근**: Ollama와 FAISS만 GPU 리소스 할당

## 🚨 주의사항

1. **GPU 요구사항**: NVIDIA GPU와 Docker GPU 지원 필요
2. **메모리**: 벡터 데이터베이스들이 많은 메모리 사용
3. **디스크**: 모델과 데이터 저장을 위한 충분한 디스크 공간 필요
4. **네트워크**: 서비스 간 통신을 위한 Docker 네트워크 설정
