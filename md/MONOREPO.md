# CodeBase Intelligence - 모노레포 구조

이 프로젝트는 모노레포 구조로 구성되어 있으며, 세 개의 주요 컴포넌트로 나뉩니다.

## 프로젝트 구조

```
codebase-sqlite/
├── README.md                 # 프로젝트 전체 개요
├── MONOREPO.md              # 이 파일 - 모노레포 구조 설명
├── aiend/                   # AI Engine (Python + FastAPI)
│   ├── src/                 # Python 소스 코드
│   ├── tests/               # 테스트 코드
│   ├── docs/                # AI Engine 문서
│   ├── requirements.txt     # Python 의존성
│   ├── pyproject.toml       # Python 프로젝트 설정
│   ├── Dockerfile           # AI Engine 컨테이너
│   ├── docker-compose.yml   # 개발 환경 설정
│   └── .gitignore           # AI Engine 전용 gitignore
├── backend/                 # Backend API (Ruby on Rails 8)
│   ├── app/                 # Rails 애플리케이션
│   ├── config/              # Rails 설정
│   ├── db/                  # 데이터베이스 마이그레이션
│   ├── Gemfile              # Ruby 의존성
│   ├── Dockerfile           # Backend 컨테이너
│   └── .gitignore           # Backend 전용 gitignore
└── frontend/                # Frontend (React + Rsbuild)
    ├── src/                 # React 소스 코드
    ├── public/              # 정적 파일
    ├── package.json         # Node.js 의존성
    ├── rsbuild.config.ts    # Rsbuild 설정
    ├── Dockerfile           # Frontend 컨테이너
    └── .gitignore           # Frontend 전용 gitignore
```

## 컴포넌트 설명

### 🤖 AI Engine (aiend/)

**기술 스택**: Python 3.11, FastAPI, SQLite, ChromaDB, Ollama

**주요 기능**:
- LLM 통합 및 오케스트레이션 (OpenAI, Anthropic, Ollama)
- 코드베이스 분석 및 인덱싱
- 의미적 검색 (Vector Search)
- Git 히스토리 분석
- 컨텍스트 최적화
- 머신러닝 모델 (예정)

**포트**: 8000 (FastAPI), 11434 (Ollama)

### 🚀 Backend (backend/)

**기술 스택**: Ruby on Rails 8, SQLite, Redis

**주요 기능**:
- 사용자 인증 및 권한 관리
- 프로젝트 관리
- 파일 업로드 및 관리
- AI Engine과의 통신
- 실시간 알림
- 데이터 영속성

**포트**: 3000

### 🎨 Frontend (frontend/)

**기술 스택**: React 18, Rsbuild, TanStack Router, TanStack Query, TypeScript

**주요 기능**:
- 사용자 인터페이스
- 코드 검색 및 탐색
- LLM 채팅 인터페이스
- 프로젝트 대시보드
- 실시간 업데이트
- 반응형 디자인

**포트**: 5173 (개발), 80 (프로덕션)

## 개발 환경 설정

### 전체 시스템 실행

```bash
# 루트 디렉토리에서
docker-compose up -d
```

### 개별 컴포넌트 개발

#### AI Engine 개발
```bash
cd aiend
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -e .
uvicorn codebase_intelligence.api.main:app --reload
```

#### Backend 개발
```bash
cd backend
bundle install
rails server
```

#### Frontend 개발
```bash
cd frontend
npm install
npm run dev
```

## 통신 구조

```
Frontend (React)
    ↓ HTTP/WebSocket
Backend (Rails)
    ↓ HTTP API
AI Engine (FastAPI)
    ↓ Local/API
Ollama + External LLMs
```

## 데이터 플로우

1. **사용자 요청**: Frontend → Backend
2. **인증/권한**: Backend에서 처리
3. **AI 처리**: Backend → AI Engine
4. **LLM 호출**: AI Engine → LLM Providers
5. **응답 반환**: LLM → AI Engine → Backend → Frontend

## 배포 전략

### 개발 환경
- Docker Compose를 사용한 로컬 개발
- 각 컴포넌트별 핫 리로드 지원

### 스테이징/프로덕션
- Kubernetes 또는 Docker Swarm
- 컴포넌트별 독립적 스케일링
- 로드 밸런서를 통한 트래픽 분산

## 환경 변수

### AI Engine (.env)
```env
OPENAI_API_KEY=your_key
ANTHROPIC_API_KEY=your_key
OLLAMA_HOST=http://localhost:11434
CODEBASE_DB_PATH=/app/data/codebase.db
```

### Backend (.env)
```env
DATABASE_URL=sqlite3:db/production.sqlite3
REDIS_URL=redis://localhost:6379
AI_ENGINE_URL=http://aiend:8000
```

### Frontend (.env)
```env
VITE_API_URL=http://localhost:3000
VITE_WS_URL=ws://localhost:3000/cable
```

## 개발 가이드라인

### 코드 스타일
- **Python**: Black, isort, flake8
- **Ruby**: RuboCop
- **TypeScript**: ESLint, Prettier

### 테스트
- **AI Engine**: pytest
- **Backend**: RSpec
- **Frontend**: Vitest, React Testing Library

### 커밋 컨벤션
```
feat(aiend): add ollama integration
fix(backend): resolve authentication issue
docs(frontend): update component documentation
```

## 모니터링

- **로그**: 각 컴포넌트별 구조화된 로깅
- **메트릭**: Prometheus + Grafana
- **헬스체크**: 각 서비스별 헬스체크 엔드포인트
- **알림**: 장애 발생 시 자동 알림

## 보안

- **API 인증**: JWT 토큰 기반
- **CORS**: 적절한 CORS 정책 설정
- **비밀 관리**: 환경 변수 또는 비밀 관리 도구
- **네트워크**: 내부 통신은 프라이빗 네트워크

## 확장성

- **수평 확장**: 각 컴포넌트 독립적 스케일링
- **캐싱**: Redis를 통한 응답 캐싱
- **큐**: 백그라운드 작업을 위한 작업 큐
- **CDN**: 정적 파일 배포

## 문제 해결

### 일반적인 문제
1. **포트 충돌**: 각 서비스의 포트 확인
2. **의존성 문제**: 각 컴포넌트의 의존성 재설치
3. **권한 문제**: Docker 볼륨 권한 확인
4. **네트워크 문제**: Docker 네트워크 설정 확인

### 로그 확인
```bash
# AI Engine 로그
docker-compose logs aiend

# Backend 로그
docker-compose logs backend

# Frontend 로그
docker-compose logs frontend
```
