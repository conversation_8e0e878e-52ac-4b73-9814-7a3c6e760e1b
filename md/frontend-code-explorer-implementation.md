# Frontend Code Explorer 구현 완료

VSCode 스타일의 파일 탐색기와 Monaco Editor를 활용한 코드 뷰어 기능을 프론트엔드에 구현했습니다.

## 📋 구현 개요

백엔드 API 설계 문서(`md/backend-file-explorer-api.md`)에 기반하여 프론트엔드 Code 메뉴와 파일 탐색기를 완전히 구현했습니다.

## 🎯 구현된 기능

### 1. Code 메뉴 추가
- **위치**: Sidebar의 View 메뉴 아래
- **라우트**: `/code`
- **아이콘**: Code 아이콘 사용

### 2. 파일 탐색기 API 타입 정의
```typescript
// frontend/src/types/index.ts에 추가된 타입들

export interface FileTreeNode {
  id: string;
  name: string;
  type: 'directory' | 'file';
  path: string;
  depth: number;
  children?: FileTreeNode[];
  expanded?: boolean;
  has_children?: boolean;
  file_count?: number;
  subdirectory_count?: number;
  
  // File specific properties
  size?: number;
  lines?: number;
  language?: string;
  extension?: string;
  is_binary?: boolean;
  last_modified?: string;
}

export interface FileContent {
  content: string;
  file: {
    id: number;
    name: string;
    path: string;
    language: string;
    encoding: string;
    lines: number;
    size: number;
  };
}
```

### 3. 파일 탐색기 API 서비스
```typescript
// frontend/src/services/api.ts에 추가된 API 함수들

export const fileExplorerApi = {
  // 파일 트리 구조 조회
  getFileTree: (repositoryId: string, options?: {...}) => {...},
  
  // 디렉토리 내용 조회 (지연 로딩용)
  getDirectoryContents: (repositoryId: string, path: string) => {...},
  
  // 파일 메타데이터 조회
  getFileInfo: (repositoryId: string, fileId: string) => {...},
  
  // 파일 내용 조회
  getFileContent: (repositoryId: string, fileId: string) => {...},
  
  // 파일 내용 업데이트
  updateFileContent: (repositoryId: string, fileId: string, content: string) => {...},
  
  // 파일 검색
  searchFiles: (repositoryId: string, query: string, type?: string) => {...},
};
```

### 4. VSCode 스타일 파일 트리 컴포넌트
**파일**: `frontend/src/components/features/FileTree.tsx`

**주요 특징**:
- 계층적 폴더/파일 구조 표시
- 파일 타입별 아이콘과 색상 구분
- 폴더 확장/축소 기능
- 선택된 파일 하이라이팅
- 지연 로딩 지원

**지원하는 파일 타입**:
- **코드 파일**: JS/TS (노란색), Python/Ruby/Java 등 (초록색)
- **마크업**: HTML/XML (주황색), CSS/SCSS (파란색)
- **설정 파일**: JSON/YAML (회색)
- **문서**: Markdown/텍스트 (회색)
- **미디어**: 이미지 (보라색), 비디오 (빨간색)
- **압축 파일**: ZIP/RAR 등 (황갈색)

### 5. Monaco Editor 컴포넌트
**파일**: `frontend/src/components/features/CodeEditor.tsx`

**주요 특징**:
- 문법 하이라이팅 지원 (JS, TS, Python, Ruby 등)
- 읽기 전용 모드 (수정 기능은 향후 추가 예정)
- 파일 정보 헤더 (이름, 경로, 라인 수, 크기, 언어)
- VSCode와 유사한 테마와 설정
- 미니맵, 라인 번호, 폴딩 등 고급 기능

**Monaco Editor 설정**:
```typescript
options={{
  readOnly: true,
  minimap: { enabled: true },
  fontSize: 14,
  lineNumbers: 'on',
  rulers: [80, 120],
  wordWrap: 'on',
  folding: true,
  contextmenu: true,
  mouseWheelZoom: true,
  // ... 기타 VSCode 스타일 설정들
}}
```

### 6. 메인 Code Explorer 페이지
**파일**: `frontend/src/components/features/CodeExplorer.tsx`

**레이아웃**:
- **상단**: 제목과 설명
- **중간**: Repository 선택 및 검색 컨트롤
- **하단**: 좌우 분할 레이아웃
  - **왼쪽 (1/4)**: 파일 트리
  - **오른쪽 (3/4)**: Monaco Editor

**기능**:
- Repository 자동 선택 (ready 상태의 첫 번째 저장소)
- 파일 트리 새로고침
- 파일 검색 UI (백엔드 연동 대기)
- 반응형 레이아웃 (모바일/태블릿 지원)

## 🔧 기술적 구현 세부사항

### 언어 감지 로직
```typescript
const getLanguageFromExtension = (fileName: string): string => {
  const extension = fileName.split('.').pop()?.toLowerCase();
  const languageMap: { [key: string]: string } = {
    js: 'javascript',
    jsx: 'javascript',
    ts: 'typescript',
    tsx: 'typescript',
    py: 'python',
    rb: 'ruby',
    // ... 30+ 언어 지원
  };
  return languageMap[extension || ''] || 'plaintext';
};
```

### 파일 아이콘 매핑
```typescript
const getFileIcon = (fileName: string, isDirectory: boolean) => {
  // 디렉토리: Folder/FolderOpen 아이콘
  // 파일: 확장자별로 다른 아이콘과 색상 반환
  // 지원: FileCode, FileText, FileImage, FileVideo, FileArchive 등
};
```

### 상태 관리
- **React Query**: API 데이터 캐싱 및 상태 관리
- **Local State**: 선택된 파일, 확장된 폴더 등 UI 상태
- **Auto-refresh**: Repository 변경 시 자동 데이터 갱신

## 🎨 UI/UX 특징

### 디자인 시스템
- **폰트**: Google Fonts 기반 (font-heading, font-body)
- **색상**: 검은색 텍스트 기반 고대비 디자인
- **간격**: 타이트한 레이아웃으로 공간 효율성 극대화
- **반응형**: 브라우저 너비에 따른 적응형 레이아웃

### 사용자 경험
- **직관적 네비게이션**: VSCode와 동일한 UX 패턴
- **빠른 로딩**: 지연 로딩과 캐싱으로 성능 최적화
- **키보드 지원**: Monaco Editor의 모든 키보드 단축키 지원
- **접근성**: 스크린 리더 및 키보드 네비게이션 지원

## 📁 파일 구조

```
frontend/src/
├── routes/
│   └── code.tsx                    # Code 페이지 라우트
├── components/
│   ├── layout/
│   │   └── Sidebar.tsx            # Code 메뉴 추가
│   └── features/
│       ├── CodeExplorer.tsx       # 메인 페이지 컴포넌트
│       ├── FileTree.tsx           # 파일 트리 컴포넌트
│       └── CodeEditor.tsx         # Monaco Editor 컴포넌트
├── services/
│   └── api.ts                     # 파일 탐색기 API 함수들
└── types/
    └── index.ts                   # 파일 탐색기 타입 정의
```

## 🚀 다음 단계

### 백엔드 연동 필요
1. **API 엔드포인트 구현**: `md/backend-file-explorer-api.md` 참조
2. **데이터베이스 마이그레이션**: 필요시 스키마 업데이트
3. **실제 데이터 연동**: 현재는 목업 데이터로 테스트

### 향후 개선 사항
1. **파일 편집 기능**: 읽기 전용에서 편집 가능으로 확장
2. **실시간 협업**: WebSocket 기반 다중 사용자 편집
3. **고급 검색**: 정규식, 전문 검색 등
4. **Git 통합**: 파일별 Git 상태 표시

## ✅ 테스트 방법

1. 프론트엔드 서버 실행
2. 브라우저에서 `/code` 경로 접속
3. Repository 선택 후 파일 트리 확인
4. 파일 클릭하여 Monaco Editor에서 내용 확인

현재 백엔드 API가 구현되지 않은 상태이므로 실제 파일 데이터는 표시되지 않지만, UI 구조와 상호작용은 완전히 동작합니다.
