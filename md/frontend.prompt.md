# React + RSBuild + TanStack 프론트엔드 프로젝트 생성 가이드

## 프로젝트 설정

### 1. 초기 프로젝트 생성 및 패키지 설치

```bash
# 프로젝트 폴더 생성 및 초기화
npm init -y

# RSBuild 및 React 설정
npm install @rsbuild/core @rsbuild/plugin-react @rsbuild/plugin-sass --save-dev
npm install react react-dom

# TanStack 라이브러리
npm install @tanstack/react-router @tanstack/react-query @tanstack/router-plugin

# UI 및 스타일링
npm install tailwindcss @tailwindcss/postcss autoprefixer postcss sass --save-dev
npm install tailwind-merge clsx class-variance-authority tailwindcss-animate
npm install @radix-ui/react-slot @radix-ui/react-dialog @radix-ui/react-label @radix-ui/react-select @radix-ui/react-icons

# Form 및 유틸리티
npm install react-hook-form @hookform/resolvers zod
npm install axios react-hot-toast lucide-react

# 상태 관리
npm install zustand

# 개발 도구
npm install typescript @types/react @types/react-dom @types/node eslint prettier --save-dev
```

### 2. 프로젝트 구조 생성

```
src/
├── app/                    # 페이지 컴포넌트들
│   ├── HomePage.tsx        # 메인 페이지
│   └── dashboard/          # 대시보드 관련 페이지들
├── routes/                 # 파일 기반 라우팅
│   ├── __root.tsx         # 루트 라우트
│   ├── index.tsx          # 홈 페이지 라우트
│   └── dashboard/         # 대시보드 라우트들
├── components/            # 재사용 가능한 컴포넌트들
│   ├── Layout/           # 레이아웃 컴포넌트들
│   │   ├── Layout.tsx    # 메인 레이아웃
│   │   ├── Header.tsx    # 헤더 컴포넌트
│   │   ├── Sidebar.tsx   # 사이드바 컴포넌트
│   │   └── Content.tsx   # 콘텐츠 컴포넌트
│   └── ui/               # UI 컴포넌트들
├── hooks/                # 커스텀 훅들
│   └── useAuth.tsx       # 인증 관련 훅
├── services/             # API 서비스들
├── types/                # TypeScript 타입 정의
├── constants/            # 상수들
└── utils/                # 유틸리티 함수들
```

### 3. RSBuild 설정 (rsbuild.config.ts)

```typescript
import path from 'path';
import { defineConfig } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSass } from '@rsbuild/plugin-sass';
import { tanstackRouter } from '@tanstack/router-plugin/rspack';

export default defineConfig({
  plugins: [pluginReact(), pluginSass()],
  source: {
    entry: { index: './src/index.tsx' },
  },
  resolve: {
    alias: { '@': path.resolve(__dirname, './src') },
  },
  tools: {
    rspack: {
      plugins: [
        tanstackRouter({
          routesDirectory: './src/routes',
          generatedRouteTree: './src/routeTree.gen.ts',
          target: 'react',
          autoCodeSplitting: true,
        }),
      ],
    },
  },
});
```

### 4. 파일 기반 라우팅 설정

**src/routes/__root.tsx** (루트 레이아웃):
```tsx
import { createRootRoute, Outlet } from '@tanstack/react-router';
import { Layout } from '@/components/Layout';

export const Route = createRootRoute({
  component: () => (
    <Layout>
      <Outlet />
    </Layout>
  ),
});
```

**src/routes/index.tsx** (홈 페이지):
```tsx
import { createFileRoute } from '@tanstack/react-router';
import { HomePage } from '@/app/HomePage';

export const Route = createFileRoute('/')({
  component: HomePage,
});
```

### 5. Header + Sidebar + Content 레이아웃 구현

**src/components/Layout/Layout.tsx**:
```tsx
import React, { ReactNode, useState } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import styles from './Layout.module.scss';

interface LayoutProps {
  children: ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);

  return (
    <div className={styles.root}>
      <div className={styles.container}>
        <Header onToggleSidebar={() => setIsSidebarVisible(!isSidebarVisible)} />
        <div className={styles.mainContent}>
          <div className={`${styles.sidebarContainer} ${!isSidebarVisible ? styles.hidden : ''}`}>
            <Sidebar />
          </div>
          <div className={styles.content}>
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};
```

**src/components/Layout/Layout.module.scss** (Content 영역만 스크롤):
```scss
.root {
  display: flex;
  width: 100%;
  height: 100vh;
  flex-direction: column;
  overflow: hidden; // 전체 스크롤 방지
}

.container {
  display: flex;
  height: 100%;
  flex-direction: column;
}

.mainContent {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px); // Header 높이 제외
  overflow: hidden;
}

.sidebarContainer {
  width: 240px;
  flex-shrink: 0;
  height: 100%;
  background-color: #ffffff;
  overflow-y: auto; // 사이드바 내부 스크롤
}

.content {
  flex: 1;
  padding: 1.5rem;
  height: 100%; // 고정 높이
  overflow-y: auto; // 콘텐츠 영역만 스크롤
  overflow-x: hidden;
}
```

### 6. TypeScript 경로 별칭 설정 (tsconfig.json)

```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@/components/*": ["./src/components/*"],
      "@/utils/*": ["./src/utils/*"],
      "@/types/*": ["./src/types/*"]
    }
  }
}
```

### 7. TanStack Query 설정

**src/router.tsx**:
```tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createRouter, RouterProvider } from '@tanstack/react-router';
import { routeTree } from './routeTree.gen';

const queryClient = new QueryClient();
const router = createRouter({ routeTree });

export function Router() {
  return (
    <QueryClientProvider client={queryClient}>
      <RouterProvider router={router} />
    </QueryClientProvider>
  );
}
```

### 8. 인증 컨텍스트 설정

**src/hooks/useAuth.tsx**:
```tsx
import React, { createContext, useContext, useState } from 'react';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (credentials: LoginData) => Promise<void>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);

  const login = async (credentials: LoginData) => {
    // 로그인 로직 구현
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('access_token');
  };

  return (
    <AuthContext.Provider value={{ user, isAuthenticated: !!user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuth must be used within AuthProvider');
  return context;
};
```

### 9. 패키지 스크립트 설정

**package.json**:
```json
{
  "scripts": {
    "dev": "rsbuild dev",
    "build": "rsbuild build",
    "preview": "rsbuild preview"
  }
}
```

이 구조를 따라하면 RSBuild, TanStack Router/Query를 활용한 파일 기반 라우팅과 Header+Sidebar+Content 레이아웃을 가진 모던 React 애플리케이션을 구축할 수 있습니다.
