# AI Engine (aiend) - 프로젝트 개요

## 📋 프로젝트 정보

- **이름**: CodeBase Intelligence AI Engine (aiend)
- **기술 스택**: Python 3.11, FastAPI, SQLite, ChromaDB, Ollama
- **목적**: LLM 페어프로그래밍 최적화를 위한 AI 엔진
- **아키텍처**: 레이어드 아키텍처 (4계층)

## 🏗️ 아키텍처 개요

### 레이어드 아키텍처 (4계층)

1. **LLM Integration Layer**
   - OpenAI, Anthropic, Ollama 통합
   - 적응형 LLM 라우팅
   - 멀티 LLM 오케스트레이션

2. **Context Optimization Layer**
   - 쿼리 의도 분석
   - 컨텍스트 빌더
   - 토큰 예산 관리
   - 의미적 압축

3. **Intelligence Layer**
   - 의미적 검색 (Semantic Search)
   - 구조적 분석 (Structural Analysis)
   - 시간적 분석 (Temporal Analysis)
   - 패턴 학습 (Pattern Learning)

4. **Data Layer**
   - SQLite 메타데이터 관리
   - Vector DB (ChromaDB) 임베딩 저장
   - Git 히스토리 분석
   - 코드베이스 인덱싱

## 🚀 핵심 기능

### 1. 지능형 컨텍스트 생성 (Smart Context Generation)
- 사용자 쿼리 분석
- 관련 코드 자동 수집
- 토큰 예산에 맞는 최적화
- 의미적 압축

### 2. 시간 여행 코드 분석 (Time-Travel Code Analysis)
- Git 히스토리 기반 분석
- 코드 변경 패턴 학습
- 버그 수정 이력 추적
- 코드 진화 분석

### 3. 적응형 LLM 라우팅 (Adaptive LLM Routing)
- 작업 타입별 최적 LLM 선택
- 멀티 LLM 결과 통합
- 신뢰도 기반 응답 생성
- 비용 최적화

### 4. 프로액티브 코드 인사이트 (Proactive Code Insights)
- 코드 품질 분석
- 잠재적 버그 탐지
- 리팩토링 제안
- 성능 최적화 힌트

## 📁 프로젝트 구조

```
aiend/
├── src/codebase_intelligence/
│   ├── core/                    # 핵심 비즈니스 로직
│   │   ├── data/               # Data Layer
│   │   ├── intelligence/       # Intelligence Layer
│   │   ├── context/           # Context Optimization Layer
│   │   └── llm/               # LLM Integration Layer
│   ├── api/                   # FastAPI 서버
│   ├── cli/                   # CLI 인터페이스
│   └── utils/                 # 유틸리티
├── tests/                     # 테스트 코드
├── docs/                      # 문서
├── config/                    # 설정 파일
└── examples/                  # 예제 코드
```

## 🔧 기술 스택 상세

### 백엔드 프레임워크
- **FastAPI**: 고성능 비동기 웹 프레임워크
- **Uvicorn**: ASGI 서버
- **Pydantic**: 데이터 검증 및 직렬화

### 데이터베이스
- **SQLite**: 메타데이터 저장
- **ChromaDB**: 벡터 임베딩 저장
- **SQLAlchemy**: ORM

### LLM 통합
- **OpenAI**: GPT-4, GPT-3.5-turbo
- **Anthropic**: Claude-3 시리즈
- **Ollama**: 로컬 LLM (CodeLlama, Mistral 등)

### 코드 분석
- **Tree-sitter**: 구문 분석
- **GitPython**: Git 히스토리 분석
- **AST**: Python 추상 구문 트리

### 머신러닝
- **Sentence Transformers**: 텍스트 임베딩
- **NumPy**: 수치 계산
- **Scikit-learn**: 머신러닝 (예정)

## 🌟 주요 특징

### 1. 모듈화된 설계
- 각 레이어가 독립적으로 동작
- 쉬운 테스트 및 확장
- 플러그인 아키텍처

### 2. 비동기 처리
- FastAPI 기반 비동기 API
- 동시 다중 LLM 호출
- 백그라운드 작업 지원

### 3. 확장성
- 새로운 LLM 제공자 쉽게 추가
- 다양한 프로그래밍 언어 지원
- 플러그인 시스템

### 4. 성능 최적화
- 벡터 검색 최적화
- 토큰 사용량 최적화
- 캐싱 시스템

## 🎯 사용 사례

### 1. 코드 검색 및 탐색
```python
# 의미적 검색
search_results = smart_search.search("authentication function")

# 구조적 검색
search_results = smart_search.search("UserService를 상속받는 클래스")

# 시간적 검색
search_results = smart_search.search("최근에 수정된 버그 수정")
```

### 2. LLM 질문 답변
```python
# 컨텍스트 기반 질문
response = await orchestrator.process_query(
    "이 코드의 보안 취약점을 찾아주세요",
    max_tokens=8000
)
```

### 3. 코드베이스 분석
```python
# 전체 코드베이스 인덱싱
stats = analyzer.analyze_repository()

# 특정 파일 분석
file_analysis = analyzer.analyze_file("src/main.py")
```

## 🔮 향후 계획

### Phase 1: 기반 구축 ✅
- SQLite 인덱싱
- Git 히스토리 분석
- 기본 벡터 검색
- FastAPI 서버

### Phase 2: 지능화 🔄
- 멀티 LLM 통합
- 고급 의미적 검색
- 버그 패턴 학습
- 성능 최적화

### Phase 3: 생태계 확장 📋
- IDE 플러그인
- 팀 협업 기능
- 엔터프라이즈 기능
- 클라우드 배포

## 📊 성능 지표

### 목표 성능
- **검색 응답 시간**: < 500ms
- **LLM 응답 시간**: < 30초
- **인덱싱 속도**: 1000 파일/분
- **메모리 사용량**: < 2GB

### 확장성 목표
- **동시 사용자**: 100명
- **코드베이스 크기**: 100만 라인
- **지원 언어**: 10개 이상
- **LLM 제공자**: 5개 이상
