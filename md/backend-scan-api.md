# Backend Aiend API 작업 내용

## 개요
repository_branch_id 형태로 특정 리포지토리의 특정 브랜치만 스캔하여 Qdrant collections에 벡터 임베딩을 생성하는 새로운 API를 개발했습니다.

## 구현된 기능

### 1. Aiend API 엔드포인트 추가
**파일**: `aiend/src/codebase_intelligence/api/routers/webhook.py`

#### 새로운 엔드포인트
```python
@router.post("/repository-branch-scan", response_model=WebhookResponse)
async def trigger_repository_branch_scan(
    repository_branch_id: str = Query(..., description="Repository Branch ID (format: repository_id_branch)"),
    force_reindex: bool = False,
    config: Config = Depends(get_config)
):
```

#### repository_branch_id 파싱 함수
```python
def parse_repository_branch_id(repository_branch_id: str) -> tuple[str, str]:
    """
    repository_branch_id를 파싱하여 repository_id와 branch를 분리
    '_' 문자를 오른쪽부터 찾아서 마지막 '_' 이후를 branch로, 그 앞을 repository_id로 분리
    """
    if '_' not in repository_branch_id:
        raise ValueError(f"Invalid repository_branch_id format: {repository_branch_id}")
    
    # 오른쪽부터 첫 번째 '_' 찾기
    last_underscore_index = repository_branch_id.rfind('_')
    
    repository_id = repository_branch_id[:last_underscore_index]
    branch = repository_branch_id[last_underscore_index + 1:]
    
    if not repository_id or not branch:
        raise ValueError(f"Invalid repository_branch_id format: {repository_branch_id}")
    
    return repository_id, branch
```

#### 파싱 예시
- `"123_main"` → repository_id: `"123"`, branch: `"main"`
- `"my_repo_123_feature_branch"` → repository_id: `"my_repo_123"`, branch: `"feature_branch"`
- `"repo_with_underscores_456_dev_test"` → repository_id: `"repo_with_underscores_456"`, branch: `"dev_test"`

### 2. Backend 서비스 메서드 추가
**파일**: `backend/app/services/aiend_webhook_service.rb`

#### 새로운 서비스 메서드
```ruby
def self.trigger_repository_branch_scan(repository_branch_id, force_reindex: false)
  """
  repository_branch_id 형태로 특정 리포지토리의 특정 브랜치 스캔을 Aiend에 요청
  """
  
  return false unless aiend_available?
  
  begin
    query_params = {
      repository_branch_id: repository_branch_id,
      force_reindex: force_reindex
    }

    Rails.logger.info "Triggering repository branch scan in Aiend for: #{repository_branch_id}"

    response = HTTParty.post(
      "#{AIEND_BASE_URL}/api/v1/webhook/repository-branch-scan",
      query: query_params,
      headers: {
        'Content-Type' => 'application/json',
        'Accept' => 'application/json'
      },
      timeout: WEBHOOK_TIMEOUT
    )

    if response.success?
      Rails.logger.info "Aiend repository branch scan triggered successfully"
      response.parsed_response
    else
      Rails.logger.error "Aiend repository branch scan trigger failed: #{response.code} - #{response.body}"
      false
    end
  rescue => e
    Rails.logger.error "Aiend repository branch scan error: #{e.message}"
    false
  end
end
```

### 3. Backend 컨트롤러 엔드포인트 추가
**파일**: `backend/app/controllers/api/repositories_controller.rb`

#### 새로운 컨트롤러 액션
```ruby
def trigger_repository_branch_id_scan
  """repository_branch_id 형태로 Aiend 스캔 트리거"""

  repository_branch_id = params[:repository_branch_id]
  force_reindex = params[:force_reindex] == 'true'

  if repository_branch_id.blank?
    render_error("repository_branch_id parameter is required")
    return
  end

  begin
    # repository_branch_id 스캔 트리거
    result = AiendWebhookService.trigger_repository_branch_scan(repository_branch_id, force_reindex: force_reindex)

    if result
      # repository_branch_id에서 repository_id 추출 (오른쪽부터 첫 번째 '_' 이전까지)
      repository_id = repository_branch_id.split('_')[0..-2].join('_')
      
      # 해당 repository가 존재하는 경우 업데이트
      if repository = Repository.find_by(id: repository_id)
        repository.update!(
          last_aiend_scan_at: Time.current,
          aiend_scan_status: 'triggered',
          aiend_task_id: result['task_id']
        )

        # 작업 모니터링 시작
        if result['task_id']
          AiendTaskMonitorJob.perform_later(repository.id, result['task_id'])
        end
      end

      render_success(
        {
          repository_branch_id: repository_branch_id,
          aiend_task: result
        },
        "Aiend repository branch scan triggered successfully for: #{repository_branch_id}"
      )
    else
      render_error("Failed to trigger Aiend repository branch scan")
    end
  rescue => e
    Rails.logger.error "Failed to trigger Aiend repository branch scan: #{e.message}"
    render_error("Failed to trigger Aiend repository branch scan: #{e.message}")
  end
end
```

### 4. 라우트 설정
**파일**: `backend/config/routes.rb`

```ruby
resources :repositories do
  member do
    # 기존 라우트들...
    get :aiend_status, path: 'aiend-status'
    post :trigger_aiend_scan, path: 'aiend-scan'
    post :trigger_aiend_branch_scan, path: 'aiend-branch-scan'
  end
  
  collection do
    post :trigger_repository_branch_id_scan, path: 'repository-branch-id-scan'
  end
end
```

### 5. Frontend API 서비스 추가
**파일**: `frontend/src/services/api.ts`

```typescript
// Repository management methods
export const repositoryApi = {
  // 기존 메서드들...
  
  // Trigger repository branch scan using repository_branch_id format
  triggerRepositoryBranchIdScan: (repositoryBranchId: string, forceReindex: boolean = false) =>
    apiService.post('/repositories/repository-branch-id-scan', { 
      repository_branch_id: repositoryBranchId, 
      force_reindex: forceReindex 
    }),
};
```

### 6. Frontend ReposPage 수정
**파일**: `frontend/src/components/features/ReposPage.tsx`

#### 변경된 handleAnalyze 함수
```typescript
const handleAnalyze = async () => {
  // ... 기존 코드 ...

  try {
    console.log('[ReposPages] repository_branch_id 스캔 호출 시작');
    
    // repository_branch_id 형태로 구성 (repository_name_branch_repository_id)
    const repositoryBranchId = `${selectedRepo.name}_${selectedBranch}_${selectedRepo.id}`;
    console.log('[ReposPages] 생성된 repository_branch_id:', repositoryBranchId);
    
    const response = await repositoryApi.triggerRepositoryBranchIdScan(repositoryBranchId, true);
    console.log('[ReposPages] repository_branch_id 스캔 응답:', response);

    // ... 응답 처리 코드 ...
  } catch (error) {
    // ... 오류 처리 코드 ...
  }
};
```

## API 사용 방법

### 1. Frontend에서 사용
```typescript
// ReposPage에서 리포지토리와 브랜치 선택 후 "Analyze" 버튼 클릭
// 내부적으로 repositoryApi.triggerRepositoryBranchIdScan() 호출
```

### 2. Backend API 직접 호출
```bash
curl -X POST "http://localhost:3000/api/repositories/repository-branch-id-scan" \
  -H "Content-Type: application/json" \
  -d '{"repository_branch_id": "tauri-translator_master_9", "force_reindex": true}'
```

### 3. Aiend API 직접 호출
```bash
curl -X POST "http://localhost:7102/api/v1/webhook/repository-branch-scan?repository_branch_id=tauri-translator_master_9&force_reindex=true"
```

## 처리 흐름

1. **Frontend**: 사용자가 리포지토리와 브랜치 선택 후 "Analyze" 버튼 클릭
2. **Frontend**: `repository_name_branch_repository_id` 형태로 repository_branch_id 생성
3. **Backend**: `/api/repositories/repository-branch-id-scan` 엔드포인트로 요청 수신
4. **Backend**: `AiendWebhookService.trigger_repository_branch_scan()` 호출
5. **Aiend**: `/api/v1/webhook/repository-branch-scan` 엔드포인트에서 요청 처리
6. **Aiend**: `parse_repository_branch_id()` 함수로 repository_id와 branch 분리
7. **Aiend**: `/app/repos/{repository_id}` 경로에서 해당 브랜치 체크아웃
8. **Aiend**: 파일 스캔 및 벡터 임베딩 생성
9. **Aiend**: Qdrant에 `repo_{repository_id}_branch_{safe_branch}` collection 생성

## 주요 특징

1. **기존 API 유지**: 전체 스캔 API (`/repository-scan`)는 그대로 보존
2. **유연한 파싱**: '_' 문자가 여러 개 있어도 오른쪽부터 파싱하여 정확히 분리
3. **repos 폴더 구조 지원**: `/app/repos/{repository_id}` 형태의 폴더 구조 자동 지원
4. **브랜치별 컬렉션**: Qdrant에서 브랜치별로 별도 컬렉션 생성
5. **간단한 API 호출**: 복잡한 페이로드 대신 repository_branch_id 하나로 모든 정보 전달

## 개선사항

- 기존: 복잡한 페이로드 (repository_path, repository_name, repository_id, branch 등)
- 현재: 간단한 repository_branch_id 하나로 모든 정보 전달
- 더 직관적이고 사용하기 쉬운 API 구조
