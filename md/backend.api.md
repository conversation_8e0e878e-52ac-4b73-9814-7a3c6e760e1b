# Backend API Documentation

이 문서는 Codebase Intelligence 백엔드(Ruby on Rails)에서 제공하는 RESTful API를 설명합니다.

## 1. Repositories API (`/api/repositories`)

Git 저장소의 생성, 조회, 관리 및 상호작용을 위한 엔드포인트를 제공합니다.

---

### `GET /api/repositories` - List Repositories

시스템에 등록된 모든 저장소의 목록을 반환합니다.

**Response Body:**
```json
[
  {
    "id": "integer",
    "name": "string",
    "path": "string",
    "created_at": "datetime",
    "updated_at": "datetime"
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories"
```

---

### `POST /api/repositories/clone` - Clone Repository

원격 Git URL로부터 새로운 저장소를 클론합니다.

**Request Body:**
```json
{
  "url": "string"
}
```

**Response Body:**
```json
{
  "id": "integer",
  "name": "string",
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/repositories/clone" \
-H "Content-Type: application/json" \
-d '{"url": "https://github.com/user/repo.git"}'
```

---

### `GET /api/repositories/:id` - Show Repository

특정 저장소의 상세 정보를 반환합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Response Body:**
```json
{
  "id": "integer",
  "name": "string",
  "path": "string",
  "branches": "array[string]",
  "current_branch": "string",
  "status": "object"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories/1"
```

---

### `POST /api/repositories/:id/scan` - Scan Repository

저장소의 전체 파일을 스캔하여 `aiend` 서비스에 분석을 요청합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Response Body:**
```json
{
  "success": "boolean",
  "message": "string",
  "task_id": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/repositories/1/scan"
```

---

### `GET /api/repositories/:id/tree` - Get File Tree

저장소의 파일 및 디렉토리 구조를 트리 형태로 반환합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Response Body:**
```json
[
  {
    "name": "string",
    "path": "string",
    "type": "string (file/directory)",
    "children": "array (recursive)"
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories/1/tree"
```

---

### `GET /api/repositories/:id/branches` - List Branches

저장소의 모든 브랜치 목록을 반환합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Response Body:**
```json
{
  "branches": "array[string]",
  "current_branch": "string"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories/1/branches"
```

---

### `POST /api/repositories/:id/switch-branch` - Switch Branch

저장소의 현재 브랜치를 변경합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Request Body:**
```json
{
  "branch": "string"
}
```

**Response Body:**
```json
{
  "success": "boolean",
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/repositories/1/switch-branch" \
-H "Content-Type: application/json" \
-d '{"branch": "develop"}'
```

---

### `POST /api/repositories/:id/pull` - Pull Changes

원격 저장소로부터 최신 변경사항을 가져옵니다(pull).

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Response Body:**
```json
{
  "success": "boolean",
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/repositories/1/pull"
```

---

### `GET /api/repositories/:id/commits` - List Commits

저장소의 커밋 히스토리를 반환합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID

**Response Body:**
```json
[
  {
    "sha": "string",
    "author": "string",
    "date": "datetime",
    "message": "string"
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories/1/commits"
```

---

### `GET /api/repositories/:id/commits/:commit_hash/diff` - Get Commit Diff

특정 커밋의 변경사항(diff)을 반환합니다.

**Path Parameters:**
- `id`: `integer` - 저장소 ID
- `commit_hash`: `string` - 커밋 해시

**Response Body:**
```json
{
  "diff": "string"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories/1/commits/a1b2c3d4/diff"
```

## 2. Files API (`/api/repositories/:repository_id/files`)

저장소 내의 파일 검색 및 내용 조회를 위한 엔드포인트를 제공합니다.

---

### `GET /api/repositories/:repository_id/files/search` - Search Files

특정 저장소 내에서 파일을 검색합니다.

**Path Parameters:**
- `repository_id`: `integer` - 저장소 ID

**Query Parameters:**
- `query`: `string` - 검색어

**Response Body:**
```json
[
  {
    "path": "string",
    "snippet": "string",
    "line_number": "integer"
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/repositories/1/files/search?query=my_function"
```

---

### `GET /api/files/search` - Global File Search

모든 저장소에서 파일을 검색합니다.

**Query Parameters:**
- `query`: `string` - 검색어

**Response Body:**
```json
[
  {
    "repository_name": "string",
    "path": "string",
    "snippet": "string",
    "line_number": "integer"
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/files/search?query=important_config"
```

## 3. Search API (`/api/search`)

벡터 임베딩 및 리랭킹 기반의 검색 기능을 제공합니다. Ollama의 임베딩/리랭커 모델과 Qdrant 벡터 DB를 활용하여 쿼리와 가장 유사한 문서들을 반환합니다.

---

### `POST /api/search` - 벡터 기반 코드/문서 검색

쿼리(질문)에 대해 벡터 임베딩 → Qdrant 벡터 검색 → 리랭킹을 거쳐 최종적으로 관련 문서(코드/텍스트) 목록을 반환합니다.

**Request Body:**
```json
{
  "query": "string",           // 검색 쿼리(필수)
  "collection": "string",      // Qdrant 컬렉션명(선택, 기본값: codebase_files)
  "top_k": 10                   // 반환할 최대 결과 개수(선택, 기본값: 10)
}
```

**Response Body:**
```json
{
  "query": "string",
  "results": [
    {
      "score": 0.9876,            // 리랭커 점수(높을수록 유사)
      "payload": {
        "text": "string",        // 원문 텍스트/코드 등
        // ... 기타 Qdrant에 저장된 payload 필드
      },
      "id": "string"             // Qdrant 포인트 ID
    }
  ]
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/search" \
     -H "Content-Type: application/json" \
     -d '{"query": "redis 연결 방법?"}'
```

**동작 흐름:**
1. 쿼리 텍스트를 Ollama 임베딩 모델(`dengcao/Qwen3-Embedding-8B`)로 임베딩 벡터화.
2. Qdrant에서 벡터 유사도 기반으로 top_k개 문서 검색.
3. Ollama 리랭커 모델(`dengcao/Qwen3-Reranker-4B`)로 쿼리와 각 문서의 관련성 점수 산출 및 내림차순 정렬.
4. 최종적으로 점수, payload, ID 포함 결과 반환.

**환경 변수:**
- `OLLAMA_HOST`: Ollama API 주소 (예: `http://host.docker.internal:11434`)
- `QDRANT_URL`: Qdrant API 주소 (예: `http://qdrant:6333`)
- `CODEBASE_VECTOR_MODEL`: 임베딩 모델명 (예: `dengcao/Qwen3-Embedding-8B:Q5_K_M`)
- `RERANKER_MODEL`: 리랭커 모델명 (예: `dengcao/Qwen3-Reranker-4B:Q5_K_M`)

---

## 4. Health Check API

백엔드 서비스의 상태를 확인합니다.

---

### `GET /up` - Rails Health Check

Rails 애플리케이션의 기본 상태를 확인합니다.

**Response Body:** (No body, only status 200 OK)

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/up"
```

---

### `GET /api/health` - API Health Check

API 서비스의 상태를 확인합니다.

**Response Body:**
```json
{
  "status": "ok"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/health"
```

---

## 5. Codebase API (`/api/codebase`)

저장소의 코드베이스 인덱싱 및 파일/디렉토리 정보 조회를 위한 엔드포인트를 제공합니다.

---

### `GET /api/codebase/files` - List Files

특정 저장소와 브랜치의 파일 목록을 반환합니다. 경로(path)로 필터링할 수 있습니다.

**Query Parameters:**
- `repository_id` 또는 `repository_name`: `integer` 또는 `string` - 저장소 ID 또는 이름 (둘 중 하나 필수)
- `branch`: `string` - 브랜치 이름 (기본값: 저장소의 기본 브랜치)
- `path`: `string` - 필터링할 디렉토리 경로.
  - 루트 디렉토리의 파일들을 조회하려면 `.` 또는 `/`를 사용합니다.
  - 하위 디렉토리는 저장소 루트로부터의 절대 경로(예: `/backend/app/jobs`)를 사용해야 합니다.

**Response Body:**
```json
[
  {
    "id": "integer",
    "repository_id": "integer",
    "branch": "string",
    "path": "string",
    "filename": "string",
    "extension": "string",
    "depth": "integer",
    "content": "string",
    "hash_id": "string",
    "size": "integer",
    "is_binary": "boolean"
  }
]
```

**Example cURL:**
```bash
# 루트 디렉토리 파일 조회
curl "http://localhost:7101/api/codebase/files?repository_name=codebase-sqlite&branch=master&path=."

# 특정 경로의 파일 조회
curl "http://localhost:7101/api/codebase/files?repository_name=codebase-sqlite&branch=master&path=/backend/app/jobs"
```

---

### `POST /api/codebase/index_by_name` - Index Repository by Name

저장소 이름과 브랜치 이름으로 코드베이스 인덱싱을 실행합니다.

**Request Body:**
```json
{
  "repository_name": "string",
  "branch": "string"
}
```

**Response Body:**
```json
{
  "message": "string",
  "job_id": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/codebase/index_by_name" \
-H "Content-Type: application/json" \
-d '{"repository_name": "codebase-sqlite", "branch": "master"}'
```

---

### `GET /api/codebase/indexing_status/:job_id` - Check Indexing Status

코드베이스 인덱싱 작업의 상태를 확인합니다.

**Path Parameters:**
- `job_id`: `string` - `index_by_name` 호출 시 반환된 작업 ID

**Response Body:**
```json
{
  "status": "string (e.g., 'queued', 'running', 'completed', 'failed')"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7101/api/codebase/indexing_status/a1b2c3d4-e5f6-7890-1234-567890abcdef"
```

---

### `POST /api/codebase/sql_query` - Execute SQL Query

데이터베이스에 대해 직접 SQL `SELECT` 쿼리를 실행하고 결과를 JSON으로 반환합니다. 보안상의 이유로 `SELECT` 문만 허용됩니다.

**Request Body:**
```json
{
  "sql": "string"
}
```

**Response Body:**
```json
[
  {
    "column1": "value1",
    "column2": "value2"
  }
]
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7101/api/codebase/sql_query" \
-H "Content-Type: application/json" \
-d '{"sql": "SELECT * FROM codebase_files WHERE branch = \'master\' LIMIT 5"}'
```

---

## API Endpoint Summary

아래는 현재 백엔드에서 제공하는 주요 API URL 목록입니다.

### Repositories API
- `GET    /api/repositories` : 저장소 목록 조회
- `POST   /api/repositories/clone` : 원격 저장소 클론
- `GET    /api/repositories/:id` : 특정 저장소 상세
- `POST   /api/repositories/:id/scan` : 저장소 전체 파일 분석 요청
- `GET    /api/repositories/:id/tree` : 저장소 파일/디렉토리 트리
- `GET    /api/repositories/:id/branches` : 저장소 브랜치 목록
- `POST   /api/repositories/:id/switch-branch` : 브랜치 변경
- `POST   /api/repositories/:id/pull` : 원격 저장소 pull
- `GET    /api/repositories/:id/commits` : 커밋 히스토리
- `GET    /api/repositories/:id/commits/:commit_hash/diff` : 커밋 diff

### Files API
- `GET    /api/repositories/:repository_id/files/search` : 저장소 내 파일 검색
- `GET    /api/files/search` : 전체 저장소 파일 검색

### Codebase API
- `GET    /api/codebase/files` : 저장소 파일 목록 조회 (경로 필터링 포함)
- `POST   /api/codebase/index_by_name` : 이름으로 저장소 인덱싱
- `GET    /api/codebase/indexing_status/:job_id` : 인덱싱 상태 확인
- `POST   /api/codebase/sql_query` : SQL 쿼리 실행

### Search API (Vector-based)
- `POST   /api/search` : 임베딩+Qdrant+리랭킹 기반 검색

### Health Check API
- `GET    /up` : Rails 기본 헬스체크
- `GET    /api/health` : API 헬스체크

---

## 6. Codebase API 사용 예제 (Workflow Example)

다음은 Codebase API를 사용하여 특정 저장소를 인덱싱하고, 파일 목록을 조회하며, 데이터베이스에 직접 쿼리하는 전체적인 워크플로우 예시입니다.

### 1단계: 저장소 인덱싱 요청

먼저, `codebase-sqlite`라는 이름의 저장소의 `master` 브랜치에 대한 인덱싱을 요청합니다. 이 요청은 백그라운드 작업을 시작하고 작업 ID를 반환합니다.

**cURL Command:**
```bash
curl -X POST "http://localhost:7101/api/codebase/index_by_name" \
-H "Content-Type: application/json" \
-d '{"repository_name": "codebase-sqlite", "branch": "master"}'
```

**예상 응답 (job_id 포함):**
```json
{
  "message": "Codebase indexing job started for repository 'codebase-sqlite' on branch 'master'.",
  "job_id": "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
}
```

### 2단계: 인덱싱 상태 확인

1단계에서 받은 `job_id`를 사용하여 인덱싱 작업의 완료 상태를 주기적으로 확인할 수 있습니다.

**cURL Command:**
```bash
curl "http://localhost:7101/api/codebase/indexing_status/xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx"
```

**예상 응답:**
```json
{
  "status": "completed"
}
```

### 3단계: 인덱싱된 파일 목록 조회

인덱싱이 완료되면 `files` 엔드포인트를 사용하여 특정 경로의 파일 목록을 가져올 수 있습니다.

**cURL Command (루트 디렉토리):**
```bash
# 'repository_name' 사용
curl "http://localhost:7101/api/codebase/files?repository_name=codebase-sqlite&branch=master&path=."

# 또는 'repository_id' 사용 (예: ID가 13인 경우)
curl "http://localhost:7101/api/codebase/files?repository_id=13&branch=master&path=."
```

**cURL Command (특정 하위 디렉토리):**
```bash
curl "http://localhost:7101/api/codebase/files?repository_name=codebase-sqlite&branch=master&path=/backend/app/controllers"
```

### 4단계: SQL 쿼리로 데이터 직접 조회

`sql_query` 엔드포인트를 사용하여 데이터베이스에 저장된 파일 정보를 직접 조회할 수 있습니다.

**cURL Command:**
```bash
curl -X POST "http://localhost:7101/api/codebase/sql_query" \
-H "Content-Type: application/json" \
-d '{"sql": "SELECT path, filename, size FROM codebase_files WHERE repository_id = 13 AND branch = '\''master'\'' AND extension = '\''rb'\'' ORDER BY size DESC LIMIT 5"}'
```

**예상 응답:**
```json
[
  {
    "path": "/backend/app/models",
    "filename": "repository.rb",
    "size": 1234
  },
  {
    "path": "/backend/app/controllers/api",
    "filename": "repositories_controller.rb",
    "size": 987
  }
]
```