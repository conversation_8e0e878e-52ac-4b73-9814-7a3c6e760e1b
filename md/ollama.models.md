아래는 `ollama ls` 명령으로 출력된 모델 목록을 **Python에서 활용**하는 방법, **모델별 특성**, 그리고 **임베딩 모델(dengcao 시리즈)의 사용법**까지 모두 마크다운 문서 형식으로 정리한 자료입니다.

---

# Ollama 모델 목록 및 Python 연동 가이드

## 📋 모델 목록 및 특성

| 모델 이름                                    | 용량    | 용도/특성                              | 비고                           |
| ---------------------------------------- | ----- | ---------------------------------- | ---------------------------- |
| **dengcao/Qwen3-Reranker-4B\:Q5\_K\_M**  | 2.9GB | 텍스트 임베딩 + 랭킹 (Reranker)            | 벡터DB 검색 rerank, embedding 활용 |
| **dengcao/Qwen3-Embedding-8B\:Q5\_K\_M** | 5.4GB | 텍스트 임베딩(Embedding)                 | 고성능 임베딩 전용                   |
| **qwen2.5vl\:latest**                    | 6.0GB | Qwen2.5 비전/멀티모달 LLM                | 텍스트+이미지 처리 가능                |
| **gemma3:4b**                            | 3.3GB | 구글 Gemma3, 4B 파라미터                 | 범용 LLM, 일반적 질의응답             |
| **gemma3:1b**                            | 815MB | Gemma3, 1B 파라미터                    | 경량 LLM, 임베디드/경량 서버           |
| **gemma3:12b**                           | 8.1GB | Gemma3, 12B 파라미터                   | 대용량, 고성능 LLM                 |
| **phi4\:latest**                         | 9.1GB | MS Phi-4                           | 범용 LLM, 코드/자연어에 강점           |
| **qwen3\:latest**                        | 5.2GB | Qwen3 LLM                          | 범용 LLM, 중국계, 다국어             |
| **gemma3:12b-it-qat**                    | 8.9GB | Gemma3, 12B, Instruct Tuning + QAT | 대화형, 압축/최적화                  |
| **gemma3:4b-it-qat**                     | 4.0GB | Gemma3, 4B, Instruct Tuning + QAT  | 대화형, 압축/최적화                  |

---

## 🚀 Python에서 Ollama 모델 사용하기

Python에서 Ollama 서버에 요청하여 LLM 또는 임베딩 결과를 받으려면 **`requests`** 등 HTTP 라이브러리로 Ollama API를 활용하면 됩니다.

### 1. 일반 LLM 질의 (예: Gemma3, Phi4 등)

```python
import requests

# 예시: Gemma3 4B 모델에 질의
response = requests.post(
    'http://localhost:11434/api/generate',
    json={
        'model': 'gemma3:4b',
        'prompt': 'Explain the concept of vector databases in simple terms.'
    }
)
print(response.json()['response'])
```

### 2. 임베딩(Embedding) 모델 사용법

임베딩 전용 모델(`dengcao/Qwen3-Embedding-8B` 등)은 Ollama API의 `/api/embeddings` 엔드포인트를 사용하여 텍스트 임베딩 벡터를 얻을 수 있습니다.

```python
import requests

texts = ["Hello world!", "This is an embedding example."]

response = requests.post(
    'http://localhost:11434/api/embeddings',
    json={
        "model": "dengcao/Qwen3-Embedding-8B:Q5_K_M",
        "prompt": texts
    }
)
embeddings = response.json()['embedding']  # 또는 'embeddings' (Ollama 버전에 따라 다름)
print(embeddings)
```

* **임베딩 모델 사용 TIP**

  * 여러 문장(리스트) 입력 가능
  * 벡터는 numpy 등으로 변환하여 벡터DB(Qdrant, FAISS 등)에 저장
  * 랭킹/재정렬 용도(검색 성능 향상)로 Reranker 모델 사용 가능

---

## 📌 모델별 주요 특징 요약

* **dengcao/Qwen3-Reranker-4B\:Q5\_K\_M**

  * 용도: 검색 결과 rerank, 문서 중요도 평가
  * 사용법: 벡터DB 1차 검색 후, 상위 문서에 대해 rerank scoring

* **dengcao/Qwen3-Embedding-8B\:Q5\_K\_M**

  * 용도: 텍스트 임베딩 전용(고성능, 대용량)
  * 사용법: 문장/문서 → 벡터화, 검색/추천/군집화에 활용

* **gemma3 시리즈**

  * 경량(1B), 표준(4B), 대형(12B) 선택 가능
  * 코드, 자연어, 다목적 LLM
  * Instruct, QAT 버전은 대화형/경량화 특화

* **phi4**

  * 코드 해석, 일반 텍스트에 강점

* **qwen 시리즈**

  * 중국계(Alibaba), 멀티모달, 다국어 특화

---

## 🛠️ 기타 참고

* Ollama는 REST API 기반이므로, Python 뿐 아니라 모든 언어에서 HTTP로 호출 가능
* `/api/generate` → 자연어 질의/대화
* `/api/embeddings` → 임베딩 벡터 추출
* API 포트(`11434`)는 기본, 필요 시 환경설정에서 변경

---

## 🔗 공식 문서

* [Ollama API Docs](https://github.com/ollama/ollama/blob/main/docs/api.md)
* [dengcao 임베딩 모델 소개](https://huggingface.co/dengcao) (HuggingFace 참조)
* [Gemma3 소개](https://ai.google.dev/gemma)
* [Qwen3 소개](https://huggingface.co/Qwen)

---

궁금한 점이나 예제 코드가 더 필요하면 언제든 요청하세요!
