# LSP Server API Documentation (2025-07 최신)

## 실행 모드 및 구조

- **REST API 모드**: 데이터베이스 직접 접근, GET/POST 기반 심볼/참조/정의/분석 API 제공
- **LSP 클라이언트 모드**: 내부 LSP 서버 프로세스와 연동, LSP 기반 위치기반 API(`/lsp/definition` 등) 제공
- **LSP 프로토콜 서버 모드**: LSP(JSON-RPC)만 독립 실행

### 실행 예시
```bash
# REST API 모드 (기본)
npm run start
# LSP 클라이언트 모드 (LSP + REST API)
npm run start:lsp-client
# LSP 프로토콜 서버만
npm run start:lsp-only
```

## 환경 변수 예시 (.env)
```env
PORT=7107
NODE_ENV=production
LOG_LEVEL=INFO
LSP_MODE=false
USE_LSP_CLIENT=true
DB_PATH=./data_lsp/codebase.sqlite
REPOSITORIES_PATH=./repositories
ANALYSIS_RECURSIVE=true
SUPPORTED_EXTENSIONS=.js,.jsx,.ts,.tsx,.rb,.ruby,.py,.pyw
```

## 주요 엔드포인트

### 1. Health Check
- `GET /api/v1/health`

### 2. 코드 분석
- `POST /api/v1/analysis/scan` (경로 기반)
- `POST /api/v1/analysis/scan-repository` (리포지토리명 기반)

### 3. 심볼/정의/참조
- `GET /api/v1/symbols/definition?name=MyClass`
- `GET /api/v1/symbols/references?name=MyClass`
- `GET /api/v1/files/:path/symbols`
- `GET /api/v1/files/:path/ast`

### 4. LSP 기반 위치 API (LSP 클라이언트 모드에서만)
- `POST /api/v1/lsp/definition`  
  ```json
  { "filePath": "/path/to/file.js", "line": 10, "character": 5, "fileContent": "..." }
  ```
- `POST /api/v1/lsp/references`
- `POST /api/v1/lsp/completion`

### 5. 리포지토리/통계
- `GET /api/v1/repositories`
- `GET /api/v1/repositories/:folderName`
- `GET /api/v1/stats`

## 예시 cURL
```bash
# 기존 방식 (심볼명 기반)
curl "http://localhost:7107/api/v1/symbols/definition?name=MyClass"
# LSP 위치 기반 (LSP 클라이언트 모드)
curl -X POST "http://localhost:7107/api/v1/lsp/definition" -H "Content-Type: application/json" -d '{"filePath": "/path/to/file.js", "line": 10, "character": 5, "fileContent": "..."}'
```

## 개발/운영 참고
- tsconfig.json, Dockerfile, .env.example 등 최신화
- LSP/REST API 모드 전환은 환경변수로 제어
- Dockerfile에서 빌드/실행 모드 분리 가능
- 모든 API는 표준 에러 포맷 반환

## 변경 이력
- 2025-07: LSP 클라이언트 모드, 위치기반 POST API, 환경변수, Docker, 타입/런타임 오류 대응 등 반영

# 이하 기존 상세 API/타입/워크플로우/예시 등은 기존 문서와 동일하게 유지

## Introduction

LSP(Language Server Protocol) Server는 코드베이스에 대한 지능형 분석 및 언어 서비스 기능을 제공합니다. Tree-sitter 기반의 구문 분석을 통해 코드 심볼, 정의, 참조 등의 정보를 SQLite 데이터베이스에 저장하고 REST API 및 LSP 프로토콜을 통해 서비스를 제공합니다.

## Server Information

- **Base URL:** `http://localhost:7107` (REST API)
- **Port:** 7107 (REST API)
- **Protocol:** REST API (JSON) + LSP (JSON-RPC)
- **Database:** SQLite (`data_lsp/codebase.sqlite`)
- **CORS Origins:** `http://localhost:7103`, `http://localhost:7107`, `http://gserver.parrot-mine.ts.net:7103`

## Authentication

현재 버전에서는 인증이 필요하지 않습니다. 모든 엔드포인트는 공개 액세스가 가능합니다.

---

## 1. Health Check API

### `GET /api/v1/health` - Health Check

서버의 상태를 확인합니다.

**Response Body:**
```json
{
  "status": "ok",
  "message": "LSP server is running"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/health"
```

---

## 2. Analysis API (`/api/v1/analysis`)

코드베이스 분석 관련 기능을 제공합니다.

### `POST /api/v1/analysis/scan` - Scan Directory

지정된 디렉토리를 스캔하여 코드 심볼과 참조를 분석합니다.

**Request Body:** `AnalysisRequest`
```json
{
  "path": "string",
  "repositoryFolderName": "string (required)",
  "recursive": "boolean (optional, default: true)"
}
```

**Response Body:** `AnalysisResponse`
```json
{
  "status": "ok | error",
  "message": "string",
  "symbolsFound": "number (optional)",
  "referencesFound": "number (optional)"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7107/api/v1/analysis/scan" \
-H "Content-Type: application/json" \
-d '{
  "path": "/path/to/your/project",
  "repositoryFolderName": "my-project_123",
  "recursive": true
}'
```

---

### `POST /api/v1/analysis/scan-repository` - Scan Repository by Folder Name

리포지토리 폴더명을 통해 공용 리포지토리 폴더에서 해당 리포지토리를 스캔합니다.

**Request Body:** `RepositoryAnalysisRequest`
```json
{
  "repositoryFolderName": "string",
  "recursive": "boolean (optional, default: true)"
}
```

**Response Body:** `AnalysisResponse`
```json
{
  "status": "ok | error",
  "message": "string",
  "symbolsFound": "number (optional)",
  "referencesFound": "number (optional)",
  "repositoryFolderName": "string",
  "repositoryPath": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:7107/api/v1/analysis/scan-repository" \
-H "Content-Type: application/json" \
-d '{
  "repositoryFolderName": "codebase-sqlite_13",
  "recursive": true
}'
```

---

## 3. Symbols API (`/api/v1/symbols`)

코드 심볼 정의 및 참조 관련 기능을 제공합니다.

### `GET /api/v1/symbols/definition` - Get Symbol Definition

심볼의 정의를 조회합니다.

**Query Parameters:**
- `name` (string, required): 심볼 이름
- `repositoryFolderName` (string, required): 리포지토리 폴더명
- `filePath` (string, optional): 파일 경로로 필터링

**Response Body:**
```json
{
  "name": "string",
  "kind": "string",
  "filePath": "string",
  "position": {
    "start": {
      "line": "number",
      "character": "number"
    },
    "end": {
      "line": "number",
      "character": "number"
    }
  }
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/symbols/definition?name=MyClass&repositoryFolderName=my-project_123&filePath=/path/to/file.js"
```

---

### `GET /api/v1/symbols/references` - Get Symbol References

심볼의 참조를 조회합니다.

**Query Parameters:**
- `name` (string): 심볼 이름 (name 또는 symbolId 중 하나 필수)
- `symbolId` (number): 심볼 ID (name 또는 symbolId 중 하나 필수)
- `repositoryFolderName` (string, required): 리포지토리 폴더명

**Response Body:**
```json
[
  {
    "filePath": "string",
    "position": {
      "start": {
        "line": "number",
        "character": "number"
      },
      "end": {
        "line": "number",
        "character": "number"
      }
    }
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/symbols/references?name=MyClass&repositoryFolderName=my-project_123"
```

---

## 4. Files API (`/api/v1/files`)

파일별 심볼 및 AST 정보를 제공합니다.

### `GET /api/v1/files/:path/symbols` - Get File Symbols

특정 파일의 모든 심볼을 조회합니다.

**Query Parameters:**
- `path` (string, required): 파일 경로
- `repositoryFolderName` (string, required): 리포지토리 폴더명

**Response Body:**
```json
[
  {
    "name": "string",
    "kind": "string",
    "position": {
      "start": {
        "line": "number",
        "character": "number"
      },
      "end": {
        "line": "number",
        "character": "number"
      }
    }
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/files/symbols?path=/path/to/file.js&repositoryFolderName=my-project_123"
```

---

### `GET /api/v1/files/:path/ast` - Get File AST (Debug)

특정 파일의 AST(Abstract Syntax Tree)를 조회합니다. (디버깅용)

**Path Parameters:**
- `path` (string): 파일 경로 (URL 인코딩 필요)

**Response Body:**
```json
{
  "ast": "object (Tree-sitter AST structure)"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/files/path%2Fto%2Ffile.js/ast"
```

---

## 5. Statistics API (`/api/v1/stats`)

### `GET /api/v1/stats` - Get Statistics

코드베이스 분석 통계를 조회합니다.

**Response Body:**
```json
{
  "totalSymbols": "number",
  "totalReferences": "number",
  "symbolsByKind": "object",
  "filesCovered": "number"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/stats"
```

---

## 6. Repositories API (`/api/v1/repositories`)

공용 리포지토리 폴더에서 사용 가능한 리포지토리들을 관리합니다.

### `GET /api/v1/repositories` - List Repositories

사용 가능한 모든 리포지토리 목록을 조회합니다.

**Response Body:**
```json
[
  {
    "folderName": "string",
    "displayName": "string",
    "path": "string",
    "lastModified": "string (ISO 8601)",
    "size": "number"
  }
]
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/repositories"
```

---

### `GET /api/v1/repositories/:folderName` - Get Repository Info

특정 리포지토리의 상세 정보를 조회합니다.

**Path Parameters:**
- `folderName` (string): 리포지토리 폴더명 (URL 인코딩 필요)

**Response Body:**
```json
{
  "folderName": "string",
  "displayName": "string",
  "path": "string",
  "lastModified": "string (ISO 8601)",
  "size": "number",
  "fileCount": "number",
  "symbolCount": "number"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:7107/api/v1/repositories/codebase-sqlite_13"
```

---

## 7. LSP Protocol Support

LSP 서버는 다음과 같은 LSP 기능을 지원합니다:

### Supported LSP Features

- **Text Document Synchronization**: 문서 변경 사항 동기화
- **Go to Definition**: 정의로 이동
- **Find References**: 참조 찾기
- **Document Symbols**: 문서 심볼 목록
- **Workspace Symbols**: 워크스페이스 심볼 검색
- **Completion**: 자동완성

### LSP Connection

LSP 서버는 JSON-RPC 프로토콜을 통해 통신합니다:

**Connection Methods:**
- WebSocket: `ws://localhost:8081`
- TCP Socket: `localhost:8081`
- Stdio: 프로세스 시작 시 stdin/stdout 사용

---

## 8. Error Responses

### Standard Error Response

모든 API 엔드포인트는 오류 발생 시 다음 형식의 응답을 반환합니다:

```json
{
  "status": "error",
  "message": "string",
  "error": "string (optional)"
}
```

### Common HTTP Status Codes

- `200 OK`: 성공적인 요청
- `400 Bad Request`: 잘못된 요청 (필수 파라미터 누락 등)
- `404 Not Found`: 리소스를 찾을 수 없음
- `500 Internal Server Error`: 서버 내부 오류

---

## 9. Data Types

### Symbol Kinds

지원되는 심볼 타입:

- `file`: 파일
- `module`: 모듈
- `namespace`: 네임스페이스
- `package`: 패키지
- `class`: 클래스
- `method`: 메서드
- `property`: 프로퍼티
- `field`: 필드
- `constructor`: 생성자
- `enum`: 열거형
- `interface`: 인터페이스
- `function`: 함수
- `variable`: 변수
- `constant`: 상수
- `string`: 문자열
- `number`: 숫자
- `boolean`: 불린
- `array`: 배열
- `object`: 객체
- `key`: 키
- `null`: Null
- `enumMember`: 열거형 멤버
- `struct`: 구조체
- `event`: 이벤트
- `operator`: 연산자
- `typeParameter`: 타입 매개변수

### Position

```typescript
interface Position {
  line: number;        // 0-based line number
  character: number;   // 0-based character offset
}
```

### Range

```typescript
interface Range {
  start: Position;
  end: Position;
}
```

---

## 10. Supported Languages

현재 지원되는 프로그래밍 언어:

- **JavaScript** (`.js`)
- **TypeScript** (`.ts`, `.tsx`)
- **Python** (`.py`)
- **Ruby** (`.rb`)

각 언어는 Tree-sitter 파서를 통해 분석되며, 언어별 특성에 맞는 심볼 추출이 수행됩니다.

---

## 11. Configuration

### Environment Variables

- `NODE_ENV`: 실행 환경 (`development`, `production`)
- `PORT`: REST API 포트 (기본값: 7107)
- `LOG_LEVEL`: 로그 레벨 (`debug`, `info`, `warn`, `error`)
- `LSP_MODE`: LSP 모드 (`true` for LSP stdio mode, `false` for REST API mode)
- `USE_LSP_CLIENT`: LSP 클라이언트 사용 여부 (`true`, `false`)
- `DB_PATH`: SQLite 데이터베이스 경로 (기본값: `./data_lsp/codebase.sqlite`)
- `REPOSITORIES_PATH`: 공용 리포지토리 폴더 경로 (기본값: `./repositories`)
- `ANALYSIS_RECURSIVE`: 분석 시 재귀 탐색 여부 (기본값: `true`)
- `SUPPORTED_EXTENSIONS`: 지원되는 파일 확장자 (기본값: `.js,.jsx,.ts,.tsx,.rb,.ruby,.py,.pyw`)

### Database Schema

LSP 서버는 다음 테이블을 사용합니다:

- `symbols`: 코드 심볼 정보 저장
- `references`: 심볼 참조 정보 저장

---

## 12. Examples

### Complete Workflow Example

1. **사용 가능한 리포지토리 목록 조회:**
```bash
curl -X GET "http://localhost:7107/api/v1/repositories"
```

2. **특정 리포지토리 정보 확인:**
```bash
curl -X GET "http://localhost:7107/api/v1/repositories/codebase-sqlite_13"
```

3. **리포지토리 스캔:**
```bash
curl -X POST "http://localhost:7107/api/v1/analysis/scan-repository" \
-H "Content-Type: application/json" \
-d '{"repositoryFolderName": "codebase-sqlite_13", "recursive": true}'
```

4. **또는 직접 경로 스캔:**
```bash
curl -X POST "http://localhost:7107/api/v1/analysis/scan" \
-H "Content-Type: application/json" \
-d '{"path": "/path/to/project", "repositoryFolderName": "my-project_123", "recursive": true}'
```

5. **특정 심볼 정의 찾기:**
```bash
curl -X GET "http://localhost:7107/api/v1/symbols/definition?name=MyClass&repositoryFolderName=codebase-sqlite_13"
```

6. **심볼 참조 찾기:**
```bash
curl -X GET "http://localhost:7107/api/v1/symbols/references?name=MyClass&repositoryFolderName=codebase-sqlite_13"
```

7. **파일의 모든 심볼 조회:**
```bash
curl -X GET "http://localhost:7107/api/v1/files/symbols?path=src/main.js&repositoryFolderName=codebase-sqlite_13"
```

8. **통계 확인:**
```bash
curl -X GET "http://localhost:7107/api/v1/stats"
```

---

## 13. Development & Debugging

### Development Mode

개발 모드에서는 추가적인 디버깅 기능이 활성화됩니다:

- 상세한 로그 출력
- AST 조회 API 활성화
- 핫 리로드 지원

### Debugging Tips

1. **로그 확인**: `LOG_LEVEL=debug` 환경변수 설정
2. **AST 구조 확인**: `/api/v1/files/:path/ast` 엔드포인트 활용
3. **데이터베이스 상태 확인**: SQLite 브라우저 도구 사용
4. **LSP 통신 디버깅**: LSP 클라이언트의 로그 기능 활용

---

## 14. Performance Considerations

### Optimization Tips

1. **대용량 프로젝트 스캔**: `recursive: false` 옵션으로 점진적 스캔
2. **메모리 사용량**: 파일 크기와 프로젝트 규모에 따라 조절
3. **데이터베이스 최적화**: 정기적인 VACUUM 실행 권장
4. **캐싱**: 빈번한 요청에 대한 응답 캐싱 고려

### Limitations

- 최대 파일 크기: 10MB
- 최대 심볼 수: 1,000,000개
- 동시 연결 수: 100개
- 메모리 사용량: 최대 512MB

---

## 15. Changelog

### Version 1.0.0
- 초기 릴리즈
- REST API 및 LSP 프로토콜 지원
- JavaScript, TypeScript, Python, Ruby 지원
- SQLite 데이터베이스 저장
- Docker 컨테이너 지원
