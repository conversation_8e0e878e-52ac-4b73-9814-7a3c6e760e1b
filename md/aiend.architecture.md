# AI Engine (aiend) - 아키텍처 상세

## 🏗️ 레이어드 아키텍처

### 전체 아키텍처 다이어그램

```
┌─────────────────────────────────────────────────────────────┐
│                    FastAPI Server                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   Search    │ │  Analysis   │ │     LLM     │          │
│  │   Router    │ │   Router    │ │   Router    │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                LLM Integration Layer                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   OpenAI    │ │  Anthropic  │ │   Ollama    │          │
│  │  Provider   │ │  Provider   │ │  Provider   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            LLM Orchestrator                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│              Context Optimization Layer                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │    Query    │ │   Context   │ │    Token    │          │
│  │  Analyzer   │ │   Builder   │ │   Manager   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │           Semantic Compressor                          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                Intelligence Layer                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │  Semantic   │ │ Structural  │ │  Temporal   │          │
│  │   Search    │ │  Analysis   │ │  Analysis   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              Smart Search Engine                       │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Data Layer                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐          │
│  │   SQLite    │ │   Vector    │ │     Git     │          │
│  │   Manager   │ │    Store    │ │  Analyzer   │          │
│  └─────────────┘ └─────────────┘ └─────────────┘          │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            Codebase Analyzer                           │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 📊 데이터 플로우

### 1. 검색 요청 플로우

```
User Query → FastAPI Router → Smart Search Engine
    ↓
Query Analyzer → Intent Analysis → Search Mode Selection
    ↓
Semantic Search ← Vector Store ← ChromaDB
Structural Search ← SQLite Manager ← Database
Temporal Search ← Git Analyzer ← Repository
    ↓
Result Fusion → Context Builder → Response
```

### 2. LLM 질문 플로우

```
User Question → FastAPI Router → LLM Orchestrator
    ↓
Context Builder → Smart Search → Relevant Code
    ↓
Query Analyzer → Intent Analysis → Strategy Selection
    ↓
LLM Router → Provider Selection → API Calls
    ↓
Response Integration → Final Answer → User
```

### 3. 코드베이스 인덱싱 플로우

```
Repository → Codebase Analyzer → File Scanner
    ↓
Language Detection → Parser Selection → AST Analysis
    ↓
SQLite Manager ← Metadata Storage
Vector Store ← Embedding Generation
Git Analyzer ← History Analysis
```

## 🔧 컴포넌트 상세

### Data Layer

#### SQLiteManager
```python
class SQLiteManager:
    """SQLite 데이터베이스 관리"""
    - 코드 파일 메타데이터 저장
    - 함수/클래스 정보 관리
    - Git 커밋 이력 저장
    - 트랜잭션 관리
```

#### VectorStore
```python
class VectorStore:
    """벡터 임베딩 저장소"""
    - ChromaDB 기반 벡터 검색
    - 코드 청크 임베딩
    - 의미적 유사도 계산
    - 컬렉션 관리
```

#### GitAnalyzer
```python
class GitAnalyzer:
    """Git 히스토리 분석"""
    - 커밋 이력 분석
    - 파일 변경 추적
    - 버그 수정 패턴 학습
    - 작성자 통계
```

### Intelligence Layer

#### SmartSearch
```python
class SmartSearch:
    """지능형 검색 엔진"""
    - 다중 검색 모드 지원
    - 결과 융합 및 랭킹
    - 관련 쿼리 제안
    - 검색 성능 최적화
```

#### SemanticSearch
```python
class SemanticSearch:
    """의미적 검색"""
    - 자연어 쿼리 처리
    - 벡터 유사도 검색
    - 컨텍스트 타입 필터링
    - 결과 스코어링
```

#### StructuralAnalyzer
```python
class StructuralAnalyzer:
    """구조적 분석"""
    - 코드 구조 분석
    - 의존성 관계 추적
    - 상속 계층 분석
    - 호출 그래프 생성
```

### Context Optimization Layer

#### ContextBuilder
```python
class ContextBuilder:
    """컨텍스트 빌더"""
    - 관련 코드 수집
    - 토큰 예산 관리
    - 우선순위 기반 선택
    - 컨텍스트 포맷팅
```

#### QueryAnalyzer
```python
class QueryAnalyzer:
    """쿼리 분석기"""
    - 의도 분석
    - 엔티티 추출
    - 키워드 분석
    - 대상 타입 결정
```

#### TokenManager
```python
class TokenManager:
    """토큰 관리자"""
    - 토큰 수 계산
    - 예산 할당
    - 압축 비율 결정
    - 사용량 최적화
```

### LLM Integration Layer

#### LLMOrchestrator
```python
class LLMOrchestrator:
    """LLM 오케스트레이터"""
    - 다중 LLM 조율
    - 응답 전략 선택
    - 결과 통합
    - 신뢰도 계산
```

#### LLMRouter
```python
class LLMRouter:
    """LLM 라우터"""
    - 작업 타입 분석
    - 제공자 선택
    - 모델 선택
    - 파라미터 최적화
```

#### Providers
```python
class BaseLLMProvider:
    """LLM 제공자 기본 클래스"""
    - API 호출 추상화
    - 응답 표준화
    - 에러 처리
    - 사용량 추적

class OpenAIProvider(BaseLLMProvider):
    """OpenAI 제공자"""

class AnthropicProvider(BaseLLMProvider):
    """Anthropic 제공자"""

class OllamaProvider(BaseLLMProvider):
    """Ollama 제공자"""
```

## 🔄 상호작용 패턴

### 1. 의존성 주입
```python
# FastAPI 의존성 주입
def get_search_system() -> SmartSearch:
    return SmartSearch(db_manager, vector_store, git_analyzer)

def get_llm_orchestrator() -> LLMOrchestrator:
    return LLMOrchestrator(context_builder, llm_router, providers)
```

### 2. 이벤트 기반 아키텍처
```python
# 코드베이스 변경 이벤트
@event_handler("file_changed")
async def on_file_changed(file_path: str):
    await codebase_analyzer.reindex_file(file_path)
    await vector_store.update_embeddings(file_path)
```

### 3. 플러그인 시스템
```python
# 새로운 LLM 제공자 등록
@register_provider("custom_llm")
class CustomLLMProvider(BaseLLMProvider):
    pass
```

## 🚀 성능 최적화

### 1. 캐싱 전략
- **메모리 캐시**: 자주 사용되는 검색 결과
- **디스크 캐시**: 임베딩 벡터
- **Redis 캐시**: 세션 데이터

### 2. 비동기 처리
- **동시 LLM 호출**: asyncio 기반
- **백그라운드 작업**: 인덱싱, 임베딩 생성
- **스트리밍 응답**: 실시간 결과 전송

### 3. 데이터베이스 최적화
- **인덱스 최적화**: 자주 사용되는 쿼리
- **배치 처리**: 대량 데이터 삽입
- **연결 풀링**: 데이터베이스 연결 관리

## 🔒 보안 고려사항

### 1. API 보안
- **인증**: JWT 토큰 기반
- **권한 관리**: 역할 기반 접근 제어
- **입력 검증**: Pydantic 스키마

### 2. 데이터 보안
- **민감 정보 마스킹**: 코드에서 비밀 정보 제거
- **암호화**: 저장 데이터 암호화
- **접근 로그**: 모든 접근 기록

### 3. LLM 보안
- **프롬프트 인젝션 방지**: 입력 검증
- **응답 필터링**: 부적절한 내용 차단
- **사용량 제한**: 남용 방지

## 📈 모니터링 및 로깅

### 1. 메트릭 수집
- **응답 시간**: API 엔드포인트별
- **에러율**: 컴포넌트별 에러 추적
- **사용량**: LLM API 호출 통계

### 2. 로그 관리
- **구조화된 로깅**: JSON 형태
- **로그 레벨**: DEBUG, INFO, WARNING, ERROR
- **로그 집계**: 중앙화된 로그 관리

### 3. 헬스체크
- **서비스 상태**: 각 컴포넌트 상태 확인
- **의존성 체크**: 외부 서비스 연결 상태
- **리소스 모니터링**: CPU, 메모리 사용량
