# `/api/codebase` 서비스 구현 - 모델 설계

## 1. CodebaseFile 모델

```ruby
# app/models/codebase_file.rb
class CodebaseFile < ApplicationRecord
  belongs_to :repository
  has_many :codebase_symbols, dependent: :destroy
  
  # 검증
  validates :branch, presence: true
  validates :path, presence: true
  validates :filename, presence: true
  validates :depth, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  
  # 스코프
  scope :by_repository, ->(repository_id) { where(repository_id: repository_id) }
  scope :by_branch, ->(branch) { where(branch: branch) }
  scope :by_extension, ->(extension) { where(extension: extension) }
  scope :by_depth, ->(depth) { where(depth: depth) }
  
  # 전체 텍스트 검색 스코프
  scope :search_content, ->(query) { 
    joins("JOIN codebase_files_fts ON codebase_files.id = codebase_files_fts.rowid")
    .where("codebase_files_fts.content MATCH ?", query)
    .where(is_binary: false) 
  }
  
  scope :search_filename, ->(query) { 
    joins("JOIN codebase_files_fts ON codebase_files.id = codebase_files_fts.rowid")
    .where("codebase_files_fts.filename MATCH ?", query)
  }
  
  scope :search_path, ->(query) { 
    joins("JOIN codebase_files_fts ON codebase_files.id = codebase_files_fts.rowid")
    .where("codebase_files_fts.path MATCH ?", query)
  }
  
  scope :search, ->(query) { 
    joins("JOIN codebase_files_fts ON codebase_files.id = codebase_files_fts.rowid")
    .where("codebase_files_fts MATCH ?", query)
  }
  
  # 파일 경로 생성
  def full_path
    File.join(path, filename)
  end
  
  # 파일 확장자 설정
  before_validation :set_extension
  
  private
  
  def set_extension
    self.extension = File.extname(filename).delete('.') if filename.present?
  end
end
```

## 2. CodebaseDirectory 모델

```ruby
# app/models/codebase_directory.rb
class CodebaseDirectory < ApplicationRecord
  belongs_to :repository
  belongs_to :parent_directory, class_name: 'CodebaseDirectory', optional: true
  has_many :subdirectories, class_name: 'CodebaseDirectory', foreign_key: 'parent_directory_id', dependent: :destroy
  
  # 검증
  validates :branch, presence: true
  validates :path, presence: true
  validates :name, presence: true
  validates :depth, presence: true, numericality: { only_integer: true, greater_than_or_equal_to: 0 }
  
  # 스코프
  scope :by_repository, ->(repository_id) { where(repository_id: repository_id) }
  scope :by_branch, ->(branch) { where(branch: branch) }
  scope :by_depth, ->(depth) { where(depth: depth) }
  scope :roots, -> { where(parent_directory_id: nil) }
  
  # 디렉토리 트리 구축
  def self.build_tree(repository_id, branch, options = {})
    max_depth = options.fetch(:max_depth, nil)
    lazy_load = options.fetch(:lazy_load, false)
    
    query = by_repository(repository_id).by_branch(branch)
    query = query.where('depth <= ?', max_depth) if max_depth.present?
    
    if lazy_load
      # 루트 디렉토리만 반환
      roots = query.roots.order(:path)
      return roots.map { |dir| dir.as_json(methods: [:has_subdirectories]) }
    else
      # 전체 트리 구축
      directories = query.order(:path).includes(:subdirectories)
      build_directory_tree(directories.select(&:root?))
    end
  end
  
  def has_subdirectories
    subdirectories.exists?
  end
  
  private
  
  def self.build_directory_tree(directories)
    directories.map do |directory|
      {
        id: directory.id,
        name: directory.name,
        path: directory.path,
        children: build_directory_tree(directory.subdirectories)
      }
    end
  end
end
```

## 3. CodebaseSymbol 모델

```ruby
# app/models/codebase_symbol.rb
class CodebaseSymbol < ApplicationRecord
  belongs_to :codebase_file
  
  # 검증
  validates :name, presence: true
  validates :type, presence: true
  
  # 스코프
  scope :by_type, ->(type) { where(type: type) }
  
  # 전체 텍스트 검색 스코프
  scope :search_name, ->(query) { 
    joins("JOIN codebase_symbols_fts ON codebase_symbols.id = codebase_symbols_fts.rowid")
    .where("codebase_symbols_fts.name MATCH ?", query)
  }
  
  scope :search_signature, ->(query) { 
    joins("JOIN codebase_symbols_fts ON codebase_symbols.id = codebase_symbols_fts.rowid")
    .where("codebase_symbols_fts.signature MATCH ?", query)
  }
  
  scope :search_documentation, ->(query) { 
    joins("JOIN codebase_symbols_fts ON codebase_symbols.id = codebase_symbols_fts.rowid")
    .where("codebase_symbols_fts.documentation MATCH ?", query)
  }
  
  scope :search, ->(query) { 
    joins("JOIN codebase_symbols_fts ON codebase_symbols.id = codebase_symbols_fts.rowid")
    .where("codebase_symbols_fts MATCH ?", query)
  }
  
  # STI(Single Table Inheritance)를 피하기 위해 'symbol_type' 컬럼 사용
  self.inheritance_column = :_type_disabled
  
  # 메타데이터 접근자
  def metadata
    self[:metadata] || {}
  end
end
```

## 4. Repository 모델 확장

기존 Repository 모델에 다음 관계를 추가해야 합니다:

```ruby
# app/models/repository.rb (확장)
class Repository < ApplicationRecord
  # 기존 관계 외에 추가
  has_many :codebase_files, dependent: :destroy
  has_many :codebase_directories, dependent: :destroy
  
  # 특정 브랜치의 코드베이스 파일 조회
  def codebase_files_for_branch(branch)
    codebase_files.by_branch(branch)
  end
  
  # 특정 브랜치의 코드베이스 디렉토리 트리 조회
  def codebase_directory_tree(branch, options = {})
    CodebaseDirectory.build_tree(id, branch, options)
  end
  
  # 코드베이스 인덱싱 메서드
  def index_codebase(branch)
    # 백그라운드 작업으로 처리
    CodebaseIndexingJob.perform_later(self, branch)
  end
end
```

## 5. 인덱싱 작업 (Background Job)

```ruby
# app/jobs/codebase_indexing_job.rb
class CodebaseIndexingJob < ApplicationJob
  queue_as :default
  
  def perform(repository, branch)
    # 1. 기존 데이터 삭제
    repository.codebase_files.by_branch(branch).delete_all
    repository.codebase_directories.by_branch(branch).delete_all
    
    # 2. 저장소 경로 설정
    repo_path = repository.local_path
    
    # 3. 브랜치 체크아웃
    `cd #{repo_path} && git checkout #{branch}`
    
    # 4. 디렉토리 및 파일 인덱싱
    index_directory(repository, branch, repo_path, '')
  end
  
  private
  
  def index_directory(repository, branch, repo_path, relative_path, parent_directory = nil, depth = 0)
    full_path = File.join(repo_path, relative_path)
    
    # 현재 디렉토리 인덱싱
    unless relative_path.empty?
      dir_name = File.basename(relative_path)
      directory = repository.codebase_directories.create!(
        branch: branch,
        path: File.dirname(relative_path),
        name: dir_name,
        depth: depth,
        parent_directory: parent_directory
      )
    end
    
    # 하위 항목 처리
    Dir.entries(full_path).each do |entry|
      next if entry == '.' || entry == '..' || entry == '.git'
      
      entry_path = File.join(relative_path, entry)
      entry_full_path = File.join(repo_path, entry_path)
      
      if File.directory?(entry_full_path)
        # 재귀적으로 하위 디렉토리 인덱싱
        index_directory(repository, branch, repo_path, entry_path, directory, depth + 1)
      else
        # 파일 인덱싱
        index_file(repository, branch, repo_path, entry_path, depth)
      end
    end
  end
  
  def index_file(repository, branch, repo_path, relative_path, depth)
    full_path = File.join(repo_path, relative_path)
    
    # 파일 메타데이터 수집
    filename = File.basename(relative_path)
    file_path = File.dirname(relative_path)
    
    # 바이너리 파일 여부 확인
    is_binary = binary?(full_path)
    
    # 파일 내용 읽기 (바이너리가 아닌 경우에만)
    content = nil
    unless is_binary
      begin
        content = File.read(full_path)
      rescue => e
        Rails.logger.error "Failed to read file #{full_path}: #{e.message}"
      end
    end
    
    # 파일 크기
    size = File.size(full_path)
    
    # 파일 해시 (내용 기반)
    hash_id = Digest::SHA256.hexdigest(content || File.binread(full_path))
    
    # 파일 레코드 생성
    file = repository.codebase_files.create!(
      branch: branch,
      path: file_path,
      filename: filename,
      depth: depth,
      content: content,
      hash_id: hash_id,
      size: size,
      is_binary: is_binary
    )
    
    # 파일이 텍스트 파일인 경우 심볼 추출
    extract_symbols(file) unless is_binary
  end
  
  def binary?(path)
    # 파일이 바이너리인지 확인하는 로직
    # 간단한 구현: 파일 확장자 기반 확인
    binary_extensions = %w[.jpg .jpeg .png .gif .pdf .zip .tar.gz .exe .dll .so]
    binary_extensions.any? { |ext| path.downcase.end_with?(ext) }
  end
  
  def extract_symbols(file)
    # 파일 확장자에 따라 적절한 심볼 추출기 사용
    case File.extname(file.filename).downcase
    when '.rb'
      extract_ruby_symbols(file)
    when '.js', '.ts'
      extract_js_symbols(file)
    when '.py'
      extract_python_symbols(file)
    when '.java'
      extract_java_symbols(file)
    when '.c', '.cpp', '.h', '.hpp'
      extract_c_symbols(file)
    end
  end
  
  # 언어별 심볼 추출 메서드 (간략한 예시)
  def extract_ruby_symbols(file)
    # Ruby 파일에서 클래스, 모듈, 메서드 등 추출
    # 실제 구현에서는 parser gem 등을 사용할 수 있음
  end
  
  def extract_js_symbols(file)
    # JavaScript/TypeScript 파일에서 함수, 클래스 등 추출
  end
  
  def extract_python_symbols(file)
    # Python 파일에서 함수, 클래스 등 추출
  end
  
  def extract_java_symbols(file)
    # Java 파일에서 클래스, 메서드 등 추출
  end
  
  def extract_c_symbols(file)
    # C/C++ 파일에서 함수, 구조체 등 추출
  end
end
```
