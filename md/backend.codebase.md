ㄷㅡ는는# `/api/codebase` 서비스 구현 계획 보고서

## 1. 개요

현재 문서를 분석한 결과, 기존 시스템은 Git 저장소 관리, 파일 검색, 벡터 기반 검색 등의 기능을 제공하고 있습니다. 새로 구현할 `/api/codebase` 서비스는 코드베이스 정보를 SQLite에 저장하고 효율적으로 검색할 수 있는 기능을 제공할 것입니다.

## 2. 데이터베이스 스키마 설계

### 2.1 `codebase_files` 테이블

```sql
CREATE TABLE codebase_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    repository_id INTEGER NOT NULL,
    branch TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_extension TEXT,
    depth INTEGER NOT NULL,
    content TEXT,
    content_hash TEXT,
    size INTEGER,
    last_modified TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(repository_id, branch, file_path)
);
```

### 2.2 `codebase_directories` 테이블

```sql
CREATE TABLE codebase_directories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    repository_id INTEGER NOT NULL,
    branch TEXT NOT NULL,
    directory_path TEXT NOT NULL,
    parent_directory TEXT,
    depth INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(repository_id, branch, directory_path)
);
```

### 2.3 `codebase_symbols` 테이블 (코드 내 함수, 클래스 등 심볼 정보)

```sql
CREATE TABLE codebase_symbols (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    file_id INTEGER NOT NULL,
    symbol_name TEXT NOT NULL,
    symbol_type TEXT NOT NULL, -- 'function', 'class', 'variable', 'interface' 등
    line_start INTEGER,
    line_end INTEGER,
    context TEXT, -- 심볼이 속한 상위 컨텍스트 (예: 클래스 내 메서드인 경우 클래스명)
    signature TEXT, -- 함수/메서드의 경우 시그니처 정보
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (file_id) REFERENCES codebase_files(id) ON DELETE CASCADE
);
```

### 2.4 인덱스 설정

```sql
-- 파일 검색 최적화를 위한 인덱스
CREATE INDEX idx_codebase_files_repo_branch ON codebase_files(repository_id, branch);
CREATE INDEX idx_codebase_files_path ON codebase_files(file_path);
CREATE INDEX idx_codebase_files_name ON codebase_files(file_name);
CREATE INDEX idx_codebase_files_extension ON codebase_files(file_extension);
CREATE INDEX idx_codebase_files_depth ON codebase_files(depth);

-- 디렉토리 검색 최적화
CREATE INDEX idx_codebase_directories_repo_branch ON codebase_directories(repository_id, branch);
CREATE INDEX idx_codebase_directories_path ON codebase_directories(directory_path);
CREATE INDEX idx_codebase_directories_depth ON codebase_directories(depth);

-- 심볼 검색 최적화
CREATE INDEX idx_codebase_symbols_file_id ON codebase_symbols(file_id);
CREATE INDEX idx_codebase_symbols_name ON codebase_symbols(symbol_name);
CREATE INDEX idx_codebase_symbols_type ON codebase_symbols(symbol_type);
```

### 2.5 전체 텍스트 검색을 위한 FTS5 가상 테이블

```sql
-- 파일 내용 전체 텍스트 검색을 위한 가상 테이블
CREATE VIRTUAL TABLE codebase_files_fts USING fts5(
    content,
    file_path,
    file_name,
    content='codebase_files',
    content_rowid='id'
);

-- 심볼 전체 텍스트 검색을 위한 가상 테이블
CREATE VIRTUAL TABLE codebase_symbols_fts USING fts5(
    symbol_name,
    signature,
    context,
    content='codebase_symbols',
    content_rowid='id'
);
```

## 3. API 엔드포인트 설계

### 3.1 기본 CRUD 엔드포인트

#### 저장소 파일 인덱싱
```
POST /api/codebase/index
```
**Request Body:**
```json
{
  "repository_id": "integer",
  "branch": "string"
}
```
**Response:**
```json
{
  "success": "boolean",
  "message": "string",
  "indexed_files_count": "integer",
  "indexed_directories_count": "integer",
  "indexed_symbols_count": "integer"
}
```

#### 파일 목록 조회
```
GET /api/codebase/files
```
**Query Parameters:**
- `repository_id`: 저장소 ID (필수)
- `branch`: 브랜치명 (필수)
- `directory_path`: 디렉토리 경로 (선택)
- `depth`: 특정 깊이의 파일만 조회 (선택)
- `extension`: 파일 확장자 필터 (선택)
- `page`: 페이지 번호 (기본값: 1)
- `per_page`: 페이지당 항목 수 (기본값: 50)

#### 디렉토리 구조 조회
```
GET /api/codebase/directories
```
**Query Parameters:**
- `repository_id`: 저장소 ID (필수)
- `branch`: 브랜치명 (필수)
- `parent_path`: 상위 디렉토리 경로 (선택)
- `depth`: 특정 깊이의 디렉토리만 조회 (선택)

#### 파일 상세 조회
```
GET /api/codebase/files/:id
```
**Path Parameters:**
- `id`: 파일 ID

#### 심볼 목록 조회
```
GET /api/codebase/symbols
```
**Query Parameters:**
- `repository_id`: 저장소 ID (필수)
- `branch`: 브랜치명 (필수)
- `file_path`: 파일 경로 (선택)
- `symbol_type`: 심볼 타입 (선택)
- `page`: 페이지 번호 (기본값: 1)
- `per_page`: 페이지당 항목 수 (기본값: 50)

### 3.2 검색 관련 엔드포인트

#### 파일명 검색
```
GET /api/codebase/search/files
```
**Query Parameters:**
- `repository_id`: 저장소 ID (필수)
- `branch`: 브랜치명 (필수)
- `query`: 검색어 (필수)
- `extension`: 파일 확장자 필터 (선택)
- `depth`: 특정 깊이의 파일만 검색 (선택)
- `page`: 페이지 번호 (기본값: 1)
- `per_page`: 페이지당 항목 수 (기본값: 20)

#### 파일 내용 검색
```
GET /api/codebase/search/content
```
**Query Parameters:**
- `repository_id`: 저장소 ID (필수)
- `branch`: 브랜치명 (필수)
- `query`: 검색어 (필수)
- `extension`: 파일 확장자 필터 (선택)
- `page`: 페이지 번호 (기본값: 1)
- `per_page`: 페이지당 항목 수 (기본값: 20)

#### 심볼 검색
```
GET /api/codebase/search/symbols
```
**Query Parameters:**
- `repository_id`: 저장소 ID (필수)
- `branch`: 브랜치명 (필수)
- `query`: 검색어 (필수)
- `symbol_type`: 심볼 타입 필터 (선택)
- `page`: 페이지 번호 (기본값: 1)
- `per_page`: 페이지당 항목 수 (기본값: 20)

#### 고급 검색 (복합 조건)
```
POST /api/codebase/search/advanced
```
**Request Body:**
```json
{
  "repository_id": "integer",
  "branch": "string",
  "file_filters": {
    "extensions": ["string"],
    "paths": ["string"],
    "depth_min": "integer",
    "depth_max": "integer"
  },
  "content_query": "string",
  "symbol_filters": {
    "types": ["string"],
    "names": ["string"]
  },
  "page": "integer",
  "per_page": "integer"
}
```

## 4. 구현 계획

### 4.1 단계별 구현 계획

1. **데이터베이스 설정 (1일)**
   - SQLite 데이터베이스 스키마 생성
   - 인덱스 및 FTS 가상 테이블 설정

2. **파일 인덱싱 기능 구현 (2일)**
   - 저장소 파일 스캔 및 메타데이터 추출
   - 디렉토리 구조 분석
   - 파일 내용 저장 및 해시 생성

3. **심볼 추출 기능 구현 (2일)**
   - 코드 파일 파싱
   - 함수, 클래스, 변수 등 심볼 추출
   - 심볼 메타데이터 저장

4. **기본 CRUD API 구현 (2일)**
   - 파일 목록 조회
   - 디렉토리 구조 조회
   - 파일 상세 조회
   - 심볼 목록 조회

5. **검색 API 구현 (3일)**
   - 파일명 검색
   - 파일 내용 검색 (FTS5 활용)
   - 심볼 검색
   - 고급 검색 (복합 조건)

6. **성능 최적화 및 테스트 (2일)**
   - 쿼리 최적화
   - 대용량 코드베이스 테스트
   - 인덱스 튜닝

7. **기존 시스템과의 통합 (1일)**
   - 기존 API와의 연동
   - 벡터 검색 시스템과의 연계

### 4.2 추가 고려사항

1. **증분 업데이트 구현**
   - 파일 변경 감지 및 부분 업데이트
   - Git 커밋 기반 변경 추적

2. **캐싱 전략**
   - 자주 조회되는 파일 및 디렉토리 구조 캐싱
   - 검색 결과 캐싱

3. **대용량 파일 처리**
   - 대용량 파일 내용은 별도 저장소에 저장
   - 콘텐츠 스트리밍 API 제공

4. **보안 고려사항**
   - SQL 인젝션 방지
   - 접근 권한 관리

## 5. 확장 가능성

1. **코드 분석 기능 확장**
   - 코드 복잡도 분석
   - 의존성 그래프 생성
   - 코드 품질 메트릭 추출

2. **언어별 특화 기능**
   - 언어별 심볼 추출기 구현
   - 언어별 코드 구조 분석

3. **변경 이력 추적**
   - 파일 및 심볼 변경 이력 저장
   - 변경 통계 API 제공

4. **벡터 검색과의 하이브리드 검색**
   - SQLite 기반 검색과 벡터 검색 결과 통합
   - 검색 결과 리랭킹

## 6. 결론

제안된 `/api/codebase` 서비스는 코드베이스의 구조적 정보를 SQLite에 효율적으로 저장하고 검색할 수 있는 기능을 제공합니다. 파일 경로, 깊이, 확장자, 내용, 심볼 정보 등 다양한 메타데이터를 저장하고, 이를 기반으로 한 다양한 검색 기능을 제공함으로써 개발자들이 코드베이스를 더 효율적으로 탐색하고 이해할 수 있도록 지원합니다.

총 구현 기간은 약 13일로 예상되며, 단계적으로 기능을 구현하고 테스트하여 안정적인 서비스를 제공할 계획입니다. 또한 향후 확장 가능성을 고려하여 유연한 구조로 설계되었습니다.
