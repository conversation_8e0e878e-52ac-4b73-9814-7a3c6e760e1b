# Backend File Explorer API 설계

VSCode 스타일의 파일 탐색기와 Monaco Editor 기능을 위한 완전한 백엔드 구조 설계 문서

## 📋 개요

이 문서는 프론트엔드에서 VSCode와 같은 파일 탐색기 UI와 Monaco Editor를 통한 코드 편집 기능을 구현하기 위해 필요한 백엔드 API 구조를 정의합니다.

## 🎯 주요 기능 요구사항

### 1. 파일 트리 구조 표시
- VSCode 스타일의 계층적 폴더/파일 트리
- 지연 로딩 (Lazy Loading) 지원
- 폴더 확장/축소 상태 관리

### 2. 파일 내용 조회 및 편집
- Monaco Editor를 통한 코드 보기/편집
- 실시간 파일 내용 업데이트
- 바이너리 파일 처리

### 3. 검색 기능
- 파일명, 경로, 내용 검색
- 언어별, 확장자별 필터링
- 빠른 검색 결과 제공

### 4. 실시간 협업
- WebSocket을 통한 실시간 업데이트
- 다중 사용자 동시 편집 지원
- 파일 변경 알림

## 🏗️ 데이터베이스 구조

### 기존 테이블 활용
```sql
-- repositories: 저장소 정보
-- directories: 디렉토리 구조 (계층적)
-- files: 파일 정보 및 내용
-- commits: Git 커밋 히스토리
```

### 주요 관계
- Repository → Directories (1:N)
- Repository → Files (1:N)
- Directory → Files (1:N)
- Directory → Directory (Self-referencing, Parent-Child)

## 🔧 백엔드 구현 상세

### 1. Directory 모델 개선

#### 새로운 메서드 추가
```ruby
# app/models/directory.rb

class Directory < ApplicationRecord
  # 완전한 트리 구조 생성
  def self.build_tree(repository, options = {})
    include_files = options.fetch(:include_files, true)
    max_depth = options.fetch(:max_depth, nil)
    lazy_load = options.fetch(:lazy_load, false)
    
    if lazy_load
      build_lazy_tree(repository, max_depth)
    else
      build_complete_tree(repository, include_files, max_depth)
    end
  end

  # 지연 로딩용 트리 구조
  def self.build_lazy_tree(repository, max_depth = 2)
    # 초기 2-3 레벨까지만 로드
    # has_children 플래그로 확장 가능 여부 표시
  end

  # 완전한 트리 구조
  def self.build_complete_tree(repository, include_files, max_depth)
    # 모든 디렉토리와 파일을 포함한 완전한 트리
    # 메모리 사용량 고려하여 max_depth 제한
  end
end
```

### 2. File 모델 검색 기능 추가

#### 검색 스코프 추가
```ruby
# app/models/file.rb

class File < ApplicationRecord
  # 검색 스코프
  scope :search_content, ->(query) { 
    where('content LIKE ? AND is_binary = ?', "%#{query}%", false) 
  }
  scope :search_by_name, ->(query) { 
    where('name LIKE ?', "%#{query}%") 
  }
  scope :search_by_path, ->(query) { 
    where('relative_path LIKE ?', "%#{query}%") 
  }
  scope :search, ->(query) { 
    where('(content LIKE ? AND is_binary = ?) OR name LIKE ? OR relative_path LIKE ?', 
          "%#{query}%", false, "%#{query}%", "%#{query}%") 
  }
end
```

### 3. 파일 탐색기 전용 컨트롤러

#### 새로운 컨트롤러 생성
```ruby
# app/controllers/api/file_explorer_controller.rb

class Api::FileExplorerController < Api::BaseController
  before_action :set_repository

  # 파일 트리 구조 반환
  def tree
    # GET /api/repositories/:repository_id/explorer/tree
    # 파라미터: include_files, max_depth, lazy_load
  end

  # 특정 디렉토리 내용 반환
  def directory
    # GET /api/repositories/:repository_id/explorer/directory/*path
    # 지연 로딩을 위한 디렉토리별 조회
  end

  # 파일 정보 반환
  def file
    # GET /api/repositories/:repository_id/explorer/file/:id
  end

  # 파일 내용 반환
  def file_content
    # GET /api/repositories/:repository_id/explorer/file/:id/content
  end

  # 파일 내용 업데이트
  def update_file_content
    # PUT /api/repositories/:repository_id/explorer/file/:id/content
    # Monaco Editor에서 편집한 내용 저장
  end

  # 파일 검색
  def search
    # GET /api/repositories/:repository_id/explorer/search
    # 파라미터: q, type (content/filename/path/all)
  end
end
```

### 4. 실시간 WebSocket 채널

#### FileExplorerChannel 구현
```ruby
# app/channels/file_explorer_channel.rb

class FileExplorerChannel < ApplicationCable::Channel
  def subscribed
    # 저장소별 채널 구독
    stream_from "file_explorer_#{params[:repository_id]}"
  end

  def update_file(data)
    # 실시간 파일 내용 업데이트
    # 다른 사용자들에게 변경 사항 브로드캐스트
  end

  def refresh_tree(data)
    # 파일 트리 새로고침 요청 처리
  end
end
```

## 🛣️ API 엔드포인트

### 파일 트리 관련
```
GET /api/repositories/:id/explorer/tree
  - 파라미터: include_files, max_depth, lazy_load
  - 응답: 트리 구조 데이터

GET /api/repositories/:id/explorer/directory/*path
  - 특정 디렉토리의 즉시 자식들만 반환
  - 지연 로딩용
```

### 파일 조작 관련
```
GET /api/repositories/:id/explorer/file/:file_id
  - 파일 메타데이터 반환

GET /api/repositories/:id/explorer/file/:file_id/content
  - 파일 내용 반환 (Monaco Editor용)

PUT /api/repositories/:id/explorer/file/:file_id/content
  - 파일 내용 업데이트
  - 디스크와 데이터베이스 동시 업데이트
```

### 검색 관련
```
GET /api/repositories/:id/explorer/search
  - 파라미터: q (쿼리), type (검색 타입)
  - 검색 타입: content, filename, path, all
```

## 📊 데이터 형식

### 트리 노드 구조
```json
{
  "id": "dir_123" | "file_456",
  "name": "Button.tsx",
  "type": "directory" | "file",
  "path": "src/components/Button.tsx",
  "depth": 2,
  "children": [...],
  "expanded": false,
  "has_children": true,
  "file_count": 5,
  "subdirectory_count": 2,
  
  // 파일인 경우 추가 정보
  "size": 2048,
  "lines": 85,
  "language": "typescript",
  "extension": "tsx",
  "is_binary": false,
  "last_modified": "2024-01-01T00:00:00Z"
}
```

### 파일 내용 응답
```json
{
  "content": "import React from 'react';\n...",
  "file": {
    "id": 456,
    "name": "Button.tsx",
    "path": "src/components/Button.tsx",
    "language": "typescript",
    "encoding": "UTF-8",
    "lines": 85,
    "size": 2048
  }
}
```

### 검색 결과
```json
{
  "results": [
    {
      "file": {
        "id": 456,
        "name": "Button.tsx",
        "path": "src/components/Button.tsx",
        "language": "typescript",
        "size": 2048,
        "lines": 85
      },
      "match_type": "content",
      "directory": "src/components"
    }
  ],
  "query": "useState",
  "search_type": "content",
  "total_results": 15
}
```

## ⚡ 성능 최적화 전략

### 1. 지연 로딩 (Lazy Loading)
- 초기 로드 시 2-3 레벨까지만 표시
- 폴더 확장 시 동적으로 하위 항목 로드
- `has_children` 플래그로 확장 가능 여부 미리 표시

### 2. 캐싱 전략
- Redis를 활용한 트리 구조 캐싱
- 파일 내용 메모리 캐싱 (자주 접근하는 파일)
- 검색 결과 캐싱

### 3. 데이터베이스 최적화
- 적절한 인덱스 설정
  - `directories(repository_id, relative_path)`
  - `files(repository_id, relative_path)`
  - `files(name)`, `files(extension)`, `files(language)`
- N+1 쿼리 방지를 위한 `includes` 사용

### 4. 페이지네이션
- 대용량 디렉토리의 경우 파일 목록 페이지네이션
- 검색 결과 페이지네이션 (기본 50개 제한)

## 🔐 보안 고려사항

### 1. 파일 접근 제어
- Repository 권한 확인
- 파일 경로 검증 (Path Traversal 공격 방지)
- 바이너리 파일 편집 제한

### 2. 입력 검증
- 파일 내용 크기 제한
- 허용된 파일 확장자 검증
- XSS 방지를 위한 내용 이스케이핑

### 3. WebSocket 보안
- 채널 구독 시 인증 확인
- 메시지 크기 제한
- Rate limiting 적용

## 🚀 확장 가능성

### 1. 고급 편집 기능
- 문법 하이라이팅 지원 API
- 자동 완성을 위한 언어 서버 연동
- 린팅 및 포맷팅 API

### 2. 협업 기능
- 실시간 커서 위치 공유
- 파일별 댓글 및 리뷰 시스템
- 변경 사항 실시간 동기화
- 충돌 해결 메커니즘

### 3. Git 통합
- 파일별 Git 상태 표시 (modified, staged, etc.)
- 인라인 diff 보기
- 브랜치별 파일 트리
- 커밋 히스토리 연동

### 4. 고급 검색
- 정규식 검색 지원
- 전문 검색 엔진 연동 (Elasticsearch)
- 코드 구조 기반 검색 (함수, 클래스 등)
- 의미적 검색 (AI 기반)

## 🔄 WebSocket 이벤트

### 클라이언트 → 서버
```javascript
// 파일 내용 업데이트
{
  "action": "update_file",
  "file_id": 456,
  "content": "new file content..."
}

// 트리 새로고침 요청
{
  "action": "refresh_tree"
}
```

### 서버 → 클라이언트
```javascript
// 파일 업데이트 알림
{
  "type": "file_updated",
  "file": {
    "id": 456,
    "name": "Button.tsx",
    "path": "src/components/Button.tsx",
    "lines": 87,
    "size": 2156,
    "last_modified": "2024-01-01T12:30:00Z"
  }
}

// 트리 구조 변경 알림
{
  "type": "tree_updated",
  "repository_id": 123
}
```

## 📝 구현 우선순위

### Phase 1: 기본 기능
1. ✅ Directory 모델 `build_tree` 메서드 구현
2. ✅ File 모델 검색 스코프 추가
3. ✅ FileExplorerController 기본 액션 구현
4. ✅ 라우트 설정

### Phase 2: 고급 기능
1. WebSocket 채널 구현
2. 실시간 파일 편집 기능
3. 검색 성능 최적화
4. 캐싱 시스템 구축

### Phase 3: 확장 기능
1. Git 통합 기능
2. 협업 기능
3. 고급 검색 기능
4. 성능 모니터링

이 설계는 **VSCode와 동일한 수준의 파일 탐색 및 편집 경험**을 제공하며, **확장성과 성능을 모두 고려**한 구조입니다.
