# AI Engine (aiend) - 배포 및 운영

## 🐳 Docker 배포

### 개발 환경 배포

#### 1. AI Engine 단독 실행
```bash
cd aiend
docker-compose up -d
```

**포함 서비스**:
- `aiend`: FastAPI 서버 (포트 8000)
- `ollama`: 로컬 LLM 서버 (포트 11434)
- `chromadb`: 벡터 데이터베이스 (포트 8001, 선택사항)
- `redis`: 캐싱 서버 (포트 6379, 선택사항)

#### 2. 전체 시스템 실행 (모노레포)
```bash
# 루트 디렉토리에서
docker-compose up -d
```

**포함 서비스**:
- `aiend`: AI Engine
- `backend`: Ruby on Rails (예정)
- `frontend`: React (예정)
- `ollama`: 로컬 LLM
- `redis`: 캐싱
- `postgres`: 프로덕션 DB (선택사항)

### 프로덕션 배포

#### Kubernetes 배포 (예정)
```yaml
# k8s/aiend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aiend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: aiend
  template:
    metadata:
      labels:
        app: aiend
    spec:
      containers:
      - name: aiend
        image: codebase-intelligence/aiend:latest
        ports:
        - containerPort: 8000
        env:
        - name: CODEBASE_DB_PATH
          value: "/app/data/codebase.db"
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
```

## 🔧 환경 설정

### 환경 변수

#### 필수 환경 변수
```env
# LLM API 키 (선택사항)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# 데이터베이스 경로
CODEBASE_DB_PATH=/app/data/codebase.db
CODEBASE_VECTOR_PATH=/app/data/chroma_db

# 로깅
CODEBASE_LOG_LEVEL=INFO
```

#### 선택적 환경 변수
```env
# Ollama 설정
OLLAMA_HOST=http://localhost:11434

# Redis 설정 (캐싱용)
REDIS_URL=redis://localhost:6379/0

# 커스텀 API 엔드포인트
OPENAI_BASE_URL=https://api.openai.com/v1
ANTHROPIC_BASE_URL=https://api.anthropic.com
```

### 설정 파일

#### config/production.yaml
```yaml
database:
  path: "/app/data/codebase.db"
  backup_enabled: true
  backup_interval: 3600

vector_store:
  path: "/app/data/chroma_db"
  model: "microsoft/codebert-base"

llm:
  default_provider: "ollama"
  max_context_tokens: 16000
  response_timeout: 120

logging:
  level: "INFO"
  file_path: "/app/logs/aiend.log"
```

## 📊 모니터링 및 로깅

### 헬스체크 엔드포인트

#### 기본 헬스체크
```bash
curl http://localhost:8000/health
```

#### 상세 헬스체크
```bash
curl http://localhost:8000/health/detailed
```

#### Kubernetes 프로브
```yaml
livenessProbe:
  httpGet:
    path: /health/live
    port: 8000
  initialDelaySeconds: 30
  periodSeconds: 10

readinessProbe:
  httpGet:
    path: /health/ready
    port: 8000
  initialDelaySeconds: 5
  periodSeconds: 5
```

### 로깅 설정

#### 구조화된 로깅
```python
# 로그 형식
{
  "timestamp": "2024-01-01T00:00:00Z",
  "level": "INFO",
  "logger": "codebase_intelligence.api.main",
  "message": "Search request processed",
  "extra": {
    "query": "authentication function",
    "results_count": 5,
    "execution_time": 0.234
  }
}
```

#### 로그 수집
- **개발**: 콘솔 출력
- **프로덕션**: 파일 + 중앙 로그 시스템 (ELK Stack)

### 메트릭 수집

#### Prometheus 메트릭
```python
# 예제 메트릭
search_requests_total = Counter('search_requests_total', 'Total search requests')
search_duration_seconds = Histogram('search_duration_seconds', 'Search duration')
llm_api_calls_total = Counter('llm_api_calls_total', 'LLM API calls', ['provider'])
```

#### Grafana 대시보드
- API 응답 시간
- 에러율
- LLM 사용량
- 시스템 리소스

## 🔒 보안 설정

### API 보안

#### CORS 설정
```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://yourdomain.com"],  # 프로덕션에서는 제한
    allow_credentials=True,
    allow_methods=["GET", "POST"],
    allow_headers=["*"],
)
```

#### 인증 (예정)
```python
# JWT 토큰 기반 인증
@app.middleware("http")
async def authenticate_request(request: Request, call_next):
    token = request.headers.get("Authorization")
    if not verify_token(token):
        return JSONResponse(status_code=401, content={"detail": "Unauthorized"})
    response = await call_next(request)
    return response
```

### 데이터 보안

#### 민감 정보 마스킹
```python
def mask_sensitive_data(code: str) -> str:
    """코드에서 민감한 정보 마스킹"""
    patterns = [
        r'password\s*=\s*["\'][^"\']+["\']',
        r'api_key\s*=\s*["\'][^"\']+["\']',
        r'secret\s*=\s*["\'][^"\']+["\']'
    ]
    for pattern in patterns:
        code = re.sub(pattern, 'password="***"', code, flags=re.IGNORECASE)
    return code
```

#### 데이터 암호화
- 저장 데이터: SQLite 암호화 (SQLCipher)
- 전송 데이터: HTTPS/TLS

## 🚀 성능 최적화

### 캐싱 전략

#### Redis 캐싱
```python
# 검색 결과 캐싱
@cache(expire=3600)  # 1시간 캐시
async def search_with_cache(query: str) -> List[SearchResult]:
    return await smart_search.search(query)
```

#### 메모리 캐싱
```python
# LRU 캐시로 임베딩 캐싱
from functools import lru_cache

@lru_cache(maxsize=1000)
def get_embedding(text: str) -> List[float]:
    return embedding_model.encode(text)
```

### 데이터베이스 최적화

#### 인덱스 최적화
```sql
-- 자주 사용되는 쿼리용 인덱스
CREATE INDEX idx_code_files_language_path ON code_files(language, path);
CREATE INDEX idx_code_functions_name_file ON code_functions(name, file_id);
CREATE INDEX idx_git_commits_timestamp ON git_commits(timestamp);
```

#### 연결 풀링
```python
# SQLAlchemy 연결 풀 설정
engine = create_engine(
    database_url,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True
)
```

### 비동기 처리

#### 백그라운드 작업
```python
# Celery 또는 FastAPI BackgroundTasks 사용
@app.post("/analysis/index")
async def start_indexing(background_tasks: BackgroundTasks):
    background_tasks.add_task(run_indexing_task)
    return {"status": "started"}
```

## 📈 확장성 고려사항

### 수평 확장

#### 로드 밸런싱
```nginx
# Nginx 로드 밸런서 설정
upstream aiend_backend {
    server aiend-1:8000;
    server aiend-2:8000;
    server aiend-3:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://aiend_backend;
    }
}
```

#### 데이터베이스 샤딩
```python
# 프로젝트별 데이터베이스 분리
def get_database_for_project(project_id: str) -> SQLiteManager:
    db_path = f"/app/data/project_{project_id}.db"
    return SQLiteManager(db_path)
```

### 수직 확장

#### 리소스 할당
```yaml
# Kubernetes 리소스 제한
resources:
  requests:
    memory: "2Gi"
    cpu: "1000m"
  limits:
    memory: "4Gi"
    cpu: "2000m"
```

#### GPU 지원 (Ollama)
```yaml
# GPU 노드에 Ollama 배포
nodeSelector:
  accelerator: nvidia-tesla-k80
resources:
  limits:
    nvidia.com/gpu: 1
```

## 🔄 CI/CD 파이프라인

### GitHub Actions (예정)
```yaml
# .github/workflows/aiend.yml
name: AI Engine CI/CD

on:
  push:
    paths:
      - 'aiend/**'

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Run tests
        run: |
          cd aiend
          pip install -e .
          pytest

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Build Docker image
        run: |
          cd aiend
          docker build -t aiend:${{ github.sha }} .

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Deploy to production
        run: |
          kubectl set image deployment/aiend aiend=aiend:${{ github.sha }}
```

## 🛠️ 운영 도구

### 관리 스크립트

#### 데이터베이스 백업
```bash
#!/bin/bash
# scripts/backup_db.sh
DATE=$(date +%Y%m%d_%H%M%S)
cp /app/data/codebase.db /app/backups/codebase_${DATE}.db
```

#### 로그 로테이션
```bash
#!/bin/bash
# scripts/rotate_logs.sh
find /app/logs -name "*.log" -mtime +7 -delete
```

### 모니터링 알림

#### Slack 알림
```python
# 에러 발생 시 Slack 알림
async def send_error_alert(error: Exception):
    webhook_url = os.getenv("SLACK_WEBHOOK_URL")
    message = {
        "text": f"AI Engine Error: {str(error)}",
        "channel": "#alerts"
    }
    async with httpx.AsyncClient() as client:
        await client.post(webhook_url, json=message)
```
