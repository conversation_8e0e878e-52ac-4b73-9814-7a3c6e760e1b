# aiend API Documentation

## Introduction

`aiend` API는 코드베이스에 대한 지능형 검색, 분석 및 상호작용 기능을 제공합니다. 의미론적 검색, 구조적 분석, 시간적 분석 및 RAG(Retrieval-Augmented Generation)를 활용하여 개발자가 코드베이스를 더 깊이 이해하고 상호작용할 수 있도록 돕습니다.

## Authentication

대부분의 엔드포인트는 인증이 필요하며, 각 요청은 사용자의 권한(예: `read`, `write`)을 확인합니다.

---

## 1. Search API (`/api/v1/search`)

코드베이스 검색 및 RAG 관련 기능을 제공합니다.

---

### `POST /query` - Natural Language Query

코드베이스에 대해 자연어로 질문하고, LLM을 통해 생성된 답변을 받습니다. RAG 파이프라인을 사용하여 관련 코드를 컨텍스트로 활용합니다.

**Request Body:** `QueryRequest`
```json
{
  "query": "string",
  "repository_id": "string (optional)",
  "max_results": "integer (optional, default: 5)"
}
```

**Response Body:** `QueryResponse`
```json
{
  "answer": "string",
  "search_results": "array[SearchResultResponse]",
  "execution_time": "float"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/search/query" \
-H "Content-Type: application/json" \
-d '{
  "query": "How is user authentication implemented?",
  "repository_id": "my-awesome-repo"
}'
```

---

### `POST /` - Codebase Search

다양한 모드(semantic, structural, temporal, hybrid)를 사용하여 코드베이스를 검색합니다.

**Request Body:** `SearchRequest`
```json
{
  "query": "string",
  "mode": "string (optional, default: auto)",
  "context_types": "array[string] (optional)",
  "file_patterns": "array[string] (optional)",
  "max_results": "integer (optional, default: 10)",
  "include_history": "boolean (optional, default: true)"
}
```

**Response Body:** `SearchResponse`
```json
{
  "results": "array[SearchResultResponse]",
  "total_count": "integer",
  "query": "string",
  "mode": "string",
  "execution_time": "float",
  "suggestions": "array[string]"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/search/" \
-H "Content-Type: application/json" \
-d '{
  "query": "Database connection handling",
  "mode": "hybrid",
  "file_patterns": ["*.py"]
}'
```

---

### `POST /encode` - Text to Vector Embedding

주어진 텍스트를 벡터 임베딩으로 변환합니다.

**Request Body:** `EncodeRequest`
```json
{
  "text": "string",
  "repository_id": "string (optional)"
}
```

**Response Body:** `EncodeResponse`
```json
{
  "vector": "array[float]",
  "model": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/search/encode" \
-H "Content-Type: application/json" \
-d '{
  "text": "function to calculate fibonacci sequence"
}'
```

---

### `GET /suggestions` - Get Search Suggestions

입력된 쿼리에 대한 검색어를 제안합니다.

**Query Parameters:**
- `query`: `string` - 부분 또는 전체 검색 쿼리
- `limit`: `integer (optional, default: 10)` - 반환할 제안의 최대 수

**Response Body:**
```json
{
  "suggestions": [
    "database connection pool",
    "database migration scripts"
  ]
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/search/suggestions?query=database%20connect"
```

---

### `GET /modes` - Get Search Modes

사용 가능한 검색 모드 목록을 반환합니다.

**Response Body:**
```json
{
  "modes": [
    "auto",
    "semantic",
    "structural",
    "temporal",
    "hybrid"
  ]
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/search/modes"
```

---

### `GET /context-types` - Get Context Types

검색 가능한 컨텍스트 타입(예: function, class) 목록을 반환합니다.

**Response Body:**
```json
{
  "context_types": [
    "function",
    "class",
    "method",
    "interface",
    "snippet"
  ]
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/search/context-types"
```

---

### `GET /vector/status` - Vector Store Status

벡터 스토어의 상태를 조회합니다.

**Query Parameters:**
- `repository_id`: `string (optional)` - 상태를 조회할 특정 리포지토리의 ID

**Response Body:**
```json
{
  "available": "boolean",
  "collection_info": "object | null",
  "host": "string",
  "port": "integer",
  "collection_name": "string",
  "model_name": "string"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/search/vector/status?repository_id=my-awesome-repo"
```

---

## 2. Analysis API (`/api/v1/analysis`)

코드베이스 인덱싱 및 통계 관련 기능을 제공합니다.

---

### `POST /index` - Start Codebase Indexing

코드베이스에 대한 인덱싱을 백그라운드 작업으로 시작합니다. 특정 언어나 파일 패턴을 지정할 수 있습니다.

**Request Body:** `IndexRequest`
```json
{
  "force_reindex": "boolean (optional, default: false)",
  "languages": "array[string] (optional)",
  "file_patterns": "array[string] (optional)"
}
```

**Response Body:** `IndexResponse`
```json
{
  "task_id": "string",
  "status": "string",
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/analysis/index" \
-H "Content-Type: application/json" \
-d '{
  "languages": ["python", "javascript"],
  "file_patterns": ["src/**/*.py", "app/**/*.js"]
}'
```

---

### `GET /index/{task_id}` - Get Indexing Status

특정 인덱싱 작업의 상태를 조회합니다. 진행률, 처리된 파일 수, 발견된 함수/클래스 수 등의 정보를 포함합니다.

**Path Parameters:**
- `task_id`: `string` - 조회할 작업의 ID

**Response Body:** `IndexStatusResponse`
```json
{
  "task_id": "string",
  "status": "string",
  "progress": "float",
  "files_processed": "integer",
  "functions_found": "integer",
  "classes_found": "integer",
  "errors": "integer",
  "start_time": "string (datetime)",
  "end_time": "string (datetime, optional)",
  "duration": "float (seconds, optional)"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/analysis/index/a1b2c3d4-e5f6-7890-1234-567890abcdef"
```

---

### `POST /reindex` - Force Reindexing

전체 코드베이스에 대한 강제 재인덱싱을 시작합니다. 기존 인덱스를 삭제하고 새로 생성합니다.

**Response Body:**
```json
{
  "task_id": "string",
  "status": "string",
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/analysis/reindex"
```

---

### `GET /stats` - Get System Statistics

시스템의 다양한 통계(데이터베이스, 벡터 스토어, 리포지토리)를 조회합니다.

**Response Body:** `StatsResponse`
```json
{
  "database": {
    "files": "integer",
    "functions": "integer",
    "classes": "integer"
  },
  "vector_store": {
    "total_documents": "integer",
    "collection_name": "string"
  },
  "repository": {
    "path": "string",
    "total_files": "integer"
  }
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/analysis/stats"
```

---

### `GET /files` - List Files

코드베이스에 포함된 파일 목록을 조회합니다. 언어별 필터링 및 페이지네이션을 지원합니다.

**Query Parameters:**
- `language`: `string (optional)` - 특정 프로그래밍 언어로 필터링
- `limit`: `integer (optional, default: 100)`
- `offset`: `integer (optional, default: 0)`

**Response Body:**
```json
{
  "files": [
    {
      "path": "string",
      "language": "string",
      "size_bytes": "integer",
      "lines_count": "integer",
      "last_modified": "string (datetime)"
    }
  ],
  "total": "integer",
  "limit": "integer",
  "offset": "integer"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/analysis/files?language=python&limit=50"
```

---

### `GET /functions` - List Functions

코드베이스에 포함된 함수 목록을 조회합니다. 파일 경로 또는 이름 패턴으로 필터링할 수 있습니다.

**Query Parameters:**
- `file_path`: `string (optional)` - 특정 파일 내의 함수로 필터링
- `name_pattern`: `string (optional)` - 함수 이름에 대한 패턴 매칭 (e.g., `get_*`)
- `limit`: `integer (optional, default: 100)`
- `offset`: `integer (optional, default: 0)`

**Response Body:**
```json
{
  "functions": [
    {
      "name": "string",
      "file_path": "string",
      "start_line": "integer",
      "end_line": "integer",
      "complexity": "integer",
      "parameters": "array[object]",
      "return_type": "string"
    }
  ],
  "total": "integer",
  "limit": "integer",
  "offset": "integer"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/analysis/functions?name_pattern=handle_*"
```

---

## 3. LLM API (`/api/v1/llm`)

LLM과의 직접적인 상호작용 및 관리 기능을 제공합니다.

---

### `POST /chat` - Chat with LLM

LLM과 직접 채팅합니다. 특정 제공자, 모델, 응답 전략을 선택할 수 있습니다.

**Request Body:** `ChatRequest`
```json
{
  "message": "string",
  "max_tokens": "integer (optional, default: 8000)",
  "provider": "string (optional)",
  "model": "string (optional)",
  "strategy": "string (optional)",
  "save_context": "boolean (optional, default: false)"
}
```

**Response Body:** `ChatResponse`
```json
{
  "content": "string",
  "confidence": "float",
  "strategy_used": "string",
  "execution_time": "float",
  "context_tokens": "integer",
  "providers_used": "array[string]",
  "suggestions": "array[string]",
  "context_id": "string (optional)"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/llm/chat" \
-H "Content-Type: application/json" \
-d '{
  "message": "Explain the SOLID principles with code examples in Python.",
  "provider": "ollama",
  "model": "llama3"
}'
```

---

### `GET /providers` - Get LLM Providers

사용 가능한 LLM 제공자(Ollama, OpenAI 등) 목록과 상태, 모델 정보, 사용량 통계를 반환합니다.

**Response Body:** `ProvidersResponse`
```json
{
  "providers": [
    {
      "name": "string",
      "available": "boolean",
      "models": "array[string]",
      "default_model": "string",
      "usage_stats": "object"
    }
  ],
  "default_provider": "string"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/llm/providers"
```

---

### `GET /models` - Get Available Models

사용 가능한 LLM 모델 목록을 반환합니다. 특정 제공자를 지정하여 필터링할 수 있습니다.

**Query Parameters:**
- `provider`: `string (optional)` - 모델 목록을 조회할 제공자 이름

**Response Body:**
```json
{
  "models_by_provider": {
    "ollama": ["llama3", "codellama"],
    "openai": ["gpt-4", "gpt-3.5-turbo"]
  }
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/llm/models?provider=ollama"
```

---

### `GET /strategies` - Get Response Strategies

사용 가능한 응답 생성 전략 목록과 설명을 반환합니다.

**Response Body:**
```json
{
  "strategies": [
    {
      "name": "string",
      "description": "string"
    }
  ]
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/llm/strategies"
```

---

### `GET /usage` - Get Usage Statistics

LLM 사용량 통계를 조회합니다. 전체 및 제공자별 요청 수, 토큰 수 등을 포함합니다.

**Response Body:**
```json
{
  "total_requests": "integer",
  "total_tokens": "integer",
  "total_errors": "integer",
  "providers": {
    "ollama": {"total_requests": 100, "total_tokens": 250000, "errors": 5},
    "openai": {"total_requests": 50, "total_tokens": 150000, "errors": 1}
  }
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/llm/usage"
```

---

### `GET /context/{context_id}` - Get Saved Context

저장된 대화 컨텍스트를 조회합니다.

**Path Parameters:**
- `context_id`: `string` - 조회할 컨텍스트의 ID

**Response Body:**
```json
{
  "context_id": "string",
  "content": "string",
  "created_at": "string (datetime)",
  "tokens": "integer"
}
```

---

### `DELETE /context/{context_id}` - Delete Saved Context

저장된 대화 컨텍스트를 삭제합니다.

**Path Parameters:**
- `context_id`: `string` - 삭제할 컨텍스트의 ID

**Response Body:**
```json
{
  "message": "string"
}
```

---

## 4. Ollama Management API (`/api/v1/ollama`)

Ollama 모델의 설치 및 관리를 위한 기능을 제공합니다.

---

### `GET /models` - List Installed Models

로컬에 설치된 Ollama 모델 목록과 상세 정보를 반환합니다.

**Response Body:** `ModelsResponse`
```json
{
  "models": [
    {
      "name": "string",
      "size": "string",
      "digest": "string",
      "modified_at": "string (datetime)",
      "details": "object"
    }
  ],
  "total": "integer"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/ollama/models"
```

---

### `POST /models/pull` - Pull a Model

Ollama Hub에서 모델을 다운로드하고 설치합니다. 이 작업은 백그라운드에서 실행됩니다.

**Request Body:** `PullRequest`
```json
{
  "model_name": "string"
}
```

**Response Body:** `PullResponse`
```json
{
  "task_id": "string",
  "status": "string",
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/ollama/models/pull" \
-H "Content-Type: application/json" \
-d '{"model_name": "codellama:7b"}'
```

---

### `DELETE /models/{model_name}` - Delete a Model

로컬에 설치된 Ollama 모델을 삭제합니다.

**Path Parameters:**
- `model_name`: `string` - 삭제할 모델의 이름 (e.g., `codellama:7b`)

**Response Body:**
```json
{
  "message": "string"
}
```

**Example cURL:**
```bash
curl -X DELETE "http://localhost:8000/api/v1/ollama/models/codellama:7b"
```

---

### `GET /models/{model_name}` - Get Model Info

특정 모델의 상세 정보를 조회합니다.

**Path Parameters:**
- `model_name`: `string` - 조회할 모델의 이름

**Response Body:** `ModelInfo`
```json
{
  "name": "string",
  "size": "string",
  "digest": "string",
  "modified_at": "string (datetime)",
  "details": {
    "format": "string",
    "family": "string",
    "parameter_size": "string",
    "quantization_level": "string"
  }
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/ollama/models/codellama:7b"
```

---

### `GET /status` - Get Ollama Service Status

Ollama 서비스의 현재 상태, 연결 정보, 설치된 모델 수를 확인합니다.

**Response Body:**
```json
{
  "status": "string (available/unavailable)",
  "base_url": "string",
  "models_count": "integer",
  "models": "array[string]"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/ollama/status"
```

---

### `GET /recommended` - Get Recommended Models

코드 분석 및 생성 작업에 추천되는 모델 목록과 용도를 반환합니다.

**Response Body:**
```json
{
  "recommended_models": [
    {
      "name": "string",
      "description": "string",
      "size": "string",
      "use_case": "string"
    }
  ]
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/ollama/recommended"
```
{{ ... }}

---

## 5. Webhook API (`/api/v1/webhook`)

외부 시스템(예: Git 서버)과의 연동을 위한 Webhook 엔드포인트를 제공합니다.

---

### `POST /git-changes` - Receive Git Changes

Git 변경사항(clone, pull, branch switch 등)을 수신하여 코드베이스 분석을 트리거합니다. 이 작업은 백그라운드에서 실행됩니다.

**Request Body:** `GitChangeNotification`
```json
{
  "repository_id": "string",
  "repository_name": "string",
  "repository_path": "string",
  "action": "string (clone, pull, branch_switch)",
  "branch": "string",
  "commit_hash": "string (optional)",
  "changed_files": "array[string] (optional)"
}
```

**Response Body:** `WebhookResponse`
```json
{
  "success": "boolean",
  "message": "string",
  "task_id": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/webhook/git-changes" \
-H "Content-Type: application/json" \
-d '{
  "repository_id": "my-awesome-project",
  "repository_name": "my-awesome-project",
  "repository_path": "/path/to/repo",
  "action": "pull",
  "branch": "main",
  "commit_hash": "a1b2c3d4"
}'
```

---

### `POST /repository-scan` - Trigger Repository Scan

특정 저장소의 전체 스캔을 수동으로 트리거합니다. 새로운 저장소를 추가했거나 전체 재분석이 필요할 때 사용합니다.

**Request Body:**
```json
{
  "repository_path": "string",
  "repository_name": "string",
  "force_reindex": "boolean (optional, default: false)"
}
```

**Response Body:** `WebhookResponse`
```json
{
  "success": "boolean",
  "message": "string",
  "task_id": "string"
}
```

**Example cURL:**
```bash
curl -X POST "http://localhost:8000/api/v1/webhook/repository-scan" \
-H "Content-Type: application/json" \
-d '{
  "repository_path": "/path/to/new-repo",
  "repository_name": "new-repo",
  "force_reindex": true
}'
```

---

### `GET /tasks/{task_id}` - Get Task Status

백그라운드 작업(분석, 스캔)의 상태를 조회합니다. **(Note: 현재 구현은 모의(mock) 응답을 반환합니다.)**

**Path Parameters:**
- `task_id`: `string` - 조회할 작업의 ID

**Response Body:**
```json
{
  "task_id": "string",
  "status": "string (e.g., pending, running, completed, failed)",
  "result": "object (optional)"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/webhook/tasks/git_change_my-awesome-project_1678886400"
```

---

## 6. Health API (`/api/v1/health`)

시스템의 상태를 확인하는 Health Check 엔드포인트를 제공합니다.

---

### `GET /` - Basic Health Check

서비스의 기본적인 동작 상태를 확인합니다.

**Response Body:**
```json
{
  "status": "ok"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/health/"
```

---

### `GET /detailed` - Detailed Health Check

시스템의 주요 컴포넌트(데이터베이스, 벡터 스토어, LLM 제공자)의 상세 상태를 확인합니다.

**Response Body:**
```json
{
  "status": "ok",
  "version": "string",
  "components": {
    "database": {
      "status": "ok",
      "path": "string"
    },
    "vector_store": {
      "status": "ok",
      "host": "string",
      "port": "integer"
    },
    "llm_providers": {
      "ollama": {"status": "available"},
      "openai": {"status": "unavailable"}
    }
  }
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/health/detailed"
```

---

### `GET /ready` - Readiness Probe

Kubernetes Readiness Probe를 위한 엔드포인트입니다. 서비스가 요청을 처리할 준비가 되었는지 확인합니다.

**Response Body:**
```json
{
  "status": "ready"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/health/ready"
```

---

### `GET /live` - Liveness Probe

Kubernetes Liveness Probe를 위한 엔드포인트입니다. 서비스가 살아있는지 확인합니다.

**Response Body:**
```json
{
  "status": "live"
}
```

**Example cURL:**
```bash
curl -X GET "http://localhost:8000/api/v1/health/live"



aiend 서비스 API URL 목록 (주요 엔드포인트)

---

### 1. Embedding & AI 분석 API
- POST   /api/embedding            : 텍스트(또는 코드) 임베딩 벡터 생성
- POST   /api/batch-embedding      : 여러 텍스트 임베딩 벡터 일괄 생성
- POST   /api/rerank               : 쿼리+문서 목록 리랭킹 점수 계산

### 2. 코드 요약/설명/문맥 분석
- POST   /api/summary              : 코드/텍스트 요약 생성
- POST   /api/explain              : 코드/텍스트 설명 생성
- POST   /api/context              : 코드/문서 문맥 정보 추출

### 3. 기타 AI 기능
- POST   /api/translate            : 텍스트 번역
- POST   /api/chat                 : AI 챗봇 대화

### 4. 상태/헬스체크
- GET    /up                       : aiend 서비스 기본 헬스체크
- GET    /api/health               : aiend API 헬스체크
- GET    /api/v1/health/detailed   : 상세 상태
- GET    /api/v1/health/ready      : Readiness Probe
- GET    /api/v1/health/live       : Liveness Probe

---

**참고:**
- 모든 API는 기본적으로 `http://localhost:8000` (혹은 컨테이너 내부에서는 `aiend:8000`)을 prefix로 사용합니다.
- 실제 사용 시에는 각 엔드포인트의 파라미터 및 Request Body/Response Body는 aiend 공식 문서 또는 OpenAPI/Swagger 명세를 참고하세요.
- 엔드포인트 명/파라미터는 실제 aiend 구현에 따라 다를 수 있으니, 소스의 라우트/컨트롤러를 참고하여 맞춤 확인 필요.

