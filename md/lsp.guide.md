# LSP & Tree-sitter 통합 서버 설계 가이드 (2025-07 최신)

## 1. 시스템 개요 및 실행 모드
- Node.js 기반 LSP + Tree-sitter 통합 서버
- REST API, LSP(JSON-RPC) 동시 지원
- 실행 모드: REST API(기본), LSP 클라이언트(내부 LSP 프로세스), LSP 프로토콜 서버(독립)
- 환경변수로 모드 전환 (LSP_MODE, USE_LSP_CLIENT 등)

## 2. 주요 폴더/파일 구조
- src/main.ts: 실행 진입점, 모드별 분기
- src/api/server.ts: REST API 서버, LSP 인스턴스 주입
- src/lsp/server.ts: LSP 기능(정의/참조/심볼 등) 메서드화, createConnection 제거
- src/lsp/client.ts: LSPClientWrapper, 내부 LSP 서버 프로세스 관리
- src/analyzer/parser.ts: tree-sitter 공식 require 방식, 타입 선언 보완
- tsconfig.json, .env(.example), Dockerfile: 최신화

## 3. REST & LSP API 통합 전략
- REST API: DB 직접 접근, GET/POST 기반 심볼/정의/참조/분석/리포지토리/통계 등 제공
- LSP 클라이언트 모드: 내부 LSP 서버와 연동, 위치기반 POST API(`/lsp/definition` 등) 제공
- LSP 프로토콜 서버: IDE/에디터와 직접 연동(JSON-RPC)

## 4. 환경변수 및 실행 예시
```env
PORT=7107
NODE_ENV=development
LOG_LEVEL=INFO
LSP_MODE=false
USE_LSP_CLIENT=true
DB_PATH=./data_lsp/codebase.sqlite
REPOSITORIES_PATH=./repositories
```

```bash
npm run start           # REST API 모드
npm run start:lsp-client # LSP 클라이언트 모드
npm run start:lsp-only    # LSP 프로토콜 서버만
```

## 5. Dockerfile/tsconfig.json 주요 변경
- Dockerfile: 빌드/실행 모드 분리, ENV USE_LSP_CLIENT 등 반영
- tsconfig.json: strict, sourceMap, declaration 등 최신화

## 6. 주요 변경점 요약
- LSPServer: createConnection 제거, 기능 메서드화
- LSPClientWrapper: 내부 LSP 서버 프로세스 관리, dispose 등 타입 보완
- main.ts: 환경변수 기반 모드 분기, LSP 인스턴스 API 서버에 주입
- routes.ts: globalThis.lspClientWrapper 타입 선언, error 핸들링, map 콜백 타입 등 TS 오류 대응
- parser.ts: require 방식, Parser 네임스페이스 타입 선언, 암시적 any 제거

## 7. API/워크플로우/예시
- 기존 문서의 상세 API, 워크플로우, 예시, 타입 등은 최신 구조에 맞게 그대로 사용 가능
- LSP 위치기반 POST API는 LSP 클라이언트 모드에서만 활성화됨을 명시

---

```markdown
# LSP & Tree-sitter 통합 분석 서버 설계 문서

> CodeBase Intelligence System을 위한 통합 코드 분석 컨테이너 설계
> **목적**: `tree-sitter`를 사용한 정적 코드 분석과 표준 LSP(Language Server Protocol) 기능을 **통합**하여, IDE 클라이언트와 내부 서비스(Backend/Frontend) 모두에게 일관된 코드 인텔리전스를 제공합니다.

---

## 1. 🚀 주요 목표 및 전략

- **통합 서비스 제공**: 단일 Node.js 서버에서 아래 두 가지 인터페이스를 모두 제공합니다.
    1.  **LSP (JSON-RPC)**: VSCode와 같은 IDE 클라이언트를 위해 `textDocument/definition`, `textDocument/references` 등 표준 LSP 기능을 제공합니다.
    2.  **REST API (HTTP)**: Backend, Frontend 등 내부 서비스가 코드 분석 데이터(심볼, 참조, AST 등)에 쉽게 접근할 수 있도록 RESTful API를 제공합니다. (외부 포트: `7107`)
- **`tree-sitter` 기반 중앙 분석**: `tree-sitter`를 활용하여 다중 언어(Ruby, JS, TS, Python) 코드를 분석하고, 그 결과를 SQLite DB에 저장합니다. LSP와 REST API는 모두 이 중앙 데이터베이스를 기반으로 동작하여 데이터 일관성을 유지합니다.
- **독립된 마이크로서비스**: `lsp-server` 컨테이너는 코드 분석에 대한 모든 책임을 가지며, 다른 서비스로부터 독립적으로 개발 및 배포됩니다.
- **개발 디렉토리**: 모든 관련 코드는 `lsp-server/` 디렉토리 하위에서 개발합니다.

---

## 2. 📁 디렉토리 구조 (`lsp-server/`)

```
/lsp-server
├── Dockerfile
├── docker-compose.yml
├── package.json
├── tsconfig.json
├── README.md
├── src/
│   ├── main.ts               # LSP, REST API 서버를 모두 실행하는 메인 엔트리포인트
│   ├── database.ts           # SQLite 연동 및 스키마 관리
│   ├── analyzer/
│   │   ├── index.ts          # Tree-sitter 분석기 총괄
│   │   ├── parser.ts         # Tree-sitter 파서 래퍼
│   │   └── queries.ts        # Tree-sitter 심볼/참조 추출 쿼리
│   ├── lsp/
│   │   └── server.ts         # LSP 서버 구현 (onDefinition, onReferences 등)
│   ├── api/
│   │   ├── server.ts         # Express 기반 REST API 서버 구현
│   │   └── routes.ts         # API 엔드포인트 라우팅
│   └── shared/
│       ├── types.ts          # 공용 타입 (Symbol, Reference 등)
│       └── logger.ts         # 로깅 유틸
└── data_lsp/
    └── codebase.sqlite       # 분석 데이터를 저장할 SQLite DB 파일
```

---

## 3. 🧰 기술 스택

| 구분 | 기술 | 목적 |
|------|------|------|
| **언어/런타임** | Node.js, TypeScript | 서버 개발 |
| **LSP** | `vscode-languageserver` | 표준 LSP 기능 구현 |
| **REST API** | `Express` | HTTP 서버 구축 |
| **코드 파싱** | `tree-sitter` | 다중 언어 AST 분석 |
| **데이터베이스** | `better-sqlite3` | 분석된 심볼/참조 정보 저장 및 조회 |
| **컨테이너화** | Docker | 서비스 분리 및 배포 |
| **통신 방식** | LSP: JSON-RPC (`stdio`) / REST: HTTP (`localhost:7107`) |

---

## 4. 🔌 주요 구성 요소 상세 설계

### 4.1. Tree-sitter 분석기 (`src/analyzer/`)
- **역할**: 소스 코드를 정적으로 분석하여 `codebase.sqlite` 데이터베이스를 구축합니다.
- **프로세스**:
    1. `tree-sitter`와 언어별 쿼리(`queries.ts`)를 사용해 코드에서 심볼 정의(클래스, 함수 등)와 참조를 추출합니다.
    2. 추출된 모든 정보를 SQLite DB의 `symbols`, `references` 테이블에 저장합니다.
    3. 이 프로세스는 서버 시작 시 실행되거나, `POST /api/v1/analysis/scan` API를 통해 트리거될 수 있습니다.

### 4.2. SQLite 데이터베이스 (`src/database.ts`)
- LSP와 REST API가 공유하는 중앙 데이터 저장소입니다.
- **Schema**: (이전과 동일)
    ```sql
    CREATE TABLE IF NOT EXISTS symbols ( ... );
    CREATE TABLE IF NOT EXISTS "references" ( ... );
    ```

### 4.3. LSP 서버 (`src/lsp/server.ts`)
- **역할**: IDE 클라이언트의 LSP 요청을 처리합니다.
- **프로세스**:
    - `onInitialize`: LSP 서버 초기화 및 기능 목록을 클라이언트에 알립니다.
    - `onDefinition`: 요청된 심볼의 정의 위치를 `codebase.sqlite` DB에서 조회하여 반환합니다.
    - `onReferences`: 요청된 심볼의 모든 참조 위치를 DB에서 조회하여 반환합니다.
    - `onCompletion`: (선택적 확장) 현재 커서 위치를 기반으로 DB에서 관련 심볼을 찾아 자동완성 목록을 제공합니다.
- **통신**: `stdio`를 통한 JSON-RPC 통신을 표준으로 사용합니다.

### 4.4. REST API 서버 (`src/api/server.ts`)
- **역할**: 내부 서비스의 HTTP 요청을 처리합니다.
- **Port**: `7107`
- **주요 Endpoints**:
    - `POST /api/v1/analysis/scan`: 코드 분석을 수동으로 트리거합니다.
    - `GET /api/v1/symbols/definition`: 심볼의 정의 위치를 조회합니다.
    - `GET /api/v1/symbols/references`: 심볼의 모든 참조 위치를 조회합니다.
    - `GET /api/v1/files/:path/ast`: 특정 파일의 `tree-sitter` AST를 JSON으로 반환합니다.

---

## 5. 🔄 시스템 연동 흐름

```text
+-----------------+   JSON-RPC (stdio)   +-------------------------+
| IDE (VSCode)    | <------------------> |                         |
+-----------------+                      |   LSP & Tree-sitter     |   REST API (7107)   +------------------+
                                         |   통합 서버 (Node.js)   | <-----------------> | Frontend/Backend |
+------------------+  (File System)   |                         |                     +------------------+
| Source Code     | <----------------> |                         |
+------------------+                     +-----------+-------------+
                                                     |
                                          (SQLite)   |
                                                     ▼
                                         +-------------------------+
                                         |   codebase.sqlite DB    |
                                         +-------------------------+
```

1.  **분석**: `lsp-server`는 시작 시 또는 API 요청 시 Docker 볼륨으로 마운트된 소스 코드를 `tree-sitter`로 분석하여 `codebase.sqlite`에 저장합니다.
2.  **LSP 요청**: IDE 사용자가 'Go to Definition'과 같은 기능을 사용하면, LSP 클라이언트가 `lsp-server`에 JSON-RPC 요청을 보냅니다. 서버는 DB를 조회하여 결과를 반환합니다.
3.  **REST API 요청**: `backend` 서비스가 특정 함수의 모든 사용 위치를 파악해야 할 경우, `lsp-server`의 `GET /api/v1/symbols/references` API를 호출합니다. 서버는 DB를 조회하여 JSON으로 응답합니다.

---

## 6. 🐳 Docker 설정

### 6.1. Dockerfile (`lsp-server/Dockerfile`)
```Dockerfile
FROM node:18-slim
WORKDIR /usr/src/app

# tree-sitter 빌드를 위한 의존성 설치
RUN apt-get update && apt-get install -y python3 make g++ && rm -rf /var/lib/apt/lists/*

COPY package*.json ./
RUN npm install

COPY . .

RUN npm run build # tsc 컴파일

EXPOSE 7107
CMD [ "node", "dist/main.js" ]
```

### 6.2. docker-compose.yml (루트 또는 `lsp-server/` 내)
```yaml
version: '3.8'
services:
  lsp-server:
    build:
      context: ./lsp-server
    container_name: codebase-lsp-server
    ports:
      - "7107:7107"
    volumes:
      - ./backend:/code/backend
      - ./frontend:/code/frontend
      - ./aiend:/code/aiend
      - lsp_server_data:/usr/src/app/data
    networks:
      - default-network

volumes:
  lsp_server_data:

# ... (other services)
```

---

## 7. ✅ To-Do 목록

- [ ] `lsp-server` 디렉토리 및 기본 구조 생성
- [ ] `package.json`에 `vscode-languageserver`, `express`, `tree-sitter`, `better-sqlite3` 등 의존성 추가
- [ ] `src/database.ts`: SQLite DB 연결 및 테이블 생성 로직 구현
- [ ] `src/analyzer/`: `tree-sitter` 파싱 및 DB 저장 로직 구현
- [ ] `src/lsp/server.ts`: LSP 요청 핸들러 구현 (DB 조회 로직 연동)
- [ ] `src/api/server.ts`: REST API 엔드포인트 구현 (DB 조회 로직 연동)
- [ ] `src/main.ts`: LSP 서버와 REST API 서버를 함께 시작하는 로직 구현
- [ ] `Dockerfile` 및 `docker-compose.yml` 작성 및 통합 테스트
- [ ] VSCode에서 LSP 연동 테스트 및 `backend`에서 REST API 호출 테스트

