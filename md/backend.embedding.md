`Rails 8`에서 Dengcao의 **임베딩 + 리랭커** 모델을 **Ollama API**를 통해 호출하고, 이를 기반으로 벡터 검색 및 리랭킹까지 수행하는 기본 구조 코드를 아래에 보여드릴게요.

> 💡 전제: `ollama`가 이미 백그라운드에서 `dengcao/Qwen3-Embedding-8B` 와 `dengcao/Qwen3-Reranker-4B` 모델을 실행 중이고, Rails 백엔드에서 이를 HTTP API로 호출합니다.

---

## ✅ Gemfile

```ruby
gem 'httparty'
gem 'qdrant-ruby', require: 'qdrant'
```

---

## ✅ `embedding_service.rb`

```ruby
# app/services/embedding_service.rb
class EmbeddingService
  include HTTParty
  base_uri 'http://localhost:11434'

  def self.embed(text)
    response = post('/api/generate', body: {
      model: "dengcao/Qwen3-Embedding-8B:Q5_K_M",
      prompt: "<|user|>\n#{text}\n<|embedding|>",
      stream: false
    }.to_json, headers: { 'Content-Type' => 'application/json' })

    if response.success?
      response['response'].strip.split(',').map(&:to_f)
    else
      raise "Embedding failed: #{response.body}"
    end
  end
end
```

---

## ✅ `reranker_service.rb`

```ruby
# app/services/reranker_service.rb
class RerankerService
  include HTTParty
  base_uri 'http://localhost:11434'

  def self.rerank(query, documents)
    documents.map do |doc|
      prompt = "<|user|>\n[Q] #{query}\n[D] #{doc}\n<|score|>"
      response = post('/api/generate', body: {
        model: "dengcao/Qwen3-Reranker-4B:Q5_K_M",
        prompt: prompt,
        stream: false
      }.to_json, headers: { 'Content-Type' => 'application/json' })

      score = response['response'].strip.to_f
      { doc: doc, score: score }
    end.sort_by { |r| -r[:score] }
  end
end
```

---

## ✅ `qdrant_service.rb`

```ruby
# app/services/qdrant_service.rb
require 'qdrant'

class QdrantService
  def self.client
    @client ||= Qdrant::Client.new(url: 'http://localhost:6333')
  end

  def self.search(collection:, vector:, top_k: 5)
    client.search(
      collection_name: collection,
      vector: vector,
      limit: top_k,
      with_payload: true
    ).map { |result| result.payload['text'] }
  end
end
```

---

## ✅ 예제 Controller

```ruby
# app/controllers/search_controller.rb
class SearchController < ApplicationController
  def search
    query = params[:query]
    embedding = EmbeddingService.embed(query)
    results = QdrantService.search(collection: 'docs', vector: embedding, top_k: 5)
    reranked = RerankerService.rerank(query, results)

    render json: { query: query, results: reranked }
  end
end
```

---

## ✅ routes.rb

```ruby
# config/routes.rb
Rails.application.routes.draw do
  get 'search', to: 'search#search'
end
```

---

## ✅ 실행 예시

```bash
curl "http://localhost:3000/search?query=파스칼과 C의 차이점"
```

---

## ✅ 결과 형식

```json
{
  "query": "파스칼과 C의 차이점",
  "results": [
    { "doc": "파스칼은 구조적 프로그래밍 언어이며...", "score": 0.912 },
    { "doc": "C언어는 저수준 메모리 제어가 가능하고...", "score": 0.865 },
    ...
  ]
}
```

---

## ✅ 요약

| 컴포넌트               | 역할         |
| ------------------ | ---------- |
| `EmbeddingService` | 쿼리를 벡터로 변환 |
| `QdrantService`    | 벡터 유사도 검색  |
| `RerankerService`  | 결과 정렬      |
| `SearchController` | 위 서비스 조합   |

---

필요하다면 Qdrant에 문서 벡터를 저장하거나, FastAPI 기반으로 동일한 구조를 만들 수도 있습니다. 그쪽도 원하시나요?
