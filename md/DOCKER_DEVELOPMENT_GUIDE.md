# Docker 개발 환경 가이드

## 🏗️ 모노레포 구조

```
codebase-sqlite/
├── docker-compose.yml          # 전체 시스템 통합 실행
├── aiend/
│   ├── Dockerfile
│   ├── docker-compose.yml      # AI Engine 개별 실행
│   └── faiss/server.py
├── backend/
│   ├── Dockerfile.dev
│   ├── docker-compose.yml      # Backend 개별 실행
│   └── ...
├── frontend/
│   ├── Dockerfile
│   ├── docker-compose.yml      # Frontend 개별 실행
│   ├── nginx.conf
│   └── ...
├── faiss-gpu/
│   ├── Dockerfile
│   ├── docker-compose.yml      # FAISS GPU 개별 실행
│   └── server.py
└── qdrant/
    ├── docker-compose.yml      # Qdrant 개별 실행
    └── config/config.yaml
```

## 🚀 실행 방법

### 1. 전체 시스템 실행 (권장)

```bash
# 루트 디렉토리에서
docker-compose up -d

# 로그 확인
docker-compose logs -f

# 특정 서비스 로그
docker-compose logs -f aiend
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 2. 개별 서비스 실행

#### AI Engine (aiend)
```bash
cd aiend
docker-compose up -d

# 또는 의존성 포함
docker-compose up -d aiend qdrant faiss-gpu redis
```

#### Backend
```bash
cd backend
docker-compose up -d

# AI Engine과 함께 실행
docker-compose --profile with-aiend up -d
```

#### Frontend
```bash
cd frontend

# 개발 모드
docker-compose up -d frontend-dev

# 프로덕션 모드
docker-compose --profile production up -d frontend-prod

# Backend와 함께 실행
docker-compose --profile with-backend up -d
```

#### Vector Databases
```bash
# Qdrant만
cd qdrant
docker-compose up -d

# FAISS GPU (Qdrant 포함)
cd faiss-gpu
docker-compose up -d
```

## 🔧 개발 환경 설정

### GPU 요구사항 확인
```bash
# NVIDIA GPU 확인
nvidia-smi

# Docker GPU 지원 확인
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

### 환경 변수 설정

#### aiend/.env
```env
OPENAI_API_KEY=your_openai_api_key
ANTHROPIC_API_KEY=your_anthropic_api_key
CODEBASE_DB_PATH=/app/data/codebase.db
CODEBASE_VECTOR_PATH=/app/data/chroma_db
CODEBASE_LOG_LEVEL=INFO
OLLAMA_HOST=http://host.docker.internal:11434
QDRANT_URL=http://qdrant:6333
FAISS_GPU_URL=http://faiss-gpu:8001
REDIS_URL=redis://redis:6379/0
```

## 🐛 디버깅 및 개발

### 컨테이너 접속
```bash
# AI Engine 컨테이너 접속
docker-compose exec aiend bash

# Backend 컨테이너 접속
docker-compose exec backend bash

# Frontend 컨테이너 접속 (개발 모드)
docker-compose exec frontend-dev sh
```

### 로그 모니터링
```bash
# 실시간 로그 (전체)
docker-compose logs -f

# 특정 서비스 로그
docker-compose logs -f aiend backend frontend

# 에러 로그만
docker-compose logs --tail=100 | grep -i error
```

### 헬스체크 확인
```bash
# 모든 서비스 상태
docker-compose ps

# 개별 헬스체크
curl http://localhost:7101/health  # AI Engine
curl http://localhost:7102/health  # Backend
curl http://localhost:7103         # Frontend
curl http://localhost:7105/health  # Qdrant
curl http://localhost:7107/health  # FAISS GPU
```

### 데이터 볼륨 관리
```bash
# 볼륨 목록
docker volume ls | grep codebase

# 볼륨 정리 (주의: 데이터 삭제됨)
docker-compose down -v

# 특정 볼륨 삭제
docker volume rm codebase-sqlite_qdrant_data
```

## 🔄 개발 워크플로우

### 1. 코드 변경 시
```bash
# 서비스 재빌드
docker-compose build aiend
docker-compose up -d aiend

# 또는 전체 재빌드
docker-compose build
docker-compose up -d
```

### 2. 의존성 변경 시
```bash
# Python 의존성 (aiend)
cd aiend
docker-compose build --no-cache aiend

# Ruby 의존성 (backend)
cd backend
docker-compose build --no-cache backend

# Node.js 의존성 (frontend)
cd frontend
docker-compose build --no-cache frontend-dev
```

### 3. 데이터베이스 초기화
```bash
# Qdrant 데이터 초기화
curl -X DELETE http://localhost:7105/collections/codebase

# FAISS 인덱스 초기화
curl -X POST http://localhost:7107/reset

# Backend 데이터베이스 초기화
docker-compose exec backend bundle exec rails db:reset
```

## 🚨 문제 해결

### 일반적인 문제들

#### 1. GPU 인식 안됨
```bash
# Docker GPU 런타임 확인
docker info | grep -i nvidia

# 컨테이너에서 GPU 확인
docker-compose exec faiss-gpu nvidia-smi
```

#### 2. 포트 충돌
```bash
# 포트 사용 확인
netstat -tulpn | grep :7101
lsof -i :7101

# 충돌하는 프로세스 종료
sudo kill -9 $(lsof -t -i:7101)
```

#### 3. 네트워크 연결 문제
```bash
# 네트워크 확인
docker network ls | grep codebase

# 컨테이너 간 연결 테스트
docker-compose exec aiend curl http://qdrant:6333/health
docker-compose exec backend curl http://aiend:8000/health
```

#### 4. 볼륨 권한 문제
```bash
# 권한 확인
docker-compose exec aiend ls -la /app/data

# 권한 수정
sudo chown -R 1000:1000 ./aiend/data
sudo chown -R 1000:1000 ./backend/storage
```

## 📊 성능 모니터링

### 리소스 사용량 확인
```bash
# 컨테이너 리소스 사용량
docker stats

# GPU 사용량
nvidia-smi -l 1

# 디스크 사용량
docker system df
```

### 벤치마크 테스트
```bash
# AI Engine API 테스트
curl -X POST http://localhost:7101/api/v1/search \
  -H "Content-Type: application/json" \
  -d '{"query": "test query", "limit": 10}'

# FAISS GPU 성능 테스트
curl -X POST http://localhost:7107/search \
  -H "Content-Type: application/json" \
  -d '{"vector": [0.1, 0.2, ...], "k": 10}'
```

## 🔒 보안 고려사항

1. **환경 변수**: API 키는 .env 파일에 저장하고 .gitignore에 추가
2. **네트워크**: 내부 통신은 Docker 네트워크 사용
3. **권한**: 컨테이너는 non-root 사용자로 실행
4. **데이터**: 중요한 데이터는 볼륨으로 영구 저장
