ActiveJob은 Ruby on Rails에서 백그라운드 작업(background job)을 처리하기 위한 프레임워크입니다. 다양한 큐 어댑터(Sideki<PERSON>, Resque, Delayed Job 등)를 통합적으로 사용할 수 있는 인터페이스를 제공합니다.

## 기본 사용법

### 1. Job 생성
```ruby
# 제너레이터를 사용하여 Job 생성
rails generate job ProcessPayment

# 또는 직접 생성
class ProcessPaymentJob < ApplicationJob
  queue_as :default

  def perform(user_id, amount)
    user = User.find(user_id)
    # 결제 처리 로직
    PaymentProcessor.new(user, amount).process
  end
end
```

### 2. Job 실행
```ruby
# 즉시 실행
ProcessPaymentJob.perform_now(user.id, 100)

# 백그라운드에서 실행
ProcessPaymentJob.perform_later(user.id, 100)

# 특정 시간에 실행
ProcessPaymentJob.set(wait: 5.minutes).perform_later(user.id, 100)
ProcessPaymentJob.set(wait_until: Date.tomorrow.noon).perform_later(user.id, 100)
```

### 3. 큐 설정
```ruby
class ProcessPaymentJob < ApplicationJob
  queue_as :payment_processing  # 특정 큐 사용
  
  def perform(user_id, amount)
    # 작업 내용
  end
end
```

## 고급 기능

### 1. 재시도 및 에러 처리
```ruby
class ProcessPaymentJob < ApplicationJob
  queue_as :default
  
  retry_on StandardError, wait: 5.seconds, attempts: 3
  discard_on ActiveJob::DeserializationError

  def perform(user_id, amount)
    user = User.find(user_id)
    raise StandardError, "Payment failed" if some_condition
  end
end
```

### 2. 콜백 사용
```ruby
class ProcessPaymentJob < ApplicationJob
  before_enqueue do |job|
    # 큐에 추가되기 전에 실행
  end
  
  before_perform do |job|
    # 작업 시작 전에 실행
  end
  
  after_perform do |job|
    # 작업 완료 후 실행
  end
  
  around_perform do |job, block|
    # 작업 전후로 실행
    start_time = Time.current
    block.call
    Rails.logger.info "Job completed in #{Time.current - start_time} seconds"
  end
end
```

### 3. 직렬화 가능한 객체 전달
```ruby
class ProcessUserJob < ApplicationJob
  def perform(user)
    # User 객체를 직접 전달 가능 (Global ID 사용)
    user.process_something
  end
end

# 사용
ProcessUserJob.perform_later(current_user)
```

## 설정

### 1. 어댑터 설정 (config/application.rb)
```ruby
# Sidekiq 사용
config.active_job.queue_adapter = :sidekiq

# Resque 사용
config.active_job.queue_adapter = :resque

# 테스트 환경에서는 inline 사용
config.active_job.queue_adapter = :inline
```

### 2. 큐 이름 설정
```ruby
# config/application.rb
config.active_job.queue_name_prefix = "myapp_#{Rails.env}"
```

## 실제 사용 예제

### 이메일 발송 Job
```ruby
class SendWelcomeEmailJob < ApplicationJob
  queue_as :mailers

  def perform(user)
    UserMailer.welcome_email(user).deliver_now
  end
end

# 사용자 생성 후 실행
class User < ApplicationRecord
  after_create_commit :send_welcome_email

  private

  def send_welcome_email
    SendWelcomeEmailJob.perform_later(self)
  end
end
```

### 이미지 처리 Job
```ruby
class ProcessImageJob < ApplicationJob
  queue_as :image_processing

  def perform(image_id)
    image = Image.find(image_id)
    # 이미지 리사이징, 썸네일 생성 등
    ImageProcessor.new(image).process
  end
end
```

ActiveJob의 장점은 백엔드 큐 시스템에 상관없이 일관된 API를 제공하고, 재시도, 예외 처리, 스케줄링 등의 기능을 쉽게 사용할 수 있다는 것입니다.