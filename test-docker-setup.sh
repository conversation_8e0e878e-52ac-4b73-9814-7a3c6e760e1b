#!/bin/bash

# Docker Setup Test Script for CodeBase Intelligence System
# Tests all services and their integrations

set -e

echo "🚀 CodeBase Intelligence Docker Setup Test"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if service is healthy
check_service_health() {
    local service_name=$1
    local health_url=$2
    local max_attempts=30
    local attempt=1

    print_status "Checking $service_name health..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            print_status "$service_name is healthy ✅"
            return 0
        fi
        
        echo -n "."
        sleep 2
        ((attempt++))
    done
    
    print_error "$service_name health check failed ❌"
    return 1
}

# Function to test GPU availability
test_gpu() {
    print_status "Testing GPU availability..."
    
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi --query-gpu=name,memory.total --format=csv,noheader
        print_status "GPU detected ✅"
    else
        print_warning "nvidia-smi not found. GPU tests will be skipped ⚠️"
        return 1
    fi
}

# Function to test Docker GPU support
test_docker_gpu() {
    print_status "Testing Docker GPU support..."
    
    if docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi &> /dev/null; then
        print_status "Docker GPU support working ✅"
    else
        print_error "Docker GPU support not working ❌"
        return 1
    fi
}

# Function to test service connectivity
test_service_connectivity() {
    print_status "Testing service connectivity..."
    
    # Test AI Engine to Qdrant
    if docker-compose exec -T aiend curl -f -s http://qdrant:6333/health > /dev/null; then
        print_status "AI Engine → Qdrant connectivity ✅"
    else
        print_error "AI Engine → Qdrant connectivity failed ❌"
    fi
    
    # Test AI Engine to FAISS GPU
    if docker-compose exec -T aiend curl -f -s http://faiss-gpu:8001/health > /dev/null; then
        print_status "AI Engine → FAISS GPU connectivity ✅"
    else
        print_error "AI Engine → FAISS GPU connectivity failed ❌"
    fi
    
    # Test Backend to AI Engine
    if docker-compose exec -T backend curl -f -s http://aiend:8000/health > /dev/null; then
        print_status "Backend → AI Engine connectivity ✅"
    else
        print_error "Backend → AI Engine connectivity failed ❌"
    fi
}

# Function to test API endpoints
test_api_endpoints() {
    print_status "Testing API endpoints..."
    
    # Test AI Engine API
    if curl -f -s http://localhost:7101/health > /dev/null; then
        print_status "AI Engine API accessible ✅"
    else
        print_error "AI Engine API not accessible ❌"
    fi
    
    # Test Backend API
    if curl -f -s http://localhost:7102/health > /dev/null; then
        print_status "Backend API accessible ✅"
    else
        print_error "Backend API not accessible ❌"
    fi
    
    # Test Frontend
    if curl -f -s http://localhost:7103 > /dev/null; then
        print_status "Frontend accessible ✅"
    else
        print_error "Frontend not accessible ❌"
    fi
    
    # Test Qdrant API
    if curl -f -s http://localhost:7105/health > /dev/null; then
        print_status "Qdrant API accessible ✅"
    else
        print_error "Qdrant API not accessible ❌"
    fi
    
    # Test FAISS GPU API
    if curl -f -s http://localhost:7107/health > /dev/null; then
        print_status "FAISS GPU API accessible ✅"
    else
        print_error "FAISS GPU API not accessible ❌"
    fi
}

# Function to test vector operations
test_vector_operations() {
    print_status "Testing vector operations..."
    
    # Test FAISS GPU vector search
    local test_vector='{"vector": [0.1, 0.2, 0.3], "k": 5}'
    if curl -f -s -X POST http://localhost:7107/search \
        -H "Content-Type: application/json" \
        -d "$test_vector" > /dev/null; then
        print_status "FAISS GPU vector search working ✅"
    else
        print_warning "FAISS GPU vector search test failed (expected if no data) ⚠️"
    fi
}

# Main test execution
main() {
    echo
    print_status "Starting Docker setup tests..."
    echo
    
    # Check if Docker Compose is running
    if ! docker-compose ps | grep -q "Up"; then
        print_error "Docker Compose services are not running!"
        print_status "Please run: docker-compose up -d"
        exit 1
    fi
    
    # Test GPU (optional)
    test_gpu || print_warning "GPU tests skipped"
    echo
    
    # Test Docker GPU support (optional)
    test_docker_gpu || print_warning "Docker GPU tests skipped"
    echo
    
    # Wait for services to be ready
    print_status "Waiting for services to be ready..."
    sleep 10
    echo
    
    # Check service health
    check_service_health "AI Engine" "http://localhost:7101/health"
    check_service_health "Backend" "http://localhost:7102/health"
    check_service_health "Qdrant" "http://localhost:7105/health"
    check_service_health "FAISS GPU" "http://localhost:7107/health"
    echo
    
    # Test service connectivity
    test_service_connectivity
    echo
    
    # Test API endpoints
    test_api_endpoints
    echo
    
    # Test vector operations
    test_vector_operations
    echo
    
    print_status "🎉 Docker setup test completed!"
    print_status "All services are running and accessible."
    echo
    print_status "Service URLs:"
    echo "  • Frontend:   http://localhost:7103"
    echo "  • Backend:    http://localhost:7102"
    echo "  • AI Engine:  http://localhost:7101"
    echo "  • Qdrant:     http://localhost:7105"
    echo "  • FAISS GPU:  http://localhost:7107"
    echo "  • Redis:      localhost:7108"
    echo
}

# Run main function
main "$@"
