#!/usr/bin/env python3
"""
Test script to verify FAISS-GPU and Qdrant integration
"""

import requests
import json
import numpy as np
import time

def test_qdrant_connection():
    """Test Qdrant connection"""
    print("Testing Qdrant connection...")
    try:
        response = requests.get("http://localhost:7105/", timeout=5)
        print(f"Qdrant status: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"Qdrant connection failed: {e}")
        return False

def test_faiss_connection():
    """Test FAISS-GPU connection"""
    print("Testing FAISS-GPU connection...")
    try:
        response = requests.get("http://localhost:7107/health", timeout=5)
        print(f"FAISS-GPU status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"FAISS-GPU info: {data}")
        return response.status_code == 200
    except Exception as e:
        print(f"FAISS-GPU connection failed: {e}")
        return False

def create_qdrant_collection():
    """Create a test collection in Qdrant"""
    print("Creating Qdrant collection...")
    try:
        payload = {
            "vectors": {
                "size": 768,
                "distance": "Cosine"
            }
        }
        response = requests.put(
            "http://localhost:7105/collections/test_codebase",
            json=payload,
            timeout=10
        )
        print(f"Collection creation status: {response.status_code}")
        if response.status_code in [200, 409]:  # 409 = already exists
            return True
        return False
    except Exception as e:
        print(f"Collection creation failed: {e}")
        return False

def add_test_vectors():
    """Add test vectors to Qdrant"""
    print("Adding test vectors to Qdrant...")
    try:
        # Generate random test vectors
        vectors = np.random.random((5, 768)).astype(np.float32)
        
        points = []
        for i, vector in enumerate(vectors):
            points.append({
                "id": i + 1,
                "vector": vector.tolist(),
                "payload": {
                    "file_path": f"test_file_{i}.py",
                    "content": f"Test content {i}"
                }
            })
        
        payload = {"points": points}
        response = requests.put(
            "http://localhost:7105/collections/test_codebase/points",
            json=payload,
            timeout=10
        )
        print(f"Vector addition status: {response.status_code}")
        return response.status_code == 200
    except Exception as e:
        print(f"Vector addition failed: {e}")
        return False

def test_faiss_sync():
    """Test FAISS sync with Qdrant"""
    print("Testing FAISS sync...")
    try:
        response = requests.post("http://localhost:7107/sync", timeout=30)
        print(f"FAISS sync status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"FAISS sync result: {data}")
        return response.status_code == 200
    except Exception as e:
        print(f"FAISS sync failed: {e}")
        return False

def test_faiss_search():
    """Test FAISS search"""
    print("Testing FAISS search...")
    try:
        # Generate a random query vector
        query_vector = np.random.random(768).astype(np.float32)
        
        payload = {
            "vector": query_vector.tolist(),
            "k": 3
        }
        response = requests.post(
            "http://localhost:7107/search",
            json=payload,
            timeout=10
        )
        print(f"FAISS search status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"FAISS search result: {data}")
        return response.status_code == 200
    except Exception as e:
        print(f"FAISS search failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=== FAISS-GPU and Qdrant Integration Test ===\n")
    
    # Wait for services to start
    print("Waiting for services to start...")
    time.sleep(5)
    
    tests = [
        ("Qdrant Connection", test_qdrant_connection),
        ("FAISS-GPU Connection", test_faiss_connection),
        ("Create Qdrant Collection", create_qdrant_collection),
        ("Add Test Vectors", add_test_vectors),
        ("FAISS Sync", test_faiss_sync),
        ("FAISS Search", test_faiss_search),
    ]
    
    results = {}
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"Test failed with exception: {e}")
            results[test_name] = False
        
        if results[test_name]:
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n=== Test Summary ===")
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! FAISS-GPU and Qdrant integration is working.")
    else:
        print("⚠️  Some tests failed. Check the logs above for details.")

if __name__ == "__main__":
    main()
