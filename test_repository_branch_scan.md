# Repository Branch ID 스캔 API 테스트 가이드

## 새로 추가된 API 엔드포인트

### 1. Aiend API
```
POST http://localhost:7102/api/v1/webhook/repository-branch-scan?repository_branch_id={repository_branch_id}&force_reindex=true
```

### 2. Backend API
```
POST http://localhost:3000/api/repositories/repository-branch-id-scan
Content-Type: application/json

{
  "repository_branch_id": "123_main",
  "force_reindex": true
}
```

## repository_branch_id 파싱 로직

새로운 `parse_repository_branch_id` 함수는 오른쪽부터 첫 번째 '_' 문자를 찾아서 분리합니다:

### 예시:
- `"123_main"` → repository_id: `"123"`, branch: `"main"`
- `"my_repo_123_feature_branch"` → repository_id: `"my_repo_123"`, branch: `"feature_branch"`
- `"repo_with_underscores_456_dev_test"` → repository_id: `"repo_with_underscores_456"`, branch: `"dev_test"`

## 처리 과정

1. **Frontend에서 repository_branch_id 전송**
   - 예: `"123_main"`

2. **Backend에서 Aiend로 요청 전달**
   ```ruby
   AiendWebhookService.trigger_repository_branch_scan("123_main", force_reindex: true)
   ```

3. **Aiend에서 파싱 및 처리**
   - repository_branch_id 파싱: `"123_main"` → repository_id: `"123"`, branch: `"main"`
   - 저장소 경로 구성: `/app/repos/123`
   - 브랜치 체크아웃: `main`
   - 파일 스캔 및 벡터 임베딩 생성
   - Qdrant collection 생성: `repo_123_branch_main`

## 테스트 방법

### 1. Frontend에서 테스트
1. ReposPage에서 리포지토리와 브랜치 선택
2. "Analyze" 버튼 클릭
3. 내부적으로 `repositoryApi.triggerRepositoryBranchIdScan("123_main", true)` 호출

### 2. curl을 사용한 직접 테스트
```bash
# Backend API 호출
curl -X POST "http://localhost:3000/api/repositories/repository-branch-id-scan" \
  -H "Content-Type: application/json" \
  -d '{"repository_branch_id": "123_main", "force_reindex": true}'

# Aiend API 직접 호출
curl -X POST "http://localhost:7102/api/v1/webhook/repository-branch-scan?repository_branch_id=123_main&force_reindex=true"
```

### 2. 로그 확인
```bash
# Backend 로그
tail -f backend/log/development.log | grep "repository_branch_id"

# Aiend 로그
docker logs aiend_container | grep "repository_branch_id"
```

## 주요 특징

1. **기존 전체 스캔 API는 그대로 유지**
   - `/api/v1/webhook/repository-scan` 엔드포인트는 변경 없음

2. **새로운 브랜치별 스캔 API 추가**
   - `/api/v1/webhook/repository-branch-scan` 엔드포인트 신규 추가

3. **유연한 repository_branch_id 파싱**
   - '_' 문자가 여러 개 있어도 오른쪽부터 파싱하여 정확히 분리

4. **repos 폴더 구조 지원**
   - `/app/repos/{repository_id}` 형태의 폴더 구조 지원

5. **Qdrant collection 명명 규칙**
   - `repo_{repository_id}_branch_{safe_branch}` 형태로 생성

## Frontend 변경사항

### 1. repositoryApi에 새로운 메서드 추가
```typescript
triggerRepositoryBranchIdScan: (repositoryBranchId: string, forceReindex: boolean = false) =>
  apiService.post('/repositories/repository-branch-id-scan', {
    repository_branch_id: repositoryBranchId,
    force_reindex: forceReindex
  })
```

### 2. ReposPage.tsx 수정
- 기존 `aiendService.triggerRepositoryScan()` 대신 `repositoryApi.triggerRepositoryBranchIdScan()` 사용
- repository_branch_id 형태로 구성: `${selectedRepo.id}_${selectedBranch}`
- 더 간단하고 직관적인 API 호출
